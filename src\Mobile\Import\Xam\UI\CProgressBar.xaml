﻿<?xml version="1.0" encoding="UTF-8"?>
<Grid xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xam="clr-namespace:AppoMobi.Xam"
             xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
             HeightRequest="1"
      
            BackgroundColor="{x:Static xam:BackColors.ProgressBack}"

             x:Class="AppoMobi.Xam.CProgressBar">
          
      <BoxView 
          BackgroundColor="Transparent"
          x:Name="cBack"
           VerticalOptions="Fill" HorizontalOptions="Fill"/>

    <svg:GradientBox
        x:Name="cProgress"
        VerticalOptions="Fill"
        HorizontalOptions="Start"
        GradientOrientation="Horizontal"
        StartColor= "{x:Static xam:BackColors.ProgressGradientStart}"
        EndColor="{x:Static xam:BackColors.ProgressGradientEnd}"/>

</Grid>