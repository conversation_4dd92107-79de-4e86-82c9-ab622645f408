﻿using Android.Hardware.Camera2;
using AppoMobi.Forms.Content.Camera.Models;

namespace AppoMobi.Droid.Camera.Views
{
    public partial class CameraFragment
    {
        public class CameraCaptureStillPictureSessionCallback : CameraCaptureSession.CaptureCallback
        {
            private static readonly string TAG = "CameraCaptureStillPictureSessionCallback";

            private readonly CameraFragment owner;



            public CameraCaptureStillPictureSessionCallback(CameraFragment owner)
            {
                if (owner == null)
                    throw new System.ArgumentNullException("owner");

                this.owner = owner;
            }

            public override void OnCaptureCompleted(CameraCaptureSession session, CaptureRequest request, TotalCaptureResult result)
            {

                var SensorExposureTime = result.Get(CaptureResult.SensorExposureTime);
                var ControlAfMode = result.Get(CaptureResult.ControlAfMode);

                var meta = new Metadata()
                {
                    Software = "Art Of Foto",
                    Vendor = $"{Android.OS.Build.Manufacturer}",
                    Model = $"{Android.OS.Build.Model}",
                    Orientation = (int)result.Get(CaptureResult.JpegOrientation),
                    ISO = (int)result.Get(CaptureResult.SensorSensitivity),
                    FocalLength = (float)result.Get(CaptureResult.LensFocalLength)
                };
                owner.Camera.Meta = meta;


                owner.UnlockFocus();
            }
        }
    }
}
