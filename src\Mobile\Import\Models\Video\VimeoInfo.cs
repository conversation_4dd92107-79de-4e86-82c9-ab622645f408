﻿using System;
using System.Collections.Generic;
using System.Globalization;
using Newtonsoft.Json;

namespace ReuseCode.Models.Video
{

    using Newtonsoft.Json.Converters;

    public partial class VimeoInfo
    {
        [JsonProperty("cdn_url")]
        public Uri CdnUrl { get; set; }

        [JsonProperty("vimeo_api_url")]
        public string VimeoApiUrl { get; set; }

        [JsonProperty("request")]
        public Request Request { get; set; }

        [JsonProperty("player_url")]
        public string PlayerUrl { get; set; }

        [JsonProperty("video")]
        public Video Video { get; set; }

        [JsonProperty("user")]
        public User User { get; set; }

        [JsonProperty("embed")]
        public Embed Embed { get; set; }

        [JsonProperty("view")]
        public long View { get; set; }

        [JsonProperty("vimeo_url")]
        public string VimeoUrl { get; set; }
    }

    public partial class Embed
    {
        [JsonProperty("autopause")]
        public long Autopause { get; set; }

        [JsonProperty("playsinline")]
        public long Playsinline { get; set; }

        [JsonProperty("settings")]
        public Dictionary<string, long> Settings { get; set; }

        [JsonProperty("color")]
        public string Color { get; set; }

        [JsonProperty("texttrack")]
        public string Texttrack { get; set; }

        [JsonProperty("on_site")]
        public long OnSite { get; set; }

        [JsonProperty("app_id")]
        public string AppId { get; set; }

        [JsonProperty("muted")]
        public long Muted { get; set; }

        [JsonProperty("dnt")]
        public long Dnt { get; set; }

        [JsonProperty("player_id")]
        public string PlayerId { get; set; }

        [JsonProperty("api")]
        public object Api { get; set; }

        [JsonProperty("editor")]
        public bool Editor { get; set; }

        [JsonProperty("context")]
        public string Context { get; set; }

        [JsonProperty("time")]
        public long Time { get; set; }

        [JsonProperty("outro")]
        public string Outro { get; set; }

        [JsonProperty("log_plays")]
        public long LogPlays { get; set; }

        [JsonProperty("quality")]
        public object Quality { get; set; }

        [JsonProperty("transparent")]
        public long Transparent { get; set; }

        [JsonProperty("loop")]
        public long Loop { get; set; }

        [JsonProperty("autoplay")]
        public long Autoplay { get; set; }
    }

    public partial class Request
    {
        [JsonProperty("files")]
        public Files Files { get; set; }

        [JsonProperty("lang")]
        public string Lang { get; set; }

        [JsonProperty("sentry")]
        public Sentry Sentry { get; set; }

        [JsonProperty("ab_tests")]
        public AbTests AbTests { get; set; }

        [JsonProperty("referrer")]
        public object Referrer { get; set; }

        [JsonProperty("cookie_domain")]
        public string CookieDomain { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("gc_debug")]
        public GcDebug GcDebug { get; set; }

        [JsonProperty("expires")]
        public long Expires { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("session")]
        public string Session { get; set; }

        [JsonProperty("cookie")]
        public Cookie Cookie { get; set; }

        [JsonProperty("build")]
        public Build Build { get; set; }

        [JsonProperty("urls")]
        public Urls Urls { get; set; }

        [JsonProperty("signature")]
        public string Signature { get; set; }

        [JsonProperty("flags")]
        public Flags Flags { get; set; }

        [JsonProperty("country")]
        public string Country { get; set; }
    }

    public partial class AbTests
    {
        [JsonProperty("chromecast")]
        public Chromecast Chromecast { get; set; }

        [JsonProperty("cdn_preference")]
        public CdnPreference CdnPreference { get; set; }
    }

    public partial class CdnPreference
    {
        [JsonProperty("data")]
        public CdnPreferenceData Data { get; set; }

        [JsonProperty("group")]
        public bool Group { get; set; }
    }

    public partial class CdnPreferenceData
    {
        [JsonProperty("city")]
        public string City { get; set; }

        [JsonProperty("country_code")]
        public string CountryCode { get; set; }

        [JsonProperty("hls_pref_found")]
        public bool HlsPrefFound { get; set; }

        [JsonProperty("dash_pref_found")]
        public bool DashPrefFound { get; set; }
    }

    public partial class Chromecast
    {
        [JsonProperty("data")]
        public ChromecastData Data { get; set; }

        [JsonProperty("group")]
        public bool Group { get; set; }
    }

    public partial class ChromecastData
    {
    }

    public partial class Build
    {
        [JsonProperty("backend")]
        public string Backend { get; set; }

        [JsonProperty("js")]
        public string Js { get; set; }
    }

    public partial class Cookie
    {
        [JsonProperty("scaling")]
        public long Scaling { get; set; }

        [JsonProperty("volume")]
        public long Volume { get; set; }

        [JsonProperty("quality")]
        public object Quality { get; set; }

        [JsonProperty("hd")]
        public long Hd { get; set; }

        [JsonProperty("captions")]
        public object Captions { get; set; }
    }

    public partial class Files
    {
        [JsonProperty("dash")]
        public Dash Dash { get; set; }

        [JsonProperty("hls")]
        public Dash Hls { get; set; }

        [JsonProperty("progressive")]
        public Progressive[] Progressive { get; set; }
    }

    public partial class Dash
    {
        [JsonProperty("separate_av")]
        public bool SeparateAv { get; set; }

        [JsonProperty("streams", NullValueHandling = NullValueHandling.Ignore)]
        public Stream[] Streams { get; set; }

        [JsonProperty("cdns")]
        public Cdns Cdns { get; set; }

        [JsonProperty("default_cdn")]
        public string DefaultCdn { get; set; }
    }

    public partial class Cdns
    {
        [JsonProperty("akfire_interconnect_quic")]
        public AkfireInterconnectQuic AkfireInterconnectQuic { get; set; }

        [JsonProperty("fastly_skyfire")]
        public AkfireInterconnectQuic FastlySkyfire { get; set; }
    }

    public partial class AkfireInterconnectQuic
    {
        [JsonProperty("url")]
        public Uri Url { get; set; }

        [JsonProperty("origin")]
        public string Origin { get; set; }
    }

    public partial class Stream
    {
        [JsonProperty("profile")]
        public long Profile { get; set; }

        [JsonProperty("quality")]
        public string Quality { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("fps")]
        public long Fps { get; set; }
    }

    public partial class Progressive
    {
        [JsonProperty("profile")]
        public long Profile { get; set; }

        [JsonProperty("width")]
        public long Width { get; set; }

        [JsonProperty("mime")]
        public string Mime { get; set; }

        [JsonProperty("fps")]
        public long Fps { get; set; }

        [JsonProperty("url")]
        public Uri Url { get; set; }

        [JsonProperty("cdn")]
        public string Cdn { get; set; }

        [JsonProperty("quality")]
        public string Quality { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("origin")]
        public string Origin { get; set; }

        [JsonProperty("height")]
        public long Height { get; set; }
    }

    public partial class Flags
    {
        [JsonProperty("preload_video")]
        public string PreloadVideo { get; set; }

        [JsonProperty("plays")]
        public long Plays { get; set; }

        [JsonProperty("log")]
        public long Log { get; set; }

        [JsonProperty("dnt")]
        public long Dnt { get; set; }

        [JsonProperty("partials")]
        public long Partials { get; set; }

        [JsonProperty("autohide_controls")]
        public long AutohideControls { get; set; }
    }

    public partial class GcDebug
    {
        [JsonProperty("bucket")]
        public string Bucket { get; set; }
    }

    public partial class Sentry
    {
        [JsonProperty("url")]
        public Uri Url { get; set; }

        [JsonProperty("enabled")]
        public bool Enabled { get; set; }

        [JsonProperty("debug_enabled")]
        public bool DebugEnabled { get; set; }

        [JsonProperty("debug_intent")]
        public long DebugIntent { get; set; }
    }

    public partial class Urls
    {
        [JsonProperty("barebone_js")]
        public Uri BareboneJs { get; set; }

        [JsonProperty("zeroclip_swf")]
        public Uri ZeroclipSwf { get; set; }

        [JsonProperty("fresnel")]
        public Uri Fresnel { get; set; }

        [JsonProperty("js")]
        public Uri Js { get; set; }

        [JsonProperty("proxy")]
        public Uri Proxy { get; set; }

        [JsonProperty("chromeless_css")]
        public Uri ChromelessCss { get; set; }

        [JsonProperty("three_js")]
        public Uri ThreeJs { get; set; }

        [JsonProperty("sentry_url")]
        public Uri SentryUrl { get; set; }

        [JsonProperty("mux_url")]
        public Uri MuxUrl { get; set; }

        [JsonProperty("vuid_js")]
        public Uri VuidJs { get; set; }

        [JsonProperty("chromeless_js")]
        public Uri ChromelessJs { get; set; }

        [JsonProperty("zeroclip_js")]
        public Uri ZeroclipJs { get; set; }

        [JsonProperty("css")]
        public Uri Css { get; set; }
    }

    public partial class User
    {
        [JsonProperty("team_origin_user_id")]
        public long TeamOriginUserId { get; set; }

        [JsonProperty("liked")]
        public long Liked { get; set; }

        [JsonProperty("account_type")]
        public string AccountType { get; set; }

        [JsonProperty("vimeo_api_client_token")]
        public object VimeoApiClientToken { get; set; }

        [JsonProperty("vimeo_api_interaction_tokens")]
        public object VimeoApiInteractionTokens { get; set; }

        [JsonProperty("team_id")]
        public long TeamId { get; set; }

        [JsonProperty("watch_later")]
        public long WatchLater { get; set; }

        [JsonProperty("owner")]
        public long Owner { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("mod")]
        public long Mod { get; set; }

        [JsonProperty("logged_in")]
        public long LoggedIn { get; set; }
    }

    public partial class Video
    {
        [JsonProperty("version")]
        public Version Version { get; set; }

        [JsonProperty("height")]
        public long Height { get; set; }

        [JsonProperty("duration")]
        public long Duration { get; set; }

        [JsonProperty("thumbs")]
        public Thumbs Thumbs { get; set; }

        [JsonProperty("owner")]
        public Owner Owner { get; set; }

        [JsonProperty("file_codecs")]
        public FileCodecs FileCodecs { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("embed_code")]
        public string EmbedCode { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("share_url")]
        public Uri ShareUrl { get; set; }

        [JsonProperty("width")]
        public long Width { get; set; }

        [JsonProperty("embed_permission")]
        public string EmbedPermission { get; set; }

        [JsonProperty("fps")]
        public long Fps { get; set; }

        [JsonProperty("spatial")]
        public long Spatial { get; set; }

        [JsonProperty("live_event")]
        public object LiveEvent { get; set; }

        [JsonProperty("allow_hd")]
        public long AllowHd { get; set; }

        [JsonProperty("hd")]
        public long Hd { get; set; }

        [JsonProperty("lang")]
        public object Lang { get; set; }

        [JsonProperty("default_to_hd")]
        public long DefaultToHd { get; set; }

        [JsonProperty("url")]
        public Uri Url { get; set; }

        [JsonProperty("privacy")]
        public string Privacy { get; set; }

        [JsonProperty("bypass_token")]
        public string BypassToken { get; set; }

        [JsonProperty("unlisted_hash")]
        public object UnlistedHash { get; set; }
    }

    public partial class FileCodecs
    {
        [JsonProperty("hevc")]
        public Hevc Hevc { get; set; }

        [JsonProperty("av1")]
        public object[] Av1 { get; set; }

        [JsonProperty("avc")]
        public long[] Avc { get; set; }
    }

    public partial class Hevc
    {
        [JsonProperty("hdr")]
        public object[] Hdr { get; set; }

        [JsonProperty("sdr")]
        public object[] Sdr { get; set; }
    }

    public partial class Owner
    {
        [JsonProperty("account_type")]
        public string AccountType { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("img")]
        public Uri Img { get; set; }

        [JsonProperty("url")]
        public Uri Url { get; set; }

        [JsonProperty("img_2x")]
        public Uri Img2X { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }
    }

    public partial class Thumbs
    {
        [JsonProperty("640")]
        public Uri The640 { get; set; }

        [JsonProperty("960")]
        public Uri The960 { get; set; }

        [JsonProperty("1280")]
        public Uri The1280 { get; set; }

        [JsonProperty("base")]
        public Uri Base { get; set; }
    }

    public partial class Version
    {
        [JsonProperty("current")]
        public object Current { get; set; }

        [JsonProperty("available")]
        public object Available { get; set; }
    }

    public partial class VimeoInfo
    {
        public static VimeoInfo FromJson(string json) => JsonConvert.DeserializeObject<VimeoInfo>(json,  Converter.Settings);
    }

    public static class Serialize
    {
        public static string ToJson(this VimeoInfo self) => JsonConvert.SerializeObject(self,  Converter.Settings);
    }

    internal static class Converter
    {
        public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
        {
            MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
            DateParseHandling = DateParseHandling.None,
            Converters =
            {
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
        };
    }
}
