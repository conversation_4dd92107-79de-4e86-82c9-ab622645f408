﻿<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    x:Class="AppoMobi.UI.StatusBar"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HeightRequest="20"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="Start">

    <svg:GradientBox
        x:Name="ControlGradient"
        EndColor="{x:Static xam:BackColors.GradientEndNav}"
        GradientOrientation="Horizontal"
        HorizontalOptions="Fill"
        IsVisible="False"
        StartColor="{x:Static xam:BackColors.GradientStartNav}"
        VerticalOptions="Fill" />

</Grid>