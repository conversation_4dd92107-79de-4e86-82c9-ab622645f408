﻿using System;
using System.Net;
using System.Net.Http;

namespace AppoMobi
{
    //*****************************************************************
    public class ApiException : Exception
    //*****************************************************************
    {
        public ApiException(string message) : base(message)
        {

        }

        public ApiException(string message, HttpMethod method, string url, HttpStatusCode statusCode) : base(message)
        {
            Method = method;
            Url = url;
            StatusCode = statusCode;
        }

        public HttpStatusCode StatusCode { get; set; }
        public string Url { get; set; }
        public HttpMethod Method { get; set; }
    }


    //*****************************************************************
    public class ApiResponse
    //*****************************************************************
    {
        public HttpStatusCode StatusCode { get; set; }
        public string Url { get; set; }
        public string Content { get; set; }
        public HttpMethod Method { get; set; }        
    }

}
