﻿using System;
using System.Reflection;
using AppoMobi.Xam;
using DrawnUi.Draw;
using SkiaSharp;
using SkiaSharp.Views.Maui;



namespace AppoMobi.Forms.Controls.Svg
{

    public enum GradientDirection
    {
        Vertical,
        Horizontal
    }

 

    /// <summary>
    /// More information: https://docs.microsoft.com/en-us/xamarin/xamarin-forms/user-interface/graphics/skiasharp/curves/clipping
    /// TODO: Include Source and Path BindableProperties.
    /// </summary>
    public class GradientBox : SKCanvasView
    {
        protected override void OnSizeAllocated(double width, double height)
        {
            base.OnSizeAllocated(width, height);

            InvalidateSurface();
        }

        //SKBitmap _bitmap;

       

        private static void RedrawCanvas(BindableObject bindable, object oldVal, object newVal)
        {
            var GradientBox = bindable as GradientBox;
            GradientBox?.InvalidateSurface();
        }

   
      //  SKPath Path = SKPath.ParseSvgPathData("M 2.6814159,2.2123932 377.68142,0.99328319 v 0 L 3.318584,38.230093 Z");
        //transform="translate(-6,-101.28319)"

        
        protected override void OnPaintSurface(SKPaintSurfaceEventArgs args)
        {
            SKImageInfo info = args.Info;
            SKSurface surface = args.Surface;
            SKCanvas canvas = surface.Canvas;

            canvas.Clear();


            float scale = (float)(args.Info.Width / this.Width);

            /*
            // Set transform to center and enlarge clip path to window height
            SKRect bounds;
            Path.GetTightBounds(out bounds);
            canvas.Translate(info.Width / 2.0, info.Height / 2);
            canvas.Scale(0.98f * info.Height / bounds.Height);
            canvas.Translate(-bounds.MidX, -bounds.MidY);
            // Set the clip path
            canvas.ClipPath(Path, SKClipOperation.Intersect, true);
            // Reset transforms
            canvas.ResetMatrix();
            */


            //   const string TEXT = "РАЗРАБОТКА";

            float size = Math.Min(info.Width, info.Height);
            float radius = 0.4f * size;
            float offset = size / 2 - radius;

            using (SKPath path = new SKPath())
            {
                var shadow = 8+2;

                if (IsClipped)
                {
                    // Define the first contour
                    path.MoveTo(0.0f, 0.0f);
                    path.LineTo(info.Width, 0.0f);
                    path.LineTo(info.Width, 0.60f * info.Height );
                    path.LineTo(0.0f, 1.0f * info.Height- shadow);
                    path.LineTo(0.0f, 0.0f);
                    path.Close();
                    //SKRect bounds;
                    //path.GetTightBounds(out bounds);
                    //canvas.ClipPath(path, SKClipOperation.Intersect, true);
                }
                else
                if (CornerRadius>0)
                {
                    path.AddRoundRect(info.Rect, CornerRadius * scale, CornerRadius * scale);
                }

                if (StyleId == "ControlPickerFilm")
                {
                    var stop = 1;
                }

                using (SKPaint paint = new SKPaint())
                {
                    paint.IsAntialias=true;

                    if (HasShadow)
                    {
                        //paint.ImageFilter = SKImageFilter.CreateDropShadow(0, 2, 0, 8, 
                        //    Color.Parse("#bbbbcc").ToSKColor(), 
                        //    SKDropShadowImageFilterShadowMode.DrawShadowAndForeground);
                    }

                    var startColor = StartColor;
                    var endColor = EndColor;

                    if (Light < 1.0)
                    {
                        startColor = startColor.MakeDarker(100-Light*100);
                        endColor = endColor.MakeDarker(100-Light * 100);
                    }
                    else
                    if (Light > 1.0)
                    {
                        startColor = startColor.MakeLighter(Light * 100-100);
                        endColor = endColor.MakeLighter(Light * 100-100);
                    }

                    if (this.GradientOrientation == GradientDirection.Horizontal)
                    {
                        StartXRatio = 0;
                        StartYRatio = 0.5f;
                        EndXRatio = 1f;
                        EndYRatio = 0.5f;
                    }
                    else
                    {
                        StartXRatio = 0.5f;
                        StartYRatio = 0f;
                        EndXRatio = 0.5f;
                        EndYRatio = 1f;
                    }

                    // Create gradient for background
                    paint.Shader = SKShader.CreateLinearGradient(
                        new SKPoint(info.Width*StartXRatio, info.Height*StartYRatio),
                        new SKPoint(info.Width*EndXRatio, info.Height * EndYRatio),
                        new SKColor[]
                        {
                            startColor.ToSKColor(),
                            endColor.ToSKColor()
                        },
                        null,
                        SKShaderTileMode.Clamp);

 // Draw background

                    if (IsClipped || CornerRadius>0)
                        canvas.DrawPath(path, paint); 
                    else
                        canvas.DrawRect(info.Rect, paint);

                    paint.ImageFilter = null; //shadow off

                    if (IsClipped)
                    {
                        SKRect bounds;
                        path.GetTightBounds(out bounds);
                        canvas.ClipPath(path, SKClipOperation.Intersect, true);
                    }

                    
                }
            }

        }

        protected float StartXRatio { get; set; }
        protected float StartYRatio { get; set; }
        protected float EndXRatio { get; set; }
        protected float EndYRatio { get; set; }

        #region Properties


        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
            nameof(CornerRadius),
            typeof(float),
            typeof(GradientBox),
            0f);

        public float CornerRadius
        {
            get { return (float)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }

        //-------------------------------------------------------------
        // HasShadow
        //-------------------------------------------------------------
        private const string nameHasShadow = "HasShadow";
        public static readonly BindableProperty HasShadowProperty = BindableProperty.Create(nameHasShadow, typeof(bool), typeof(GradientBox), 
            false,
            propertyChanged: RedrawCanvas);

        public bool HasShadow
        {
            get { return (bool)GetValue(HasShadowProperty); }
            set { SetValue(HasShadowProperty, value); }
        }	

        //-------------------------------------------------------------
        // IsClipped
        //-------------------------------------------------------------
        private const string nameIsClipped = "IsClipped";
        public static readonly BindableProperty IsClippedProperty = BindableProperty.Create(nameIsClipped, typeof(bool), typeof(GradientBox)
            , false,
            propertyChanged: RedrawCanvas); //, BindingMode.TwoWay); //, BindingMode.TwoWay
        public bool IsClipped
        {
            get { return (bool)GetValue(IsClippedProperty); }
            set { SetValue(IsClippedProperty, value); }
        }


        public static readonly BindableProperty GradientOrientationProperty = BindableProperty.Create(
            nameof(GradientOrientation),
            typeof(GradientDirection),
            typeof(GradientBox),
            GradientDirection.Vertical, propertyChanged: RedrawCanvas);

        public GradientDirection GradientOrientation
        {
            get { return (GradientDirection)GetValue(GradientOrientationProperty); }
            set { SetValue(GradientOrientationProperty, value); }
        }
        

        //-------------------------------------------------------------
        // Source
        //-------------------------------------------------------------
        private const string nameSource = "Source";

        public static readonly BindableProperty SourceProperty = BindableProperty.Create(nameSource, typeof(string),
            typeof(GradientBox), string.Empty, propertyChanged: RedrawCanvas); //, BindingMode.TwoWay

        public string Source
        {
            get { return (string)GetValue(SourceProperty); }
            set { SetValue(SourceProperty, value); }
        }


        //-------------------------------------------------------------
        // Light
        //-------------------------------------------------------------
        private const string nameLight = "Light";
        public static readonly BindableProperty LightProperty = BindableProperty.Create(nameLight, typeof(double), typeof(GradientBox), 1.0,
            propertyChanged: RedrawCanvas); 
        public double Light
        {
            get { return (double)GetValue(LightProperty); }
            set { SetValue(LightProperty, value); }
        }


        //-------------------------------------------------------------
        // StartColor
        //-------------------------------------------------------------
        private const string nameStartColor = "StartColor";
        public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(GradientBox),
            Colors.DarkGray,
            propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
        public Color StartColor
        {
            get { return (Color)GetValue(StartColorProperty); }
            set { SetValue(StartColorProperty, value); }
        }
        
        //-------------------------------------------------------------
        // EndColor
        //-------------------------------------------------------------
        private const string nameEndColor = "EndColor";
        public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(GradientBox),
            Colors.Gray,
            propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
        public Color EndColor
        {
            get { return (Color)GetValue(EndColorProperty); }
            set { SetValue(EndColorProperty, value); }
        }

    #endregion

    }

}