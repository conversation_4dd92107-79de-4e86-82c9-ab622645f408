﻿using AppoMobi.Framework.Attributes;
using AppoMobi.Framework.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using Editor = AppoMobi.Framework.Attributes.Editor;

namespace AppoMobi.Framework.Extensions
{
    
    public static partial class AttributesExtensions
    
    {

        //-
        public static bool IsImage(this PropertyInfo property)
        //-
        {
            return (Attribute.IsDefined(property, typeof(IsImage)));
        }

        //-
        public static bool HasImageEditor(this PropertyInfo property)
        //-
        {
            return (bool)property.GetAttribute<IsImage>()?.Editor;
        }

        //-
        public static bool IsRandomColors(this PropertyInfo property)
        //-
        {
            return (Attribute.IsDefined(property, typeof(RandomColors)));
        }

        //-
        public static bool IsColor(this PropertyInfo property)
        //-
        {
            return (Attribute.IsDefined(property, typeof(IsColor)));
        }

        //-
        public static bool? GetImageIfNeedUploadId(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<Export>()?.NeedUploadId;
        }

        //-
        public static string GetImageRole(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<IsImage>()?.Role;
        }
        //-
        public static string GetImageName(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<IsImage>()?.Name;
        }
        //-
        public static string GetExportName(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<Export>()?.Name;
        }
        //-
        public static bool NeedUploadId(this PropertyInfo property)
        //-
        {
            var t = property.GetAttribute<Export>()?.NeedUploadId;
            if (t == null) return false;
            return (bool)t;
        }


        public static string GetClassExportKey<T>()

        {
            var dnAttribute = typeof(T).GetCustomAttributes(
                typeof(Export), true
            ).FirstOrDefault() as Export;
            if (dnAttribute != null)
            {
                return dnAttribute.Key;
            }
            return null;
        }


        //-
        public static string GetClassExportKey(this Type classType)
        //-
        {
            if (classType.GetCustomAttributes(
                typeof(Export), true
            ).FirstOrDefault() is Export dnAttribute)
            {
                return dnAttribute.Key;
            }
            return null;
        }


        //-
        public static Type GetChildExportType(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<Export>()?.ChildExportType;
        }
        //-
        public static Type GetChildContextType(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<Export>()?.ChildContextType;
        }
        //-
        public static string GetChildController(this PropertyInfo property)
        //-
        {
            return property.GetAttribute<Export>()?.ChildController;
        }
        //-
        public static string GetDropdownType(this PropertyInfo property)
        //-
        {
            var res = "";
            var attr = property.GetAttribute<Dropdown>();
            if (attr != null)
            {
                var getVal = attr.DataType;
                if (getVal != null)
                    res = (string)getVal;
            }
            return res;
        }
        //-
        public static string GetForeignKeyProperty(this PropertyInfo property)
        //-
        {
            string res = null;
            var attr = property.GetAttribute<ForeignKeyProperty>();
            if (attr != null)
            {
                var getVal = attr.PropertyName;
                if (getVal != null)
                    res = (string)getVal;
            }
            return res;
        }
        //-
        public static string GetForModules(this PropertyInfo property)
        //-
        {
            var res = "";
            var attr = property.GetAttribute<ForModules>();
            if (attr != null)
            {
                var getVal = attr.Modules;
                if (getVal != null)
                    res = (string)getVal;
            }
            return res;
        }
        //-

        /// <summary>
        /// "Use bool CheckIsReadOnly instead"
        /// </summary>
        /// <param name="property"></param>
        /// <returns></returns>
        [Obsolete]
        public static string GetIsReadOnly(this PropertyInfo property)
        //-
        {
            string res = null;
            var attr = property.GetAttribute<ReadOnly>();
            if (attr != null)
            {
                var getVal = attr.ExcludeForRoles;
                if (getVal != null)
                    res = (string)getVal;
            }
            return res;
        }

        /// <summary>
        /// Checking ReadOnly custom attribute
        /// </summary>
        /// <param name="propertyInfo"></param>
        /// <param name="existing">If the model is new, will check ExcludeForNew</param>
        /// <param name="userRoles">user roles, can be null, roles will be checked vs ExcludeForRoles</param>
        /// <returns></returns>
        public static bool IsReadOnly(this PropertyInfo propertyInfo, bool existing, IEnumerable<string> userRoles)
        {
            var attr = propertyInfo.GetAttribute<ReadOnly>();
            if (attr != null)
            {
                //readonly
                //exclude if new?
                if (attr.ExcludeForNew && !existing)
                    return false;

                if (userRoles == null || string.IsNullOrEmpty(attr.ExcludeForRoles))
                    return true;

                foreach (var excludeRole in attr.ExcludeForRoles.TagsToList())
                {
                    if (userRoles.Contains(excludeRole))
                        return false;
                }

                return true;
            }
            return false;
        }

        //-
        public static string GetRoles(this PropertyInfo property)
        //-
        {
            var res = "";
            var attr = property.GetAttribute<Editor>()?.Roles;
            if (attr.HasContent())
            {
                res = attr;
            }
            return res;
        }
        //-
        public static bool NeedsSetId(this PropertyInfo property)
        //-
        {
            var res = false;
            var attr = property.GetAttribute<Editor>()?.SetId;
            if (attr != null)
            {
                res = attr.Value;
            }
            return res;
        }
        //-
        public static bool HasEditorLineAfter(this PropertyInfo property)
        //-
        {
            var res = false;
            var attr = property.GetAttribute<Editor>()?.LineAfter;
            if (attr != null)
            {
                res = attr.Value;
            }
            return res;
        }
        //-
        public static bool HasEditorLineBefore(this PropertyInfo property)
        //-
        {
            var res = false;
            var attr = property.GetAttribute<Editor>()?.LineBefore;
            if (attr != null)
            {
                res = attr.Value;
            }
            return res;
        }
        //-
        public static string EditorLink(this PropertyInfo property) //-
        {
            var attr = property.GetAttribute<Editor>()?.Link;
            return attr;
        }
        //-
        public static Type MultiSelectSource(this PropertyInfo property)
        //-
        {
            Type res = null;
            var attr = property.GetAttribute<MultiSelect>();
            if (attr != null)
            {
                var getVal = attr.Source;
                if (getVal != null)
                    res = (Type)getVal;
            }
            return res;
        }
        //-
        public static bool MultiSelectIsSystemDb(this PropertyInfo property)
        //-
        {
            bool res = false;
            var attr = property.GetAttribute<MultiSelect>();
            if (attr != null)
            {
                res = attr.IsSystemDb;
            }
            return res;
        }

    }



}