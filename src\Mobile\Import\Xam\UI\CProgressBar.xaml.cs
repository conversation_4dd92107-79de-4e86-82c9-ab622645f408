﻿using System.Threading.Tasks;



namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class CProgressBar
	{
        
		public CProgressBar ()
        
		{
			InitializeComponent ();
		}

        
	    private void SetProgress(double progress)
        
	    {
	        var maxWidth = cBack.Width;
	        var progressWidth = maxWidth * progress;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                //cProgress.BackgroundColor=Colors.Red;
                cProgress.WidthRequest = progressWidth;
                //cProgress.ForceLayout();
                await Task.Delay(10);
            });
	    }

        // DO NOT CLOSE
        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {

                //property changed
	            case nameProgress:
	                SetProgress(Progress);
	                break;
            }

        }

        //-------------------------------------------------------------
        // Progress
        //-------------------------------------------------------------
        private const string nameProgress = "Progress";
        public static readonly BindableProperty ProgressProperty = BindableProperty.Create(nameProgress, typeof(double), typeof(CProgressBar), 0.0); //, BindingMode.TwoWay
        public double Progress
        {
            get { return (double)GetValue(ProgressProperty); }
            set { SetValue(ProgressProperty, value); }
        }	


        private double _ScreenWidth;
	    public double ScreenWidth
	    {
	        get
	        {
	            return Width;
	            //return _ScreenWidth;
	        }
	        set
	        {
	            if (_ScreenWidth != value)
	            {
	                _ScreenWidth = value;
	                OnPropertyChanged();
	            }
	        }
	    }
    }
}
