using Android.Views;
using AppoMobi.Touch;
using System;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidRotateEventArgs : RotateEventArgs
	{
		public AndroidRotateEventArgs(MotionEvent current, RotateEventArgs previous, Android.Views.View view)
		{
			this.Cancelled = current.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(current, 2, previous);
			base.CalculateAngles(previous);
		}
	}
}