﻿

namespace AppoMobi.Xam
{
    
    public class ExtLabel : Label
    
    {


        //-------------------------------------------------------------
        // LetterSpacing
        //-------------------------------------------------------------
        private const string nameLetterSpacing = "LetterSpacing";
        public static readonly BindableProperty LetterSpacingProperty = BindableProperty.Create(nameLetterSpacing, typeof(float), typeof(ExtLabel), 0.00f); //, BindingMode.TwoWay
        public float LetterSpacing
        {
            get { return (float)GetValue(LetterSpacingProperty); }
            set { SetValue(LetterSpacingProperty, value); }
        }

        //-------------------------------------------------------------
        // LineSpacing
        //-------------------------------------------------------------
        private const string nameLineSpacing = "LineSpacing";
        public static readonly BindableProperty LineSpacingProperty = BindableProperty.Create(nameLineSpacing, typeof(float), typeof(ExtLabel), 1.00f); //, BindingMode.TwoWay
        public float LineSpacing
        {
            get { return (float)GetValue(LineSpacingProperty); }
            set { SetValue(LineSpacingProperty, value); }
        }



    }
}
