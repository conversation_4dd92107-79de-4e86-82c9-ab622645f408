﻿using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace AppoMobi.Framework.Api
{
    /// <summary>
    /// Output from API
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ApiResultListPaged<T>
    {
        public ApiResultListPaged()
        {
           // FilteredItems = Items.AsQueryable();
        }

        public IEnumerable<T> Items { get; set; } = new List<T>();
        public int Count { get; set; }
        public int Total { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public string Filter { get; set; }
        public string Search { get; set; }
        public string Order { get; set; }
        public bool FullDto { get; set; }

        /// <summary>
        /// Used to pass any additional info regarding the request
        /// </summary>
        public object Info { get; set; }

        //[JsonIgnore]
        //public IQueryable<T> FilteredItems { get; set; }

        [JsonIgnore]
        public int TotalPages
        {
            get
            {
                if (PageSize == 0)
                    return 0;
                var maybe = Total / PageSize;
                
                if (maybe * PageSize < Total)
                    maybe++;

                return maybe;
            }
        }

    }
}
