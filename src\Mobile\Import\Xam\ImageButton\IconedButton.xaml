﻿<?xml version="1.0" encoding="UTF-8" ?>
<Frame
    x:Class="AppoMobi.Xam.IconedButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Padding="0"
    BackgroundColor="Transparent"
    BorderColor="Transparent"
    CornerRadius="5"
    HasShadow="False"
    IsClippedToBounds="True"
    MinimumHeightRequest="40"
    VerticalOptions="StartAndExpand">

    <gestures:LegacyGesturesGrid
        x:Name="ControlLayout"
        Margin="0,4,0,4"
        Padding="0"
        ColumnSpacing="8"
        Down="NiftyImageButton_OnDown"
        HorizontalOptions="FillAndExpand"
        Tapped="OnTapped_Frame"
        VerticalOptions="FillAndExpand">

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!--  left aligned image  -->
        <xam:FontIconLabel
            x:Name="ControlImageLeft"
            Grid.Column="0"
            Margin="0,0,1,0"
            FontSize="15"
            HorizontalOptions="End"
            Tag="IconedButtonLeft"
            TextColor="{x:Static xam:TextColors.BtnArrow}"
            VerticalOptions="Center" />


        <!--  centered stack  -->
        <StackLayout
            x:Name="stackCenter"
            Grid.Column="1"
            HorizontalOptions="Center"
            Orientation="Horizontal"
            Spacing="4"
            VerticalOptions="Center">

            <!--  left accessory image  -->
            <forms:CachedImage
                x:Name="ControlImageLeftAccessory"
                Aspect="AspectFit"
                Error="OnImageErrors"
                FadeAnimationEnabled="False"
                IsVisible="False"
                VerticalOptions="Center" />
            <Label
                x:Name="ControlLabel"
                Margin="2,2,2,2"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center" />
            <!--  right accessory image  -->
            <forms:CachedImage
                x:Name="ControlImageRightAccessory"
                Aspect="AspectFit"
                Error="OnImageErrors"
                FadeAnimationEnabled="False"
                IsVisible="False"
                VerticalOptions="Center" />
        </StackLayout>

        <!--  right aligned image  -->

        <xam:FontIconLabel
            x:Name="ControlImageRight"
            Grid.Column="0"
            Margin="0,0,1,0"
            FontSize="15"
            HorizontalOptions="Start"
            IconName="fa_chevron_right"
            IsVisible="False"
            TextColor="{x:Static xam:TextColors.BtnArrow}"
            VerticalOptions="Center" />


    </gestures:LegacyGesturesGrid>


</Frame>