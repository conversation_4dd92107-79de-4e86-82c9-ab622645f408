﻿using System;
using AppoMobi.Xam;

using NiftyImageButton = AppoMobi.Xam.NiftyImageButton;

namespace AppoMobi.Controls
{

    //****************************************************
    public class CButton: NiftyImageButton
    //****************************************************
    {
        private static ImageSource imgFx { get; set; }

        
        public CButton()
        
        {
            Margin = new Thickness(0,0,0,4);
            BackgroundColor = BackColors.Button;//Colors.Transparent;//Color.Parse(TenantOptions.ColorButtonsBack);//Color.Parse("#65c9ea").MakeDarker(5);//
            BorderColor = BackColors.Outline; //Colors.Black.MultiplyAlpha(0.33f);


            FontSize = 12.5;
            HeightRequest = 30;            
            WidthRequest = 180;
            HorizontalOptions = LayoutOptions.Center;
            ImageHeightRequest = 22;
            ImagePosition = LayoutPosition.Right;
          
            ImageWidthRequest = 23;
            TextColor = TextColors.ButtonText;// Color.Parse(TenantOptions.ColorButtonsTxt);
            ImageTintColor = TextColor;
            //BorderColor = AppColors.PrimaryDark;

            if (imgFx == null)
            {
                var generatedFilename = Core.AssemblyName + ".Images.UI." + "fxbtn.png";
                imgFx = ImageSource.FromResource(generatedFilename);
            }
            //FxSource = null;
            FxSource = imgFx;
        }

        
        private void OnAppearing(object sender, EventArgs e)
        
        {
            Refresh();
        }
    }

    public class LargeButton : NiftyImageButton
    {
        public LargeButton()
        {
            WidthRequest = 200;
            TextColor = Colors.White;
            BackgroundColor = Color.Parse("#589442");
            Margin = new Thickness(50,0,50,0);
            FontAttributes = FontAttributes.Bold;
            FontSize = 14;
            HeightRequest = 40;
            HorizontalOptions = LayoutOptions.Center;
        }
    }


}
