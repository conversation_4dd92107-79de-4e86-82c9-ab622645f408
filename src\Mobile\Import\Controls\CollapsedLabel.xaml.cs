﻿using System;
using AppoMobi.Forms.Common.ResX;

using AppoMobi.Xam;



namespace AppoMobi.UI
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class CollapsedLabel 
	{
		public CollapsedLabel ()
		{
			InitializeComponent ();
		    Collapsed = true;
		}


        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);


            switch (propertyName)
            {

                //property changed
                case nameCutSize:
                    UpdateText();
                    break;

                //property changed
                case nameText:
                    UpdateText();
                    break;
             
                case nameTextColor:
                    MainLabel.TextColor = TextColor;
                    break;

                case nameCollapsed:
                    UpdateText();
                    break;

            }

        }

        
	    protected void UpdateText()
        
	    {
	        if (Text == null) return;
	        if (!Collapsed)
	        {
	            MainLabel.Text = Text;
	            cButton.Text =ResStrings.Collapse;
	            cButton.IsVisible = true;
            }
            else
	        {
	            cButton.Text = ResStrings.Expand;
	            var cutText = Text.Left(CutSize);
	            if (Text.Length <= CutSize)
	            {
	                cButton.IsVisible = false;
                }
                else
	            {
	                //cut
	                cutText += "..  ";
	                cButton.IsVisible = true;
	            }
	            MainLabel.Text = cutText;
            }
        }

        #region Properties


        //-------------------------------------------------------------
        // Collapsed
        //-------------------------------------------------------------
        private const string nameCollapsed = "Collapsed";
        public static readonly BindableProperty CollapsedProperty = BindableProperty.Create(nameCollapsed, typeof(bool), typeof(CollapsedLabel), false); //, BindingMode.TwoWay
        public bool Collapsed
        {
            get { return (bool)GetValue(CollapsedProperty); }
            set { SetValue(CollapsedProperty, value); }
        }	



        //-------------------------------------------------------------
        // TextColor
        //-------------------------------------------------------------
        private const string nameTextColor = "TextColor";
        public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameTextColor, typeof(Color), typeof(CollapsedLabel), TextColors.Standart); //, BindingMode.TwoWay
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }

	    //-------------------------------------------------------------
	    // ActionColor
	    //-------------------------------------------------------------
	    private const string nameActionColor = "ActionColor";
	    public static readonly BindableProperty ActionColorProperty = BindableProperty.Create(nameActionColor, typeof(Color), typeof(CollapsedLabel), TextColors.Standart); //, BindingMode.TwoWay
	    public Color ActionColor
	    {
	        get { return (Color)GetValue(ActionColorProperty); }
	        set { SetValue(ActionColorProperty, value); }
	    }

        //-------------------------------------------------------------
        // Text
        //-------------------------------------------------------------
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(CollapsedLabel), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }


        //-------------------------------------------------------------
        // CutSize
        //-------------------------------------------------------------
        private const string nameCutSize = "CutSize";
        public static readonly BindableProperty CutSizeProperty = BindableProperty.Create(nameCutSize, typeof(int), typeof(CollapsedLabel), 100); //, BindingMode.TwoWay
        public int CutSize
        {
            get { return (int)GetValue(CutSizeProperty); }
            set { SetValue(CutSizeProperty, value); }
        }	

        #endregion

        
	    private void OnButton(object sender, EventArgs e)
        
	    {
	        Collapsed = !Collapsed;
	    }
	}



}