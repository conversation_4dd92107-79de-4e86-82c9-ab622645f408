﻿ 

namespace AppoMobi.Extensions
{
    public static class TimeSpanExtensions
    {



        public static string ToShortFormNoDays(this TimeSpan t, bool useDays=true)
        {
            string shortForm = "";

            bool empty = true;

            var addHours = 0;
            if (t.Days > 0)
            {
                if (useDays)
                {
                    shortForm += $"{t.Days}{ResStrings.TimeCalculator_Day}";
                    empty = false;
                }
                else
                {
                    addHours = t.Days * 24;
                }
            }
            var totalHours = t.Hours + addHours;

            if (totalHours > 0)
            {
                shortForm += $" {totalHours}{ResStrings.TimeCalculator_Hour}";
                empty = false;
            }
          
            if (t.Minutes > 0)
            {
                shortForm += $" {t.Minutes}{ResStrings.TimeCalculator_Min}";
                empty = false;
            }

            if (t.Seconds > 0)
            {
                shortForm += $" {t.Seconds}{ResStrings.TimeCalculator_Sec}";
                empty = false;
            }

            if (empty)
            {
                shortForm  = $" {t.TotalSeconds}{ResStrings.TimeCalculator_Sec}";
            }
        
            return shortForm.Trim();
        }


        public static string ToShortFormNoDaysFractions(this TimeSpan t, bool useDays = true, int hideFractionsBelowSecs = int.MaxValue)
        {
            string shortForm = "";

            bool empty = true;

            var addHours = 0;
            if (t.Days > 0)
            {
                if (useDays)
                {
                    shortForm += $"{t.Days}{ResStrings.TimeCalculator_Day}";
                    empty = false;
                }
                else
                {
                    addHours = t.Days * 24;
                }
            }
            var totalHours = t.Hours + addHours;

            if (totalHours > 0)
            {
                shortForm += $" {totalHours}{ResStrings.TimeCalculator_Hour}";
                empty = false;
            }

            if (t.Minutes > 0)
            {
                shortForm += $" {t.Minutes}{ResStrings.TimeCalculator_Min}";
                empty = false;
            }

            double millisecs = t.Milliseconds / 1000.0;
            if (t.Seconds > 0)
            {
   
                if (t.Seconds < hideFractionsBelowSecs)
                    //                    shortForm += $" {t.Seconds}{ResStrings.TimeCalculator_Sec} {millisecs.ToUnicodeFractions3(false)}";
                    shortForm = $" {millisecs}{ResStrings.TimeCalculator_Sec}";

                else
                    shortForm += $" {t.Seconds}{ResStrings.TimeCalculator_Sec}";

                empty = false;
            }

            if (empty)
            {
                //todo use photo fractions up to 1/8000
                //shortForm = $" {millisecs.ToUnicodeFractions3(true)}{ResStrings.TimeCalculator_Sec}"; //
                shortForm = $" {millisecs}{ResStrings.TimeCalculator_Sec}";
            }

            return shortForm.Trim();
        }



        public static string ToShortPhoto(this TimeSpan t, string secondsStep="", bool useDays = true, int hideFractionsBelowSecs = int.MaxValue)
        {
            string shortForm = "";

            bool empty = true;

            var addHours = 0;
            if (t.Days > 0)
            {
                if (useDays)
                {
                    shortForm += $"{t.Days}{ResStrings.TimeCalculator_Day}";
                    empty = false;
                }
                else
                {
                    addHours = t.Days * 24;
                }
            }
            var totalHours = t.Hours + addHours;

            if (totalHours > 0)
            {
                shortForm += $" {totalHours}{ResStrings.TimeCalculator_Hour}";
                empty = false;
            }

            if (t.Minutes > 0)
            {
                shortForm += $" {t.Minutes}{ResStrings.TimeCalculator_Min}";
                empty = false;
            }

            double millisecs = t.Milliseconds / 1000.0;
            if (t.Seconds > 0)
            {

                //  if (t.Seconds < hideFractionsBelowSecs)
                //    shortForm += $" {secondsStep}{ResStrings.TimeCalculator_Sec}";
                //else
                shortForm += $" {t.Seconds}{ResStrings.TimeCalculator_Sec}";

                empty = false;
            }

            if (empty)
            {
                //todo use photo fractions up to 1/8000
                //shortForm = $" {millisecs.ToUnicodeFractions3(true)}{ResStrings.TimeCalculator_Sec}"; //
                shortForm = $" {secondsStep}{ResStrings.TimeCalculator_Sec}";
            }

            return shortForm.Trim();
        }

    }
}
