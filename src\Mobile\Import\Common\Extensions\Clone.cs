﻿using System;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.Extensions
{
    public static class CloneExtensions
    {
        ////-------------------------------------------------------------------------------
        //public static ObservableRangeCollection<T> Clone<T>(this ObservableRangeCollection<T> source)
        ////-------------------------------------------------------------------------------
        //{
        //    if (Object.ReferenceEquals(source, null))
        //    {
        //        return default(ObservableRangeCollection<T>);
        //    }

        //    // In the PCL we do not have the BinaryFormatter
        //    return JsonConvert.DeserializeObject<ObservableRangeCollection<T>>(
        //        JsonConvert.SerializeObject(source));
        //}
        //-------------------------------------------------------------------------------
        public static IEnumerable<T> Clone<T>(this IEnumerable<T> source)
            //-------------------------------------------------------------------------------
        {
            if (Object.ReferenceEquals(source, null))
            {
                return default(IEnumerable<T>);
            }

            // In the PCL we do not have the BinaryFormatter
            return JsonConvert.DeserializeObject<IEnumerable<T>>(
                JsonConvert.SerializeObject(source));
        }


    }

}
