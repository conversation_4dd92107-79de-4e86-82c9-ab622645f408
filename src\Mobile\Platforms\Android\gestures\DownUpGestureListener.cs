using Android.Runtime;
using Android.Views;
using Java.Lang;
using AppoMobi.Touch;
using AppoMobi.Touch.Droid.EventArgs;
using System;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using View = Android.Views.View;

namespace AppoMobi.Touch.Droid
{
    internal class DownUpGestureListener : Java.Lang.Object
    {
        private IWithTouch element;

        private View view;

        private IGestureListener listener;

        internal DownUpGestureListener(IWithTouch element, View view, IGestureListener listener)
        {
            this.element = element;
            this.view = view;
            this.listener = listener;
        }

        internal DownUpGestureListener(IntPtr handle, JniHandleOwnership ownership) : base(handle, ownership)
        {

        }

        public bool OnDown(MotionEvent e)
        {
            DownUpGestureListener.u003cu003ec__DisplayClass5_0 variable = null;
            if (this.element.GestureHandler.HandlesDown)
            {
                AndroidDownUpEventArgs androidDownUpEventArg = new AndroidDownUpEventArgs(e, this.view);
                Task.Run(() =>
                {
                    listener.OnDown(androidDownUpEventArg);
                });
              //  Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnDown(this.args)));
            }
            return false;
        }

        public bool OnUp(MotionEvent e)
        {
            DownUpGestureListener.u003cu003ec__DisplayClass6_0 variable = null;
            if (this.element.GestureHandler.HandlesUp)
            {
                AndroidDownUpEventArgs androidDownUpEventArg = new AndroidDownUpEventArgs(e, this.view);
                //  Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnUp(this.args)));
                Task.Run(() =>
                {
                    listener.OnUp(androidDownUpEventArg);
                });
            }
            return false;
        }

        [CompilerGenerated]
        // <>c__DisplayClass5_0
        private sealed class u003cu003ec__DisplayClass5_0
        {
            public AndroidDownUpEventArgs args;

            // <>4__this
            public DownUpGestureListener u003cu003e4__this;

            public u003cu003ec__DisplayClass5_0()
            {
            }

            // <OnDown>b__0
            internal bool u003cOnDownu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnDown(this.args);
            }
        }

        [CompilerGenerated]
        // <>c__DisplayClass6_0
        private sealed class u003cu003ec__DisplayClass6_0
        {
            public AndroidDownUpEventArgs args;

            // <>4__this
            public DownUpGestureListener u003cu003e4__this;

            public u003cu003ec__DisplayClass6_0()
            {
            }

            // <OnUp>b__0
            internal bool u003cOnUpu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnUp(this.args);
            }
        }
    }
}