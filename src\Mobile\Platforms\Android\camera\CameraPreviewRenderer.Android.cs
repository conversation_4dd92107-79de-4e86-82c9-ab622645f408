﻿using Android.Content;
using Android.Graphics;
using AppoMobi.Droid.Camera;
using AppoMobi.Droid.Camera.Views;
using AppoMobi.Forms.Content.Camera.Controls;
using AppoMobi.Forms.Content.Camera.Models;
using SkiaSharp.Views.Android;
using System.ComponentModel;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
using CameraPreview = AppoMobi.Forms.Content.Camera.Controls.CameraPreview;
using Path = System.IO.Path;
using Stream = System.IO.Stream;


//[assembly: ExportRenderer(typeof(CameraPreview), typeof(CameraViewPreviewRenderer))]
namespace AppoMobi.Droid.Camera
{
    public class CameraViewPreviewRenderer : ViewRenderer<View, CameraView>, ICameraViewRenderer
    {
        protected bool isCapturingBitmap = false;

        /// <summary>
        /// Run this in background thread!
        /// </summary>
        /// <returns></returns>
        public async Task<SKBitmap> TakePictureForSkia()
        {
            if (isCapturingBitmap)
                return null;

            //isCapturingBitmap = false;
            //await Task.Delay(10); //update another threads
            Bitmap capturedBitmap = null;

            nativeControl.Control.CapturedImage = bitmap =>
            {
                capturedBitmap = bitmap;
                isCapturingBitmap = false;
            };

            FormsControl.IsTakingPhoto = true;
            isCapturingBitmap = true;

            nativeControl.Control.CaptureLocation = CaptureLocationType.Bitmap;

            Device.BeginInvokeOnMainThread(() =>
            {
                nativeControl.FlashScreen();
                nativeControl.Control.TakePicture();
            });

            while (isCapturingBitmap)
            {
                await Task.Delay(10);
            }

            var skBitmap = capturedBitmap.ToSKBitmap();

            //return rotated
            return HandleOrientation(skBitmap, nativeControl.Control.SavedRotation);
            //return skBitmap;
        }

        public SKBitmap HandleOrientation(SKBitmap bitmap, double sensor)
        {
            SKBitmap rotated;

            return bitmap;

            switch (sensor)
            {
                case 180:

                    using (var surface = new SKCanvas(bitmap))
                    {
                        surface.RotateDegrees(180, bitmap.Width / 2f, bitmap.Height / 2f);
                        surface.DrawBitmap(bitmap.Copy(), 0, 0);
                    }

                    return bitmap;

                case 270: //iphone on the right side
                    rotated = new SKBitmap(bitmap.Height, bitmap.Width);

                    using (var surface = new SKCanvas(rotated))
                    {
                        surface.Translate(0, rotated.Height);
                        surface.RotateDegrees(270);
                        surface.DrawBitmap(bitmap, 0, 0);
                    }

                    return rotated;

                case 90: // iphone on the left side
                    rotated = new SKBitmap(bitmap.Height, bitmap.Width);

                    using (var surface = new SKCanvas(rotated))
                    {
                        surface.Translate(rotated.Width, 0);
                        surface.RotateDegrees(90);
                        surface.DrawBitmap(bitmap, 0, 0);
                    }

                    return rotated;

                default:
                    return bitmap;
            }
        }


        public void FlashScreen()
        {
            if (nativeControl != null)
                nativeControl.FlashScreen();
        }

        public void SaveStreamAsFile(Stream inputStream, string fullFilename, double sensor)
        {
            //DirectoryInfo info = new DirectoryInfo(filePath);
            //if (!info.Exists)
            //{
            //    info.Create();
            //}

            //string path = Path.Combine(filePath, fileName);
            using (FileStream outputFileStream = new FileStream(fullFilename, FileMode.Create))
            {
                inputStream.CopyTo(outputFileStream);
            }

            FormsControl.SavedFilename = fullFilename;
            FormsControl.SavedRotation = sensor;

        }

        public async Task<bool> SaveJpgStreamToGallery(Stream stream, string filename, double sensor, string exifFromAnotherFilename = null)
        {

            string fullFilename = Path.Combine(nativeControl.Control.GetOutputGalleryFolder().AbsolutePath, filename);

            SaveStreamAsFile(stream, fullFilename, sensor);

            //todo use 			this.nativeControl.Control.Camera.Meta;
            //var newexif = new ExifInterface(exifFromAnotherFilename);

            //ExifInterface.TagUserComment = re
            //var dateTime = newexif.GetAttribute(ExifInterface.TagDatetime);
            //var make = newexif.GetAttribute(ExifInterface.TagMake);
            //var model = newexif.GetAttribute(ExifInterface.TagModel);
            //var latitude = newexif.GetAttribute(ExifInterface.TagGpsLatitude);
            //var longitude = newexif.GetAttribute(ExifInterface.TagGpsLongitude);

            nativeControl.Control.PublishFile(fullFilename);

            //todo investigate do we need this o its already set???
            //FormsControl.SavedFilename = filename;
            //FormsControl.SavedRotation = sensor;

            return true;
        }

        public void TakePicture()
        {
            if (nativeControl != null && nativeControl.Control != null)
            {
                nativeControl.Control.TakePicture();
                nativeControl.FlashScreen();
            }
        }

        public void AddFileToGallery(string filename, double sensor)
        {
            ContentValues values = new ContentValues();

            values.Put("_data", filename);
            values.Put("title", ResStrings.OwnerTitle);
            values.Put("mime_type", "image/jpeg"); // or image/png
            values.Put("description", ResStrings.OwnerTitle);

            FormsControl.SavedFilename = filename;
            FormsControl.SavedRotation = sensor;

            Context.ContentResolver.Insert(Android.Provider.MediaStore.Images.Media.ExternalContentUri, values);
        }


        //protected override void OnDraw(Canvas canvas)
        //{



        //    base.OnDraw(canvas);


        //    Paint paint = new Paint();
        //    paint.Color = Color.Red;
        //    paint.StrokeWidth = 1.5f;
        //    paint.SetStyle(Paint.Style.Stroke);
        //    canvas.DrawRect(0, 0, Width, Height, paint);
        //}



        public static List<CameraViewPreviewRenderer> Instances = new List<CameraViewPreviewRenderer>();

        public void Disable()
        {
            nativeControl?.Control?.CloseCamera(true);
        }

        // main camera engine code is inside CameraFragment class

        public void DisableOtherCameras(bool all = false)
        {
            SkiaCamera.StopAll();

            foreach (var renderer in CameraViewPreviewRenderer.Instances)
            {
                bool disable = false;
                if (all || renderer != this)
                {
                    disable = true;
                }

                if (disable)
                {
                    renderer.Disable();
                    System.Diagnostics.Debug.WriteLine($"[CAMERA] Stopped {renderer.FormsControl.StyleId}");
                }
            }
        }

        private void CreateNative(CameraPreview formsControl)
        {
            if (!FormsControl.IsEnabled)
                return;

            DisableOtherCameras();

            nativeControl = new CameraView(this.Context, RndExtensions.CreateRandom(464, 6128));

            UpdateBackgroundColor();

            SetNativeControl(nativeControl);

            //set options
            nativeControl.Control.Effect = FormsControl.Filter;
            nativeControl.Control.DisplayMode = FormsControl.DisplayMode;
            nativeControl.Control.Gamma = (float)FormsControl.Gamma;

            nativeControl.Control.CaptureLocation = formsControl.CaptureLocation;
            nativeControl.Control.CaptureCustomFolder = formsControl.CaptureCustomFolder;

            OnUpdateOrientation(null, null);

            SubscribeToNativeControl();

            nativeControl.Control.SetVirtualControl(formsControl);

            FormsControl.State = CameraState.On;

            formsControl.PlatformRenderer = this;


        }


        protected CameraView nativeControl;

        #region ctor

        public CameraViewPreviewRenderer(Context context) : base(context)
        {
        }

        protected CameraPreview FormsControl;


        #endregion

        protected override void OnElementChanged(ElementChangedEventArgs<View> e)
        {

            base.OnElementChanged(e);

            if (e.OldElement != null)
            {
                CameraViewPreviewRenderer.Instances.Remove(this);

                if (nativeControl != null)
                {
                    if (nativeControl.Control != null)
                    {
                        FormsControl.PlatformRenderer = null;

                        SubscribeToNativeControl(false);
                        //nativeControl.Control.Destroy();
                        nativeControl.Destroy(); //remove fragment
                                                 //nativeControl.Control.Dispose();

                        nativeControl.Control = null;
                        nativeControl = null;


                    }
                }



                e.OldElement.PropertyChanged -= OnControlPropertyChanged;

                FormsControl = null;

                System.Diagnostics.Debug.WriteLine("[CAMERA] Disposed");
            }

            if (e.NewElement != null)
            {

                if (!CameraViewPreviewRenderer.Instances.Contains(this))
                    CameraViewPreviewRenderer.Instances.Add(this);

                System.Diagnostics.Debug.WriteLine("[CAMERA] Created");

                FormsControl = (CameraPreview)e.NewElement;

                if (Control == null)
                {
                    CreateNative(FormsControl);
                }

                e.NewElement.PropertyChanged += OnControlPropertyChanged;

            }

        }


        private bool subscribed;

        protected void SubscribeToNativeControl(bool activate = true)
        {
            if (activate)
            {
                if (!subscribed)
                {
                    subscribed = true;

                    nativeControl.Control.OnImageTaken += OnImageTaken;
                    nativeControl.Control.OnImageTakingFailed += OnImageTakingFailed;
                    nativeControl.Control.OnUpdateFPS += OnUpdateFPS;
                    nativeControl.Control.OnUpdateOrientation += OnUpdateOrientation;
                    nativeControl.Control.PropertyChanged += OnNativeControlPropertyChanged;
                }
                else
                {
                    return;
                }
            }
            else
            {
                nativeControl.Control.OnImageTaken -= OnImageTaken;
                nativeControl.Control.OnImageTakingFailed -= OnImageTakingFailed;
                nativeControl.Control.OnUpdateFPS -= OnUpdateFPS;
                nativeControl.Control.OnUpdateOrientation -= OnUpdateOrientation;
                nativeControl.Control.PropertyChanged -= OnNativeControlPropertyChanged;

                subscribed = false;
            }
        }

        private void OnImageTakingFailed(object sender, EventArgs e)
        {
            FormsControl.IsTakingPhoto = false;
        }

        private void OnImageTaken(object sender, EventArgs e)
        {
            FormsControl.IsTakingPhoto = false;
        }

        private void OnNativeControlPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            //received from camera fragment
            if (e.PropertyName == "State")
            {

                return;
            }

            if (FormsControl != null)
            {
                if (e.PropertyName == "Camera")
                {
                    FormsControl.CameraDevice = nativeControl.Control.Camera;
                    return;
                }

                else if (e.PropertyName == "SavedFilename")
                {
                    FormsControl.SavedFilename = nativeControl.Control.SavedFilename;
                    return;
                }
                else if (e.PropertyName == "SavedRotation")
                {
                    FormsControl.SavedRotation = nativeControl.Control.SavedRotation;
                    return;
                }

                if (e.PropertyName == "CaptureWidth")
                {
                    FormsControl.CaptureWidth = nativeControl.Control.CaptureWidth;
                    return;
                }
                else if (e.PropertyName == "CaptureHeight")
                {
                    FormsControl.CaptureHeight = nativeControl.Control.CaptureHeight;
                    return;
                }
                else if (e.PropertyName == "PreviewWidth")
                {
                    FormsControl.PreviewWidth = nativeControl.Control.PreviewWidth;
                    return;
                }
                else if (e.PropertyName == "PreviewHeight")
                {
                    FormsControl.PreviewHeight = nativeControl.Control.PreviewHeight;
                    return;
                }
                else if (e.PropertyName == "Camera")
                {
                    FormsControl.CameraDevice = nativeControl.Control.Camera;
                    return;
                }
                if (e.PropertyName == "ZoomScale"
                    || e.PropertyName == "ZoomScaleTexture"
                    || e.PropertyName == "FocalLength"
                    || e.PropertyName == "ViewportScale")
                {
                    ReportFocalLength();
                    return;
                }
            }

        }


        public void ReportFocalLength()
        {
            double focal = nativeControl.Control.FocalLength * nativeControl.Control.ZoomScale * nativeControl.Control.ViewportScale;
            FormsControl.FocalLength = focal;

            FormsControl.Zoom = nativeControl.Control.ZoomScale;
            FormsControl.TextureScale = nativeControl.Control.ZoomScaleTexture;
            FormsControl.ViewportScale = nativeControl.Control.ViewportScale;
        }

        public void SetFocal(double focal)
        {
            // Z =  F / (B V)

            var zoom = focal / (nativeControl.Control.FocalLength * nativeControl.Control.ViewportScale);

            if (!Double.IsNaN(zoom))
                nativeControl.Control.SetZoom((float)zoom, true);
        }

        //public void SetZoom(double zoom)
        //{
        //    // Z = F / B V

        //    var focal = focal / nativeControl.Control.FocalLength * nativeControl.Control.ViewportScale;

        //    var check = nativeControl.Control.FocalLength * zoom * nativeControl.Control.ViewportScale;

        //    nativeControl.Control.ZoomScale = (float)zoom;
        //}

        private void OnUpdateFPS(object sender, EventArgs e)
        {
            FormsControl.FPS = nativeControl.Control.FPS;

        }

        public void UpdateBackgroundColor()
        {
            if (FormsControl.BackgroundColor !=null && FormsControl.BackgroundColor != Colors.Transparent)
            {
                nativeControl.SetBackgroundColor(FormsControl.BackgroundColor.ToPlatform());
            }
            else
            {
                SetBackgroundColor(Android.Graphics.Color.Black);
            }
        }



        private void OnUpdateOrientation(object sender, EventArgs e)
        {
            if (nativeControl == null)
                return;

            if (nativeControl.Control.DeviceOrientation == 90)
            {
                FormsControl.Orientation = DeviceOrientation.LandscapeRight;
                nativeControl.Control.Orientation = 270;
            }
            else if (nativeControl.Control.DeviceOrientation == 270)
            {
                FormsControl.Orientation = DeviceOrientation.LandscapeLeft;
                nativeControl.Control.Orientation = 90;
            }
            else
            if (nativeControl.Control.DeviceOrientation == 180)
            {
                FormsControl.Orientation = DeviceOrientation.PortraitUpsideDown;
                nativeControl.Control.Orientation = 180;
            }
            else
            {
                FormsControl.Orientation = DeviceOrientation.Portrait;
                nativeControl.Control.Orientation = 0;
            }
        }


        private void OnControlPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            //received from forms control (CameraPreview)

            EnhancedCamera.ExecuteOnMainThread(() =>
            {
                // Update the UI

                var control = sender as CameraPreview;

                if (control == null)
                    return;

                try
                {


                    if (e.PropertyName == "IsEnabled")
                    {
                        if (Control == null && FormsControl.IsEnabled)
                        {
                            CreateNative(FormsControl);
                        }

                        return;
                    }

                    if (nativeControl == null || FormsControl == null)
                    {
                        return;
                    }



                    //if (e.PropertyName == "Zoom")
                    //{
                    //    SetZoom(FormsControl.Zoom);
                    //    return;
                    //}
                    //else
                    if (e.PropertyName == "FocalLength")
                    {
                        SetFocal(FormsControl.FocalLength);
                        return;
                    }
                    //if (e.PropertyName == "Camera")
                    //{
                    //    //switched camera
                    //    var options = new CameraOptions
                    //    {
                    //        Type = FormsControl.Camera,
                    //        Effect = FormsControl.Filter
                    //    };
                    //    nativeControl.ChangeConfiguration(options);
                    //}
                    else
                    if (e.PropertyName == "Filter")
                    {
                        nativeControl.Control.Effect = FormsControl.Filter;
                    }

                    else if (e.PropertyName == "CaptureLocation")
                    {
                        nativeControl.Control.CaptureLocation = FormsControl.CaptureLocation;
                    }
                    else if (e.PropertyName == "CaptureCustomFolder")
                    {
                        nativeControl.Control.CaptureCustomFolder = FormsControl.CaptureCustomFolder;
                    }

                    //else
                    //if (e.PropertyName == "ColorPreset")
                    //{
                    //    nativeControl.Control.FilterPreset = FormsControl.ColorPreset;
                    //}
                    else if (e.PropertyName == "DisplayMode")
                    {
                        nativeControl.Control.DisplayMode = FormsControl.DisplayMode;
                    }
                    else if (e.PropertyName == "Gamma")
                    {
                        var gamma = (float)FormsControl.Gamma;
                        nativeControl.Control.Gamma = gamma;
                    }
                    else if (e.PropertyName == "BackgroundColor")
                    {
                        UpdateBackgroundColor();
                    }
                    //else
                    //if (e.PropertyName == "WhiteColor")
                    //{
                    //    Color? white = FormsControl.WhiteColor;
                    //    if (white == Color.Default || white == Color.Transparent)device
                    //        white = null;
                    //    LastWhiteColor = white;
                    //    //reset black if overlapping
                    //    if (LastBlackColor != null)
                    //    {
                    //        if (LastBlackColor.Value.R >= LastWhiteColor.Value.R ||
                    //            LastBlackColor.Value.G >= LastWhiteColor.Value.G ||
                    //            LastBlackColor.Value.B >= LastWhiteColor.Value.B)
                    //        {
                    //            LastBlackColor = null;
                    //            FormsControl.BlackColor = Color.Black;
                    //        }
                    //    }
                    //    nativeControl.SetupColorNegativeManual(LastBlackColor, null, LastWhiteColor);
                    //}
                    //else
                    //if (e.PropertyName == "BlackColor")
                    //{
                    //    Color? black = FormsControl.BlackColor;
                    //    if (black == Color.Default || black == Color.Transparent)
                    //        black = null;
                    //    LastBlackColor = black;
                    //    //reset white if overlapping
                    //    if (LastWhiteColor != null)
                    //    {
                    //        if (LastBlackColor.Value.R >= LastWhiteColor.Value.R ||
                    //            LastBlackColor.Value.G >= LastWhiteColor.Value.G ||
                    //            LastBlackColor.Value.B >= LastWhiteColor.Value.B)
                    //        {
                    //            LastWhiteColor = null;
                    //            FormsControl.WhiteColor = Color.White;
                    //        }
                    //    }
                    //    nativeControl.SetupColorNegativeManual(LastBlackColor, null, LastWhiteColor);
                    //}
                    //else
                    //    if (e.PropertyName == "CommandToRenderer")
                    //{
                    //    if (control.CommandToRenderer == "PickColor")
                    //    {
                    //        FormsControl.BlackColor = Color.Black; //todo react change arguments
                    //        FormsControl.PickerMode = CameraPickerMode.NegativeBlack;
                    //    }
                    //    else
                    //    if (control.CommandToRenderer == "PickColorWhite")
                    //    {
                    //        FormsControl.WhiteColor = Color.White; //todo react change arguments
                    //        FormsControl.PickerMode = CameraPickerMode.NegativeWhite;
                    //    }
                    //    else
                    //    if (control.CommandToRenderer == "FilterNone")
                    //    {
                    //        nativeControl.SetEffect(CameraEffect.None);
                    //    }
                    //    else
                    //    if (control.CommandToRenderer == "FilterGrayscaleNegative")
                    //    {
                    //        nativeControl.SetEffect(CameraEffect.GrayscaleNegative);
                    //    }
                    //    else
                    //    if (control.CommandToRenderer == "FilterGrayscale")
                    //    {
                    //        nativeControl.SetEffect(CameraEffect.Grayscale);
                    //    }
                    //    else
                    //    if (control.CommandToRenderer == "FilterNegative")
                    //    {
                    //        nativeControl.SetEffect(CameraEffect.ColorNegativeAuto);
                    //    }
                    else if (e.PropertyName == "CommandToRenderer")
                    {
                        //Console.WriteLine($"[CAMERA] {control.CommandToRenderer} {control.StyleId}");

                        if (control.CommandToRenderer == "TakePictureHigh")
                        {
                            FormsControl.IsTakingPhoto = true; //negative camera
                            nativeControl.FlashScreen();
                            nativeControl.Control.TakePicture();

                        }
                        else if (control.CommandToRenderer == "TakePicture")
                        {
                            FormsControl.IsTakingPhoto = true;
                            nativeControl.FlashScreen();
                            nativeControl.Control.TakePicture();

                        }
                        else if (control.CommandToRenderer == "Destroy")
                        {
                            if (nativeControl != null)
                            {
                                nativeControl.Control?.Stop();
                            }

                            OnElementChanged(new ElementChangedEventArgs<View>(oldElement: FormsControl, null));
                        }
                        else if (control.CommandToRenderer == "HardPause")
                        {
                            nativeControl?.Control?.Stop();

                        }
                        //    else
                        //    if (control.CommandToRenderer == "Pause")
                        //    {
                        //        nativeControl.Pause();
                        //    }
                        else if (control.CommandToRenderer == "Resume")
                        {
                            //pause other cameras
                            DisableOtherCameras();

                            nativeControl.Control.Resume();
                        }
                        //    else
                        //    if (control.CommandToRenderer == "Test")
                        //    {

                        //    }
                        control.SendCommandToRenderer(null);
                    }
                }
                catch (Exception exception)
                {
                    Console.WriteLine(exception);
                }
                finally
                {
                    control.SendCommandToRenderer(null);
                }



                //}


            });


        }


    }

}
