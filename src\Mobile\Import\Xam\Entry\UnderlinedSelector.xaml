﻿<?xml version="1.0" encoding="UTF-8"?>
<Grid 
              xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:gestures="clr-namespace:AppoMobi.Touch"
             xmlns:xam="clr-namespace:AppoMobi.Xam"
             xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"

             WidthRequest="300"
              HorizontalOptions="Center"

             x:Class="AppoMobi.Xam.UnderlinedSelector">

    <!--placeholder-->


    <!--entry and line-->
    <StackLayout Spacing="0" Margin="0,0,0,15">

        <Label
            TextColor="{x:Static xam:TextColors.Placeholder}"
            FontSize="{x:Static xam:FontSizes.Entry}"
            HorizontalOptions="Center"
            x:Name="cEntry"/>

        <!--line-->
        <svg:GradientBox
        x:Name="cGradientLine"
        HeightRequest="1"
        HorizontalOptions="FillAndExpand"
        GradientOrientation="Horizontal"
        StartColor= "{x:Static xam:BackColors.GradientStartLine}"
        EndColor="{x:Static xam:BackColors.GradientEndLine}"/>


</StackLayout>

    <Label         
        VerticalOptions="End"
        HorizontalOptions="Fill"
        HorizontalTextAlignment="Center"
        x:Name="cPlaceholder" 
        FontSize="{x:Static xam:FontSizes.EntrySmallPlaceholder}" 
           Margin="0,0,0,0"
           TextColor="{x:Static xam:TextColors.Placeholder}"/>

    <ContentView x:Name="PickerContainer"
                 Opacity="0"
                 IsClippedToBounds="True"
                 HeightRequest="1"/>

  <!--hotspot-->
    <gestures:LegacyGesturesBoxView
        Margin="0,4,0,0"
 
        VerticalOptions="Start"
        WidthRequest="150"
        HeightRequest="20"
        Tapped="OnTapped_Selector"/>



</Grid>