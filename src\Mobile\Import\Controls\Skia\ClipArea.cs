﻿using SkiaSharp;
using SkiaSharp.Views.Maui.Controls;
using System;
using SkiaSharp.Views.Maui;


namespace AppoMobi.Forms.Controls.Skia
{
	public class ClipArea : SKCanvasView, IDisposable
	{


		public void Dispose()
		{
			PaintSurface -= CanvasViewOnPaintSurface;
		}


		public ClipArea()
		{
			IgnorePixelScaling = true;

			HorizontalOptions = LayoutOptions.Start;
			VerticalOptions = LayoutOptions.Start;
			BackgroundColor = Colors.Transparent;

			PaintSurface += CanvasViewOnPaintSurface;
		}

		#region PROPERTIES

		//todo 

		//-------------------------------------------------------------
		// ClipWidth
		//-------------------------------------------------------------
		private const string nameClipWidth = "ClipWidth";
		public static readonly BindableProperty ClipWidthProperty = BindableProperty.Create(nameClipWidth, typeof(double), typeof(ClipArea),
			40.0,
			propertyChanged: RedrawCanvas);
		public double ClipWidth
		{
			get { return (double)GetValue(ClipWidthProperty); }
			set { SetValue(ClipWidthProperty, value); }
		}

		//-------------------------------------------------------------
		// ClipHeight
		//-------------------------------------------------------------
		private const string nameClipHeight = "ClipHeight";
		public static readonly BindableProperty ClipHeightProperty = BindableProperty.Create(nameClipHeight, typeof(double), typeof(ClipArea),
			40.0,
			propertyChanged: RedrawCanvas);
		public double ClipHeight
		{
			get { return (double)GetValue(ClipHeightProperty); }
			set { SetValue(ClipHeightProperty, value); }
		}

		//-------------------------------------------------------------
		// TintColor
		//-------------------------------------------------------------
		private const string nameTintColor = "TintColor";
		public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(ClipArea),
				Colors.DarkGray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color TintColor
		{
			get { return (Color)GetValue(TintColorProperty); }
			set { SetValue(TintColorProperty, value); }
		}

		//-------------------------------------------------------------
		// ClipOperation
		//-------------------------------------------------------------
		private const string nameClipOperation = "ClipOperation";
		public static readonly BindableProperty ClipOperationProperty = BindableProperty.Create(nameClipOperation, typeof(SKClipOperation), typeof(ClipArea),
			SKClipOperation.Difference,
			propertyChanged: RedrawCanvas);
		public SKClipOperation ClipOperation
		{
			get { return (SKClipOperation)GetValue(ClipOperationProperty); }
			set { SetValue(ClipOperationProperty, value); }
		}




		#endregion

		//protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
		//{
		//    base.OnPropertyChanged(propertyName);

		//    if (propertyName == nameClipHeight
		//        || propertyName == nameClipWidth
		//        || propertyName == nameTintColor
		//    )
		//    {
		//        _canvasView?.InvalidateSurface();
		//    }

		//}

		//-------------------------------------------------------------
		private void CanvasViewOnPaintSurface(object sender, SKPaintSurfaceEventArgs argsSurface)
		//-------------------------------------------------------------
		{
			SKCanvas canvas = argsSurface.Surface.Canvas;

			canvas.Clear(SKColors.Transparent);

			SKImageInfo info = argsSurface.Info;

			SKSurface surface = argsSurface.Surface;

			//var svgClipShape = "<svg><rect width= \"20\" height= \"20\" style =\"fill:rgb(0,0,255);\" /></svg>";
			//SKPath pathClip = SKPath.ParseSvgPathData(svgClipShape);


			//create clip path
			var clipRect = new SKRect(0, 0, (float)ClipWidth, (float)ClipHeight);

			//center inside canvas
			var offsetX = (info.Width - clipRect.Width) / 2.0f;
			var offsetY = (info.Height - clipRect.Height) / 2.0f;

			//var clipRectCentered = new new SKRect(0, 0, (float)ClipWidth, (float)ClipHeight);

			clipRect.Offset(new SKPoint(offsetX, offsetY));

			//clip
			var clipPath = new SKPath();
			clipPath.AddRect(clipRect);
			canvas.ClipPath(clipPath, ClipOperation, true);

			//create filler
			var paint = new SKPaint
			{
				Style = SKPaintStyle.Fill,
				Color = TintColor.ToSKColor()
			};

			// draw fill
			canvas.DrawRect(0, 0, info.Width, info.Height, paint);

			//XmlReader reader = XmlReader.Create(new StringReader(svgClipShape));
			//var svg = new SKSvg();
			//svg.Load(reader);

			//using (SKPaint paint = new SKPaint())
			//{


			//}




		}

		private static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)
		{
			var me = bindable as ClipArea;
			me.InvalidateSurface();
		}




	}

}
