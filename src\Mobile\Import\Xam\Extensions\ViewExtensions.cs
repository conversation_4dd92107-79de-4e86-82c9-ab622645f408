﻿using System;
using Point = Microsoft.Maui.Graphics.Point;
using Size = Microsoft.Maui.Graphics.Size;


namespace AppoMobi.Xam.Extensions
{
    public static class ViewExtensions
    {
        /// <summary>
        /// Gets the page to which an element belongs
        /// </summary>
        /// <returns>The page.</returns>
        /// <param name="element">Element.</param>
        public static Page GetParentPage(this VisualElement element)
        {
            if (element != null)
            {
                var parent = element.Parent;
                while (parent != null)
                {
                    if (parent is Page)
                    {
                        return parent as Page;
                    }
                    parent = parent.Parent;
                }
            }
            return null;
        }


        
        public static bool ClickedInsideWindow(this View parent, Point click)
        
        {
            var screenDensity = Core.DisplayDensity;
            //Get parent screen abs pos in pixels
            //We are using native code get absolute screen position
            var positionParent = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(parent);
 
                //Gets childs (hotspots) rectangles, everything in pixels using screen density
                var rectChild = new Rect(positionParent, new Size((int)(parent.Width * screenDensity), (int)(parent.Height * screenDensity)));

                //Convert the finger XY to screen pos in pixels
                var positionClick = new Point((int)(positionParent.X + click.X * screenDensity), (int)(positionParent.Y + click.Y * screenDensity)); //absolute relative to screen

                if (rectChild.Contains(positionClick))
                    return true;
 
            return false;
        }

        
        public static bool ClickedInsideWindows(this View parent, Point click , View[] children)
        
        {
            var screenDensity = Core.DisplayDensity;
            //Get parent screen abs pos in pixels
            //We are using native code get absolute screen position
            var positionParent = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(parent);

            foreach (var child in children)
            {
                //Gets childs (hotspots) screen abs position in pixels
                var positionChild = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(child);

                //Gets childs (hotspots) rectangles, everything in pixels using screen density
                var rectChild = new Rect(positionChild, new Size((int)(child.Width * screenDensity), (int)(child.Height * screenDensity)));

                //Convert the finger XY to screen pos in pixels
                var positionClick = new Point((int)(positionParent.X + click.X * screenDensity), (int)(positionParent.Y + click.Y * screenDensity)); //absolute relative to screen

                if (rectChild.Contains(positionClick))
                    return true;
            }


            return false;
        }

        public static void Animate(this View view, AnimationType animationType = AnimationType.Shake, uint speed=350, Action callback=null)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var initialT = view.Opacity;
                try
                {
                   
                    if (animationType == AnimationType.Fade)
                    {
                        await view.FadeTo(0.3, 300);
                        await view.FadeTo(initialT, 300);
                    }
                    else if (animationType == AnimationType.EntranceX)
                    {
                        view.Opacity = 0.0;
                        await view.FadeTo(1.0, speed).ContinueWith(_ => {  callback?.Invoke();  });
                    }
                    else if (animationType == AnimationType.Scale)
                    {
                        await view.ScaleTo(1.2, 170, easing: Easing.Linear);
                        await view.ScaleTo(1, 170, easing: Easing.Linear);
                    }
                    else if (animationType == AnimationType.TapDown)
                    {
                        await view.ScaleTo(0.99, 50, easing: Easing.Linear);
                    }
                    else if (animationType == AnimationType.TapUp)
                    {
                        await view.ScaleTo(1.0, 50, easing: Easing.Linear);
                    }
                    else if (animationType == AnimationType.Rotate)
                    {
                        await view.RotateTo(360, 200, easing: Easing.Linear);
                        view.Rotation = 0;
                    }
                    else if (animationType == AnimationType.FlipHorizontal)
                    {
                        // Perform half of the flip
                        await view.RotateYTo(90, 200);
                        await view.RotateYTo(0, 200);
                    }
                    else if (animationType == AnimationType.FlipVertical)
                    {
                        // Perform half of the flip
                        await view.RotateXTo(90, 200);
                        await view.RotateXTo(0, 200);
                    }
                    else if (animationType == AnimationType.Shake)
                    {
                        await view.TranslateTo(-15, 0, 50);
                        await view.TranslateTo(15, 0, 50);
                        await view.TranslateTo(-10, 0, 50);
                        await view.TranslateTo(10, 0, 50);
                        await view.TranslateTo(-5, 0, 50);
                        await view.TranslateTo(5, 0, 50);
                        view.TranslationX = 0;
                    }
                }
                catch
                {
                    view.Opacity = initialT;
                }
                finally
                {
                    //if (Command != null)
                    //{
                    //    if (Command.CanExecute(CommandParameter))
                    //    {
                    //        Command.Execute(CommandParameter);
                    //    }
                    //}
                    //System.Diagnostics.Debug.WriteLine(CommandParameter);

                    //_isAnimating = false;
                }

            });

        }

 
    }
}