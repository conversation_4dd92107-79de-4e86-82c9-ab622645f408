﻿using Android.Views;
using Android.Webkit;
using AppoMobi.Xam;
using Java.Interop;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using WebView = Android.Webkit.WebView;

namespace AppoMobi.Droid.Renderers
{
	public class CustomWkWebViewRenderer : WebViewHandler
	{
        public class MyWebViewClient : WebViewClient
        {
            private readonly string _javascript;

            public MyWebViewClient(string javascript)
            {
                _javascript = javascript;
            }

            public override void OnPageFinished(Android.Webkit.WebView view, string url)
            {
                base.OnPageFinished(view, url);

                // Inject your JavaScript once the page has loaded.
                if (!string.IsNullOrWhiteSpace(_javascript))
                {
                    // EvaluateJavascript is asynchronous
                    view?.EvaluateJavascript(_javascript, null);
                }
            }
        }

        const string JavascriptFunction = "function invokeCSharpAction(data){jsBridge.invokeAction(data);}";

        protected override void ConnectHandler(WebView platformView)
        {
            base.ConnectHandler(platformView);

            this.FormsControl = (XamWebView)this.VirtualView;

            Control.SetWebViewClient(new MyWebViewClient( $"javascript: {JavascriptFunction}"));

            Control.AddJavascriptInterface(new JSBridge(this), "jsBridge");

            if (Android.OS.Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.Lollipop)
            {
                var check = CookieManager.Instance.AcceptCookie();
                check = CookieManager.Instance.AcceptThirdPartyCookies(Control);

                CookieManager.Instance.SetAcceptCookie(true);
                CookieManager.Instance.SetAcceptThirdPartyCookies(Control, true);

                //string cookieString = "datr=eTiwX31TzpjZSn3hHJKJJZ2H; domain=.facebook.com; path=/";
                //CookieManager.Instance.SetCookie("https://facebook.com", cookieString);
            }

            //  SetNativeControl(new CustomWebVew(Context));



            FormsControl.RendererCommand += OnRendererCommand;

            //todo
            //Control.SetOnScrollChangeListener();

            //if (FormsControl.WebViewColor != Color.Transparent)
            //{
            //    Control.SetBackgroundColor(FormsControl.WebViewColor.ToAndroid());
            //}

            Control.SetBackgroundColor(FormsControl.WebViewColor.ToPlatform());

            if (FormsControl.ZoomEnabled)
            {
                //If you want to support zoom buttons
                //Control.Settings.BuiltInZoomControls = true;
                Control.Settings.SetSupportZoom(true);

                Control.SetInitialScale(100);
                Control.Settings.UseWideViewPort = true;
                Control.Settings.LoadWithOverviewMode = true;
            }

            Control.OverScrollMode = OverScrollMode.Never;

        }

        protected override void DisconnectHandler(WebView platformView)
        {
            FormsControl.RendererCommand -= OnRendererCommand;
            FormsControl = null;

            Control.RemoveJavascriptInterface("jsBridge");

            base.DisconnectHandler(platformView);
        }

        WebView Control => PlatformView as WebView;




		public XamWebView FormsControl { get; set; }

		public void StopPlayback()
		{
			// Control.PauseTimers();;
			Control.OnPause();
		}

		public void ResumePlayback()
		{
			Control.OnResume();
		}

		private void OnRendererCommand(object sender, EventArgs e)
		{
			if (sender is string)
			{
				var command = (string)sender;

				if (command == "PausePlay")
				{
					StopPlayback();
				}
				else
				if (command == "ResumePlay")
				{
					ResumePlayback();
				}
				else
				if (command == "EnableZoom")
				{
					//If you want to support zoom buttons
					Control.Settings.BuiltInZoomControls = true;
					Control.Settings.SetSupportZoom(true);
					Control.Settings.UseWideViewPort = true;
					Control.Settings.LoadWithOverviewMode = true;
				}
				else
				if (command == "DisableZoom")
				{
					//If you want to support zoom buttons
					Control.Settings.BuiltInZoomControls = false;
					Control.Settings.SetSupportZoom(false);
					Control.Settings.UseWideViewPort = false;
					Control.Settings.LoadWithOverviewMode = false;
				}

			}

		}
 

		public class JSBridge : Java.Lang.Object
		{
			readonly WeakReference<CustomWkWebViewRenderer> hybridWebViewRenderer;

			public JSBridge(CustomWkWebViewRenderer hybridRenderer)
			{
				hybridWebViewRenderer = new WeakReference<CustomWkWebViewRenderer>(hybridRenderer);
			}

			[JavascriptInterface]
			[Export("invokeAction")]
			public void InvokeAction(string data)
			{
				CustomWkWebViewRenderer hybridRenderer;

				if (hybridWebViewRenderer != null && hybridWebViewRenderer.TryGetTarget(out hybridRenderer))
				{
					// todo https://docs.microsoft.com/en-us/xamarin/xamarin-forms/app-fundamentals/custom-renderer/hybridwebview
					//    ((WebView)hybridRenderer.Element).InvokeAction(data);
				}
			}
		}

	}






}
