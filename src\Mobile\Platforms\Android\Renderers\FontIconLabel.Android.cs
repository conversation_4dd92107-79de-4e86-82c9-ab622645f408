﻿using Android.Graphics;
using AndroidX.AppCompat.Widget;
using AppoMobi.Droid.Native;
using AppoMobi.Xam;
using Microsoft.Maui.Handlers;

//[assembly: ExportRenderer(typeof(FontIconLabel), typeof(FontIconLabel_Renderer))]
namespace AppoMobi.Droid
{
    public class FontIconLabel_Renderer : LabelHandler
    {
        protected override void ConnectHandler(AppCompatTextView platformView)
        {
            base.ConnectHandler(platformView);

            CurrentLabel = (FontIconLabel)this.VirtualView;
            CurrentLabel.RendererNeedUpdate += OnUpdateSkin;

            OnUpdateSkin(this, null);
        }

        protected override void DisconnectHandler(AppCompatTextView platformView)
        {
            base.DisconnectHandler(platformView);

            if (CurrentLabel!=null)
                CurrentLabel.RendererNeedUpdate -= OnUpdateSkin;
            
            CurrentLabel = null;
        }

        AppCompatTextView Control => PlatformView as AppCompatTextView;

        protected FontIconLabel CurrentLabel { get; private set; }

        protected Typeface IconFont => DroidCore.FontIcons;
        
        private void OnUpdateSkin(object sender, EventArgs e)
        
        {
            Typeface tf = null;

                if (!string.IsNullOrEmpty(CurrentLabel.Font))
                {
                    tf = DroidCore.RequestFont(CurrentLabel.Font);
                }
                if (tf == null)
                {
                    Control.Typeface = IconFont; //base default
                }
                else
                {
                    Control.Typeface = tf;
                }

                //apply preset data
                //if (CurrentLabel?.Preset != null)
                //{
                //    Control.SetPaddingRelative(Padding.Left, Padding.Top, Padding.Right, Padding.Bottom);
                //}

            Control.ForceLayout();
        }

 

        
    


    }
}
