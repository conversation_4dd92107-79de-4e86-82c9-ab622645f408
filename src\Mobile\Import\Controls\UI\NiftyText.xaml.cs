﻿using System;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    //===================================================================
    public partial class NiftyText : ContentView
        //===================================================================
    {
        
        // Constructor
        
        public NiftyText()
        {
            InitializeComponent();
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);
            switch (propertyName)
            {
                case nameLabelText:
                    ControlLabelText.Text = LabelText;//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;
                //                case nameLabelIcon:
                //                    ControlLabelIcon.Source = LabelIcon;
                //                    break;
            }

        }

        
        // LabelText
        

        //ublic static readonly BindableProperty EventNameProperty =BindableProperty.Create("EventName", typeof(string), typeof(EventToCommandBehavior), null);


        private const string nameLabelText = "LabelText";
        public static readonly BindableProperty LabelTextProperty =
            BindableProperty.Create(nameLabelText, typeof(string), typeof(NiftyText), string.Empty);
        
        public string LabelText
            
        {
            get { return (string)GetValue(LabelTextProperty); }
            set { SetValue(LabelTextProperty, value); }
        }
        /*
                
                // LabelIcon
                
                private const string nameLabelIcon = "LabelIcon";
                public static readonly BindableProperty Property2 = BindableProperty.Create(nameLabelIcon, typeof(string), typeof(NiftyShare), string.Empty);
                public string LabelIcon
                {
                    get { return (string)GetValue(Property2); }
                    set { SetValue(Property2, value); }
                }
        */
        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty Property3 = BindableProperty.Create(nameTag, typeof(string), typeof(NiftyText), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(Property3); }
            set { SetValue(Property3, value); }
        }

        
        // Tapped
        
        public event EventHandler Tapped = null;
        private async void OnTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, EventArgs.Empty);
        }
        
        // Shared
        
        public event EventHandler Shared = null;
        private async void OnShared(object sender, EventArgs e)
        {
            Shared?.Invoke(this, EventArgs.Empty);
        }
    }
}