﻿
using FFImageLoading.Maui;
using FFImageLoading.Transformations;


namespace AppoMobi.Xam
{
    //============================================================================
    /// <summary>
    /// 
    /// </summary>
    public class NiftyDotImage : ContentView
    //============================================================================
    {
        public CachedImage Img { get; set; }

        public NiftyDotImage()
        {
            Img= Build();
            Content = Img;
        }
        
        private CachedImage Build()
        
        {
            var img = new CachedImage
            {
                Source = "round",
                HeightRequest = DotSize,
                WidthRequest = DotSize
            };
            if (ColorTint != null && ColorTint != Colors.Transparent)
            {
                var hex = ColorTint.ToHex();
                var tint = new TintTransformation(hex);
                tint.EnableSolidColor = true;
                img.Transformations.Add(tint);
            }
            return img;
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);
            switch (propertyName)
            {
                case nameDotSize:
                    if (Img != null)
                    {
                        Img.HeightRequest = DotSize;
                        Img.WidthRequest = DotSize;
                    }
                    break;

                case nameColorTint:
                    Img.Transformations.Clear();
                    if (ColorTint != null && ColorTint != Colors.Transparent)
                    {
                        var hex = ColorTint.ToHex();
                        var tint = new TintTransformation(hex);
                        tint.EnableSolidColor = true;
                        Img.Transformations.Add(tint);
                    }
                    break;
            }

        }

        
        // ColorTint
        
        private const string nameColorTint = "ColorTint";
        public static readonly BindableProperty ColorTintProperty = BindableProperty.Create(nameColorTint, typeof(Color), typeof(NiftyDotImage), Colors.White); //, BindingMode.TwoWay
        public Color ColorTint
        {
            get { return (Color)GetValue(ColorTintProperty); }
            set { SetValue(ColorTintProperty, value); }
        }	

    
        
        // Total of dots
        
        private const string nameDotSize = "DotSize";
        public static readonly BindableProperty DotSizeProperty = BindableProperty.Create(nameDotSize, typeof(int), typeof(NiftyDots), 2);
        public int DotSize
        {
            get { return (int)GetValue(DotSizeProperty); }
            set { SetValue(DotSizeProperty, value); }
        }

    }
}
