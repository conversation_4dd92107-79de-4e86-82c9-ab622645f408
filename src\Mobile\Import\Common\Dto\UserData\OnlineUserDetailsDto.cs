﻿using System;
using System.Collections.Generic;
using AppoMobi.Framework.Api;
using AppoMobi.Common.Dto.Required;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class OnlineUserDetailsDto : BaseFrameworkDto
    {

        public OnlineUserDetailsDto()
        {
            Profiles = new List<ServiceProfileDto>();
            Documents = new List<GalleryImageDTO>();
            CreatedTime = DateTime.UtcNow;
        }

        public string Code { get; set; }

        public string Fullname { get; set; }

        public string FirstName { get; set; }

        public string Avatar { get; set; }

        public List<GalleryImageDTO> Documents { get; set; }

        public List<ServiceProfileDto> Profiles { get; set; }

        public string SelectedProfile { get; set; }

        public string SelectedRole { get; set; }

        public string AvatarColor { get; set; }

        public int Gender { get; set; }

        public int Age { get; set; }

        public string Country { get; set; }

        public string CityDesc { get; set; }

        public bool IsOnline { get; set; }

        public bool IsFav { get; set; }

        public string Notes { get; set; }

        public bool IsVerified { get; set; }

        public string SpokenLanguages { get; set; }

        public bool IsDND { get; set; } //занят

        public ProfileType Type { get; set; }

        public string Roles { get; set; }

        public string RelationKey { get; set; }

        public DateTime? Checked { get; set; }

        public DateTime? RelationConfirmed { get; set; }

        public string CreatedBy { get; set; }
        
        public bool Blocked { get; set; }

        public int TimesShared { get; set; }

        public int TimesViewed { get; set; }

        public DateTime? LastOnline { get; set; }
    }
}