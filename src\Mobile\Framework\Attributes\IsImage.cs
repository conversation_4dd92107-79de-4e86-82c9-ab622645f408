﻿using System;

namespace AppoMobi.Framework.Attributes
{
    public class IsImage : Attribute
        //-
    {
        public ImageType Type { get; set; }

        public string Ratio { get; set; }

        public bool Editor { get; set; }

        public int UploadType { get; set; }

        private string _role;
        
        public string Role
        {
            get
            {
                if (_role == null)
                    return "Main";
                return _role;
            }
            set
            {
                _role = value;
            }
        }  // main, mini?..
        
        public string Name { get; set; }

        public string PreviewMask { get; set; }
        
        public bool ForceToJpg { get; set; }
        
        public string BackgroundHexColor { get; set; }
        
        private long _jpgQuality;

        public long JpgQuality
        {
            get
            {
                if (_jpgQuality < 1)
                    return 90L;
                return _jpgQuality;
            }
            set
            {
                _jpgQuality = value;
            }
        }

    }
}