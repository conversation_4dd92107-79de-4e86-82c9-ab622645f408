﻿namespace AppoMobi.Main
{
    public class StatusBarPlaceholder : <PERSON>aShape
    {
        public StatusBarPlaceholder()
        {
            Tag = "StatusBarPadding";
            HorizontalOptions = LayoutOptions.Fill;

            this.Observe(App.Instance.Presentation, (me, prop) =>
            {
                if (prop.IsEither(nameof(BindingContext), nameof(NavigationViewModel.StatusBarHeightRequest)))
                {
                    me.HeightRequest = App.Instance.Presentation.StatusBarHeightRequest;
                }
            });
        }
    }
}
