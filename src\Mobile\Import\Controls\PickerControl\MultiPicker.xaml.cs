﻿using System;
using System.Collections.Generic;
using System.Windows.Input;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Models;
using AppoMobi.Framework.Abstractions;


namespace AppoMobi.Forms.Controls.PickerControl
{
    public partial class MultiPicker  
    {
        public MultiPicker()
        {
            InitializeComponent();
        }

        //protected override void OnParentSet()
        //{
        //    base.OnParentSet();

        //    if (ControlTemplate == null)
        //        ControlTemplate = DefaultTemplate;
        //}

        public static IList<OptionItem> BuildMultiselectionList(IEnumerable<IHasTitleWithId> list, Func<IHasTitleWithId, bool> isSelected)
        {
            var index = 0;
            var newList = new List<OptionItem>();
            foreach (var item in list)
            {
                var newItem =
                    new OptionItem
                    {
                        Id = item.Id,
                        //Title = $"{item.Title} ({item.Id})",
                        Title = $"{item.Title}"
                    };
                if (isSelected != null)
                {
                    newItem.Selected = isSelected(item);
                }
                newList.Add(newItem);
                index++;
            }
            return newList;
        }

        public static void PresentForMultiOptions(IList<OptionItem>list, ICommand onSubmit, int min=-1, int max=-1, string title = null, string customActionTitle=null, 
            ICommand customActionCommand=null)
        {
            var showTitle = ResStrings.Selection;
            if (!string.IsNullOrEmpty(title))
                showTitle = title;

            var picker = new MultiPicker
            {
                Title = showTitle,
                ItemsSource = list,
                MultiselectMin = min,
                MultiselectMax = max,
                CommandOnSubmit = onSubmit,
                CustomActionTitle = customActionTitle,
                CommandCustomAction = customActionCommand,
            };
            picker.Show();
        }

        public static void PresentForSingleOption(IEnumerable<PickerOption> buttons, string title=null)
        {
            var showTitle = "";
            if (!string.IsNullOrEmpty(title))
                showTitle = title;

            var index = -1;
            var source = new List<SingleOptionItem>();
            foreach (var line in buttons)
            {
                index++;
                var add = new SingleOptionItem
                {
                    OnSelected = line.OnSelected,
                    Selected = line.Selected,
                    Title = line.Title,
                    Id = index.ToString()
                };
                source.Add(add);
            }

            var picker = new MultiPicker
            {
                ItemsSource = source,
                Title = showTitle,
                Multiselect = false,
                CommandOnSubmit = new Command(async (object context) =>
                {
                    if (context is SingleOptionItem selected)
                    {
                        selected.OnSelected?.Execute(selected);
                    }
                }),
            };
            picker.Show();
        }

        public static void PresentForSingleOption(IEnumerable<SingleOptionItem> source, string title = null)
        {
            var showTitle = "";
            if (!string.IsNullOrEmpty(title))
                showTitle = title;

            var picker = new MultiPicker
            {
                ItemsSource = source,
                Title = showTitle,
                Multiselect = false,
                CommandOnSubmit = new Command(async (object context) =>
                {
                    if (context is SingleOptionItem selected)
                    {
                        selected.OnSelected?.Execute(selected);
                    }
                }),
            };
            picker.Show();
        }

        public static void PresentForSingleOption(IList<OptionItem> source, ICommand onSubmit, string title = null)
        {
            var showTitle = "";
            if (!string.IsNullOrEmpty(title))
                showTitle = title;

            var picker = new MultiPicker
            {
                ItemsSource = source,
                Title = showTitle,
                Multiselect = false,
                CommandOnSubmit = new Command(async (object context) =>
                {
                    var selected = context as OptionItem;
                    onSubmit?.Execute(selected);
                }),
            };
            picker.Show();
        }

        private double _HotspotTopHeight;
        public double HotspotTopHeight
        {
            get { return _HotspotTopHeight; }
            set
            {
                if (_HotspotTopHeight != value)
                {
                    _HotspotTopHeight = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _HotspotBottomHeight;
        public double HotspotBottomHeight
        {
            get { return _HotspotBottomHeight; }
            set
            {
                if (_HotspotBottomHeight != value)
                {
                    _HotspotBottomHeight = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _HotspotLeftWidth;
        public double HotspotLeftWidth
        {
            get { return _HotspotLeftWidth; }
            set
            {
                if (_HotspotLeftWidth != value)
                {
                    _HotspotLeftWidth = value;
                    OnPropertyChanged();
                }
            }
        }


        private double _HotspotLeftHeight;
        public double HotspotLeftHeight
        {
            get { return _HotspotLeftHeight; }
            set
            {
                if (_HotspotLeftHeight != value)
                {
                    _HotspotLeftHeight = value;
                    OnPropertyChanged();
                }
            }
        }


 
    }
}