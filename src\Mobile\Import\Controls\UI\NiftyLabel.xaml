﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyLabel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui">

    <ContentView.Content>
        <gestures:LegacyGesturesStackLayout
            Down="ControlContainer_OnDown"
            LongPressing="OnShared"
            Panning="StackLayout_OnPanning"
            Spacing="0"
            Tapped="Grid_OnTapped"
            Up="ControlContainer_OnUp">

            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="Grid_OnTapped" />
            </StackLayout.GestureRecognizers>


            <Grid
                x:Name="ControlContainer"
                Padding="8,6,8,0"
                ColumnSpacing="8"
                RowSpacing="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="24" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <!--  selection box  -->
                <BoxView
                    x:Name="sel"
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="FillAndExpand" />

                <!--  background wave image  -->

                <!--  column 0 - ICON  -->
                <appoMobi:CImage
                    x:Name="ControlLabelIcon"
                    Grid.Row="0"
                    Grid.Column="0"
                    Aspect="AspectFit"
                    DownsampleToViewSize="False"
                    HeightRequest="18"
                    HorizontalOptions="End"
                    IsOpaque="True"
                    VerticalOptions="Center"
                    WidthRequest="18">
                    <appoMobi:CImage.Transformations>
                        <transformations:TintTransformation EnableSolidColor="True" HexColor="{x:Static appoMobi:AppColors.controls_dark}" />
                    </appoMobi:CImage.Transformations>
                </appoMobi:CImage>
                <!--  column 1 - LABEL  -->
                <Label
                    x:Name="ControlLabelText"
                    Grid.Row="0"
                    Grid.Column="1"
                    Margin="2,0,0,0"
                    TextColor="{x:Static appoMobi:AppColors.AccentDark75}"
                    FontSize="18"
                    FontFamily="ui"
                    VerticalOptions="Center" />


                <BoxView              x:Name="MyDivider"
                                      Grid.Row="1"
                                      Grid.Column="0"
                                      Grid.ColumnSpan="2"
                                      IsVisible="False"
                 
                    BackgroundColor="#C8C7CC"
                    HeightRequest="0.75" />

    

                <!--  NIFTY SHARE No ICON  -->

                <!--  columns 0-1 - TEXT  -->
                    <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        x:Name="ControlLabelDesc"
                        IsVisible="False"                        
                        Margin="11,12,8,7"
                        InputTransparent="True"
                        FontSize="14.5"              TextColor="{x:Static appoMobi:AppColors.BwGrey}"              
                        VerticalOptions="Center" />
            </Grid>

        </gestures:LegacyGesturesStackLayout>
    </ContentView.Content>
</ContentView>