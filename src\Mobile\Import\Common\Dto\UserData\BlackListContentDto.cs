﻿using AppoMobi.Framework.Api;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class ApiRequestPlayersDto : ApiRequestPagedPeopleDto
    {
        public bool InFavs { get; set; }

        public bool InBlacklist { get; set; }

        /// <summary>
        /// This is used to get the minimum price for rate
        /// </summary>
        public ProUserType Type { get; set; }
        /// <summary>
        /// The profile im requesting from
        /// </summary>
        public string Profile { get; set; }
    }


    /// <summary>
    /// Id - key of content to be blocked
    /// </summary>
    public class BlackListContentDto : WithIdDto
    {
        /// <summary>
        /// As title
        /// </summary>
        public BlacklistContentType Type { get; set; }

        ///// <summary>
        ///// Key of content, like event, poll etc, can leave blank for blacklisting player
        ///// </summary>
        //public string ContentKey { get; set; }

        /// <summary>
        /// Block or unblock content
        /// </summary>
        public bool On { get; set; }
    }
}
