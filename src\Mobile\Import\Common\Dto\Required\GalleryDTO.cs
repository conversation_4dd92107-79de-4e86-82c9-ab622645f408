﻿using System.Collections.Generic;

namespace AppoMobi.Common.Dto.Required
{
    //===================================================================
    public class GalleryDTO
    //===================================================================
    {
        public string Id { get; set; } //hello UID
        public string Keywords { get; set; } //NEW
        public string Title { get; set; } //todo
        public string Subtitle { get; set; } //todo
        public string Desc { get; set; }

        public List<GalleryImageDTO> Images { get; set; } //taged list
        
        public bool New { get; set; } //todo
        public int Priority { get; set; } //for news

        public string ShareLink { get; set; } //for sharing!

        public string ImageId { get; set; }
        public string ImageColor { get; set; }

        
        public GalleryDTO()
        
        {
            Images = new List<GalleryImageDTO>();
            ShareLink = "";
            New = false;
        }

    }
    //===================================================================
}
