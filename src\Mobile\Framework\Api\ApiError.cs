﻿namespace AppoMobi.Framework.Api
{
    public class ApiError : IWithErrorDto
    {
        public ApiError(int code, string message)
        {
            ErrorCode = code;
            ErrorMessage = message;
        }

        public ApiError()
        {
        }

        public int? ErrorCode { get; set; }

        public string ErrorMessage { get; set; }

        public string ErrorDetails { get; set; }
    }


    public class WithIdAndCodeDto : WithIdDto
    {
        public string Code { get; set; }
    }


    public class TagsWithIdDto : WithIdDto
    {
 

        public string Tags { get; set; }
    }
}