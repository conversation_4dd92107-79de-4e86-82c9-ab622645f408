﻿using System;
//using System.ComponentModel.DataAnnotations;


namespace AppoMobi.Common.Dto.Appo
{


    //*************************************************************

    //*************************************************************
    public class BookableIntervalDto : AppoTimeInterval
    //*************************************************************
    {
        public string Id { get; set; }
        public int SeatsTaken { get; set; }
        public string PublicNotes { get; set; }

    
    public BookableIntervalDto(DateTime? start, DateTime? end)
        
    {
            Id = Guid.NewGuid().ToString();
            TimeStart = start;
            TimeEnd = end;
        }

    
    public BookableIntervalDto()
        
    {
        Id = Guid.NewGuid().ToString();
    }


    }



    

    //*************************************************************

    //*************************************************************

    //*************************************************************

    

    //===================================================================
}
