﻿using System;
using System.Diagnostics;
using Android.Views;
using Math = Java.Lang.Math;

namespace AppoMobi.Droid.Camera.Views
{
    public class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener
    {
        public float ScaleLimitMin { get; set; } = 0.1f;

        public float ScaleLimitMax { get; set; } = 10.0f;

        public float ScaleFactor { get; set; } = 1.0f;

        public override bool OnScaleBegin(ScaleGestureDetector detector)
        {
            return base.OnScaleBegin(detector);
        }

        public override void OnScaleEnd(ScaleGestureDetector detector)
        {
            base.OnScaleEnd(detector);
        }

        public EventHandler<EventArgsScale> OnScaleEvent;

        public override bool OnScale(ScaleGestureDetector scaleGestureDetector)
        {
            if (scaleGestureDetector == null)
                return base.OnScale(scaleGestureDetector);

            var scale = ScaleFactor * scaleGestureDetector.ScaleFactor;
            ScaleFactor = Math.Max(ScaleLimitMin, Math.Min(scale, ScaleLimitMax));

            Debug.WriteLine($"[ZOOM] Scale: {ScaleFactor}");

            OnScaleEvent?.Invoke(this, new EventArgsScale{Scale = ScaleFactor});
         
            //mImageView.setScaleX(mScaleFactor);
            //mImageView.setScaleY(mScaleFactor);

            return true;
        }
    }
}