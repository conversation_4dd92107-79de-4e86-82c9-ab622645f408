using Android.Views;
using AppoMobi.Touch;
using System;
using Xamarin.Forms;
using View = Android.Views.View;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidPanEventArgs : PanEventArgs
	{
		public AndroidPanEventArgs(MotionEvent previous, MotionEvent current, PanEventArgs prevArgs, View view)
		{
			this.Cancelled = current.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(current);
			if (prevArgs == null)
			{
				Point[] touches = AndroidEventArgsHelper.GetTouches(previous);
				if ((int)touches.Length != (int)this.Touches.Length)
				{
					this.DeltaDistance = Point.Zero;
				}
				else
				{
					Point point = touches.Center();
					this.DeltaDistance = this.Center.Subtract(point);
				}
				this.TotalDistance = this.DeltaDistance;
			}
			else
			{
				base.CalculateDistances(prevArgs);
			}
			this.Velocity = this.GetVelocity(previous, current, prevArgs);
		}

		private Point GetVelocity(MotionEvent previous, MotionEvent current, PanEventArgs prevArgs)
		{
			if (previous == null)
			{
				return new Point(0, 0);
			}
			if (this.DeltaDistance.X == 0 && this.DeltaDistance.Y == 0)
			{
				if (prevArgs != null)
				{
					return prevArgs.Velocity;
				}
				return new Point(0, 0);
			}
			Point deltaDistance = this.DeltaDistance;
			double eventTime = (double)(current.EventTime - previous.EventTime);
			return new Point(deltaDistance.X * 1000 / eventTime, deltaDistance.Y * 1000 / eventTime);
		}
	}
}