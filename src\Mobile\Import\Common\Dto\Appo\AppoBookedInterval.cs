﻿using Newtonsoft.Json;
using AppoMobi.Common.Enums.Appo;

namespace AppoMobi.Common.Dto.Appo
{
    public class AppoBookedInterval : AppoTimeInterval
        //*************************************************************
    {
        public string Id { get; set; }
        
        public BookingStatus Status { get; set; }
        
        public string ServiceId { get; set; }
        
        public string ObjectId { get; set; }

        public string CustomNotes { get; set; }

        [JsonIgnore]
        
        public string TimeDesc
            
        {
            get
            {
                if (TimeStart != null)
                {
                    var time = TimeStart.Value.ToShortTimeString();
                    if (Status == BookingStatus.Unknown || Status== BookingStatus.Pending)
                    {
                        //confirmation pending
                        var ret = string.Format("{0} (Не подтверждено)", time);
                        return ret;
                        //return string.Format(ResStrings.AppoTimeDescPending, time);
                    }
                    else
                    {
                        //confirmed
                        return time;
                    }
                }

                return "";
            }

        }

    }
}