﻿using System;

namespace AppoMobi.Framework.Attributes
{
    /// <summary>
    /// Permissions who can see it in Editor and edit
    /// usually used only for some system stuff like
    /// database recods locks and such
    /// Param: Separate roles with comma.
    /// </summary>
    //================================================
    public class Editor : Attribute
        //================================================
    {
        public bool LineBefore { get; set; }
        
        public bool LineAfter { get; set; }
        
        public bool SetId { get; set; }
        
        public string Link { get; set; }

        public string Roles { get; set; }

        public string Template { get; set; }
        public string TemplateParameters { get; set; }
    }
}