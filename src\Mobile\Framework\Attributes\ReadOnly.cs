﻿using System;

namespace AppoMobi.Framework.Attributes
{
    public class ReadOnly : Attribute
    {
        /// <summary>
        /// If Readonly is set, still can edit if model Id = null (new)
        /// </summary>
        public bool ExcludeForNew { get; set; }

        public string ExcludeForRoles { get; set; } 

        public ReadOnly(string roles="")
        {
            ExcludeForRoles = roles;
        }
    }
}