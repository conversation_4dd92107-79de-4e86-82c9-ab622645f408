﻿using System;


namespace AppoMobi.Video
{
 

    public class VideoView : View
    {
        public Action StopAction;
        public VideoView()
        {

        }

        public static readonly BindableProperty FileSourceProperty = BindableProperty.Create(nameof(FileSource),
        typeof(string),
        typeof(VideoView),
        string.Empty, 
        
        BindingMode.TwoWay, null, (bindable, value, newValue) =>
        {
            //todo
        });
        public string FileSource
        {
            get { return (string)GetValue(FileSourceProperty); }
            set { SetValue(FileSourceProperty, value); }
        }

        public void Stop()
        {
            if (StopAction != null)
                StopAction();
        }
    }
}
