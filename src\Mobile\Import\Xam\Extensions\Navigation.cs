﻿

namespace AppoMobi.Xam
{
 

    public static class NavigationExtensions
    {
        //
        //public static async Task SafePop(this IPopupNavigation This, bool animate = true)
        //
        //{
        //    //todo check if alpha exists!!!!!!
        //    if (This.PopupStack.Count > 0)
        //        await This.PopAsync(animate);
        //}

        
        public static bool Contains(this IReadOnlyList<Page> This, Type type)
        
        {
            //todo check if alpha exists!!!!!!
            foreach (var page in This)
            {
                if (page.GetType() == type)
                {
                    return true;                  ;
                }
            }
            return false;
        }
    }
}
