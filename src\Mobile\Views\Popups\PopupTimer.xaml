﻿<?xml version="1.0" encoding="utf-8" ?>

<xam:PopupDialogBase
    x:Class="AppoMobi.PopupTimer"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Controls"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    OverlayColor="{x:Static appoMobi:AppColors.PopupOverlay}"
    VerticalOptions="Fill"
    Color="Transparent">

    <!--<pages:PopupPage.Animation>
        <animations:ScaleAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"
            PositionIn="Center"
            PositionOut="Center"
            ScaleIn="1.0"
            ScaleOut="0.5" />
    </pages:PopupPage.Animation>-->

    <Grid>

        <views:Canvas
            HorizontalOptions="FillAndExpand"
            InputTransparent="True"
            VerticalOptions="FillAndExpand">

            <xam:SkiaScreenshot HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <views:SkiaControl.VisualEffects>

                    <views:BlurEffect Amount="4" />
                    <views:TintEffect Color="#CC000000" />

                </views:SkiaControl.VisualEffects>
            </xam:SkiaScreenshot>

        </views:Canvas>

        <!--<BoxView
            VerticalOptions="Start"
            HeightRequest="20" BackgroundColor="Red" HorizontalOptions="Fill"/>
        <BoxView
            Margin="0,20,0,0"
            VerticalOptions="Start"
            HeightRequest="20" BackgroundColor="Green" HorizontalOptions="Fill"/>

        <BoxView
            Margin="0,0,0,20"
            VerticalOptions="End"
            HeightRequest="20" BackgroundColor="Green" HorizontalOptions="Fill"/>
        <BoxView
            VerticalOptions="End"
            HeightRequest="20" BackgroundColor="Red" HorizontalOptions="Fill"/>-->

        <gestures:LegacyGesturesFrame
            x:Name="ControlMessageContainer"
            Margin="20,0,20,10"
            Padding="8"
            BackgroundColor="{x:Static xam:BackColors.GradientEnd}"
            HorizontalOptions="Center"
            Stroke="{x:Static appoMobi:AppColors.Divider}"
            StrokeShape="RoundRectangle 16,16,16,16"
            StrokeThickness="1"
            Tapped="OnCLicked_Cancel"
            VerticalOptions="Center"
            WidthRequest="300">

            <StackLayout
                Margin="0,0,0,7"
                HorizontalOptions="Fill"
                InputTransparent="True"
                Spacing="16">

                <!--  message  -->


                <Label
                    x:Name="cCountdown"
                    Margin="10,10,10,0"
                    FontSize="{x:Static xam:FontSizes.Largest}"
                    HorizontalOptions="FillAndExpand"
                    HorizontalTextAlignment="Center"
                    Text="{Binding Countdown.RemainTimeFullText}"
                    TextColor="{x:Static xam:BackColors.CalculatorButtonOperator}" />

                <Label
                    x:Name="cMessage"
                    Margin="10"
                    FontSize="{x:Static xam:FontSizes.Small}"
                    HorizontalOptions="FillAndExpand"
                    HorizontalTextAlignment="Center"
                    Text="{Binding Message}"
                    TextColor="White" />

                <!--  hidden buttons just in case  -->
                <Grid HorizontalOptions="FillAndExpand" IsVisible="False">

                    <StackLayout
                        HorizontalOptions="Fill"
                        Orientation="Horizontal"
                        Spacing="16">

                        <!--  Button  -->
                        <controls:CButton
                            x:Name="btnYes"
                            Margin="0,5,0,0"
                            Tapped="CardInterface_OnDown"
                            Text="Start Contdown" />

                        <!--  Button  -->
                        <controls:CButton
                            x:Name="btnNo"
                            Margin="0,5,0,0"
                            Tapped="OnCLicked_Cancel"
                            Text="Close" />

                    </StackLayout>

                </Grid>

            </StackLayout>


        </gestures:LegacyGesturesFrame>
    </Grid>


</xam:PopupDialogBase>