﻿using System;
using System.Globalization;

using AppoMobi.Xam.Converters;

namespace AppoMobi.Forms.Xaml
{
    public class CompareIntegersConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var ret = true;

            try
            {
                if (value is Enum)
                {
                    int intVar = (int)value.GetType().GetField("value__").GetValue(value);

                    if (intVar == parameter.ToString().ToInteger())
                        return true;
                }

            }
            catch (Exception e)
            {

            }

            try
            {
                var iVisualStep = int.Parse((string) parameter);
                var iStep = (int) value;
                if (iStep != iVisualStep)
                    ret = false;
                return ret;
            }
            catch (Exception e)
            {
            }

            try
            {
                ret = false;
                var ints = ((string)parameter).Split(',');
                foreach (var number in ints)
                {
                    var iStep = int.Parse(number);
                    if (iStep == (int)value)
                    {
                        ret = true;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
            }

            return ret;
        }


    }

    public class CompareEnum : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var ret = true;

            try
            {
                var iVisualStep = int.Parse((string)parameter);
                
                var iStep = (int)value;

                if (iStep != iVisualStep)
                    ret = false;

                return ret;
            }
            catch (Exception e)
            {
            }

            try
            {
                ret = false;
                var ints = ((string)parameter).Split(',');
                foreach (var number in ints)
                {
                    var iStep = int.Parse(number);
                    if (iStep == (int)value)
                    {
                        ret = true;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
            }

            return ret;
        }


    }

    public class CompareIntegersNotConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var ret = true;

            try
            {
                var iVisualStep = int.Parse((string)parameter);
                var iStep = (int)value;
                if (iStep == iVisualStep)
                    ret = false;
                return ret;
            }
            catch (Exception e)
            {
            }

            try
            {
                ret = false;
                var ints = ((string)parameter).Split(',');
                foreach (var number in ints)
                {
                    var iStep = int.Parse(number);
                    if (iStep == (int)value)
                    {
                        ret = false;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
            }

            return ret;
        }


    }

    //public class ClientResultColorConverter : ConverterBase
    //{
    //    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        var color = Colors.Transparent;
    //        try
    //        {
    //            if (value != null)
    //            {
    //                var result = (VacancyResponceResultType)value;
    //                if (result == VacancyResponceResultType.VacancyApproved)
    //                    color = Colors.Green.MultiplyAlpha(0.1f);
    //                else
    //                if (result == VacancyResponceResultType.VacancyRejected)
    //                    color = Colors.DarkGray.MultiplyAlpha(0.1f);
    //            }
    //        }
    //        catch (Exception e)
    //        {
    //        }
    //        return color;
    //    }
    //}
}
