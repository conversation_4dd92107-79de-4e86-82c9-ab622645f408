﻿namespace AppoMobi.Main
{
    public class AppButton : SkiaButton
    {
        public AppButton(string caption)
        {
            CornerRadius = 12;
            HeightRequest = 38;
            WidthRequest = 150;
            Text = caption;
            //BackgroundColor = Colors.Transparent;
            BackgroundColor = Colors.White;
            TextColor = Colors.Black;
            FontFamily = "FontText";
        }

        public override void ApplyProperties()
        {
            base.ApplyProperties();

            if (MainFrame != null)
            {
                MainFrame.StrokeWidth = 1;
                MainFrame.StrokeColor = Colors.Black;
            }
        }
    }
}