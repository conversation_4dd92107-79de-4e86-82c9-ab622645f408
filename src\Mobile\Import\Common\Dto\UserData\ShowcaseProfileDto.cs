﻿using AppoMobi.Framework.Api;
using Newtonsoft.Json;

namespace AppoMobi.Common.Dto.UserData
{

    public class ShowcaseProfileDto : BaseFrameworkDto
    {
        /// <summary>
        /// QR-code
        /// </summary>
        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("role")]
        public string Role { get; set; }

        [JsonProperty("fullname")]
        public string Fullname { get; set; }

        [JsonProperty("fname")]
        public string FirstName { get; set; }

        [JsonProperty("sex")]
        public int Gender { get; set; }

        [JsonProperty("age")]
        public int Age { get; set; }

        [JsonProperty("ava")]
        public string Avatar { get; set; }

        [JsonProperty("lang")]
        public string NativeLanguage { get; set; }

        [JsonProperty("flag")]
        public string NativeLanguageFlag { get; set; }

        [JsonProperty("langs")]
        public string Languages { get; set; }

        [JsonProperty("teachflag")]
        public string LanguagesMainFlag { get; set; }

        [JsonProperty("about")]
        public string AboutMe { get; set; }

        [JsonProperty("price")]
        public decimal MinutePrice { get; set; }

        [JsonProperty("reviews")]
        public int ReviewsCount { get; set; }

        [JsonProperty("rating")]
        public double CalculatedRating { get; set; }

        [JsonProperty("ratesroles")]
        public string RatesRoles { get; set; }

        [JsonProperty("roled")]
        public string RoleDescription { get; set; }

        [JsonProperty("priced")]
        public string PriceDescription { get; set; }

        [JsonProperty("langsd")]
        public string LanguagesDescription { get; set; }
    }
}
