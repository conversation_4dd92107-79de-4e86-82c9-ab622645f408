﻿using System;
using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;

namespace AppoMobi.Common.Dto
{
    public class NewsDTO
    {
        public string Id { get; set; }
        //[Display(Name="Автор")]
        public string Author { get; set; }
        public string Title { get; set; }
        public string Text { get; set; }
        public string Image { get; set; }
        public string Action { get; set; } //www, mail etc
        public string Params { get; set; } //url etc

        //[DataType(DataType.DateTime)]
        //[DisplayFormat(ApplyFormatInEditMode = true, DataFormatString = @"{0:dd\/MM\/yyyy}")]

        public DateTime DateTime { get; set; }
        //[Display(Name = "Ширина")]
        public double ImageWidth { get; set; }
        //[Display(Name = "Автор")]
        public double ImageHeight { get; set; }


        public double ImageDynamicHeight { get; set; }
        //last position!!!!!
        public bool NewsVisible { get; set; } = true;

        public int Priority { get; set; }
    }


    //===================================================================
    public class JsonNewsList
    //===================================================================
    {
        public List<NewsDTO> news { get; set; }
        public string status { get; set; }
        public string code { get; set; }
        public int count { get; set; }
        public double version { get; set; }
        public double export { get; set; }
    }


}
