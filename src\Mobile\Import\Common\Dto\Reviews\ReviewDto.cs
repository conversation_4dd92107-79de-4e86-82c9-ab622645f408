﻿using System;
using AppoMobi.Framework.Api;
using AppoMobi.Common.Dto.UserData;

namespace AppoMobi.Common.Dto.Reviews
{
    public class ReviewDto : BaseFrameworkDto
    {
        public ReviewDto()
        {
            //Author = new OnlineUserDetailsDto();
        }

        public int Rating { get; set; }

        public string Profile { get; set; }

        public string Message { get; set; }

        public DateTime? MessageTime { get; set; }
        
        public OnlineUserDetailsDto Author { get; set; }

        public string Answer { get; set; }

        public DateTime? AnswerTime { get; set; }

    }
}