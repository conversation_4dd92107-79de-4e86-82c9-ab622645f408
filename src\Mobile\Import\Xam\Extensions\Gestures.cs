﻿using System;
using System.Threading.Tasks;


namespace AppoMobi.Xam.Extensions
{
    //***************************************************************************
    public static partial class GesturesExtensions
    //***************************************************************************
    {


        public static void DisableSpamClicks(this View This, int milliseconds = 1500, double reduceOpacity = 0.15)
        {
            if (DeviceInfo.Current.Platform != DevicePlatform.Android)
                return;

            var thisOpacity = This.Opacity;
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                This.InputTransparent = true;
                This.Opacity = thisOpacity - reduceOpacity;
                Task.Delay(1).Wait(); //update ui
            });

            Device.StartTimer(TimeSpan.FromMilliseconds(milliseconds), () =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    This.InputTransparent = false;
                    This.Opacity = thisOpacity;
                    Task.Delay(1).Wait(); //update ui
                });
                return false;
            });
        }

    }
}
