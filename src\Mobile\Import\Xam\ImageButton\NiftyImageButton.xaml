﻿<?xml version="1.0" encoding="UTF-8" ?>
<Frame
    MinimumHeightRequest="40"
    x:Class="AppoMobi.Xam.NiftyImageButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    Padding="0"
    BackgroundColor="Transparent"
    HasShadow="False"
    BorderColor="Transparent"
    CornerRadius = "5"
    
    IsClippedToBounds="True"
    VerticalOptions="StartAndExpand">

    <gestures:LegacyGesturesGrid      
               Down="NiftyImageButton_OnDown"
        Tapped="OnTapped_Frame"

        Margin="0,4,0,4"
            x:Name="ControlLayout"
            Padding="0"
            ColumnSpacing="8"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!--left aligned image-->
        <forms:CachedImage
                FadeAnimationEnabled="False"
                Error="OnImageErrors"
                x:Name="ControlImageLeft"
                Grid.Column="0"
                Aspect="Fill"
                HorizontalOptions="End"
                IsVisible="False"
                VerticalOptions="Center" />

        <!--centered stack-->
        <StackLayout         
                Orientation="Horizontal"
                Grid.Column="1"
                HorizontalOptions="Center"
                VerticalOptions="Center"                
                Spacing="4"
                x:Name="stackCenter">

            <!--left accessory image-->
            <forms:CachedImage
                    FadeAnimationEnabled="False"                    
                    Error="OnImageErrors"
                    x:Name="ControlImageLeftAccessory"
                    Aspect="AspectFit"                    
                    IsVisible="False"
                    VerticalOptions="Center" />
            <Label
                    x:Name="ControlLabel"
                    HorizontalTextAlignment="Center"
                    VerticalTextAlignment="Center" />
            <!--right accessory image-->
            <forms:CachedImage
                    FadeAnimationEnabled="False"                    
                    Error="OnImageErrors"
                    x:Name="ControlImageRightAccessory"
                    Aspect="AspectFit"
                    IsVisible="False"
                    VerticalOptions="Center" />
        </StackLayout>

        <!--right aligned image-->
        <forms:CachedImage
                FadeAnimationEnabled="False"                
                Error="OnImageErrors"
                x:Name="ControlImageRight"
                Grid.Column="2"
                Aspect="Fill"
                HorizontalOptions="Start"
                IsVisible="False"
                VerticalOptions="Center"/>


    </gestures:LegacyGesturesGrid>


</Frame>