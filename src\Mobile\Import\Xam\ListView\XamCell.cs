﻿using System;


namespace AppoMobi.Xam
{

    //*********************************************************
    public class XamCell : ViewCell
    //*********************************************************
    {
        #region Event Handlers

       

        #endregion


        protected bool Disposed;



        public double ParentWidth { get; set; }

        public bool IsScrolling { get; protected set; }
        public bool Visible { get; protected set; }
        public string Uid { get; set; }

        //public void ShowImages { get; set; }
        public virtual void ShowImages()
        {

        }

        public virtual void ProcessSizeUpdating(double parentWidth)
        {

        }
        

        public void SetRefreshBinding(string propertyName)
        {
            this.SetBinding(SetToRefreshProperty, propertyName);
        }

        public virtual void OnRefresh()
        {

        }

        public virtual void OnDispose()
        {

        }

        public void Dispose()
        {
            OnDispose();
        }

        //-------------------------------------------------------------
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        //-------------------------------------------------------------
        {
            base.OnPropertyChanged(propertyName);

            //refresh cell
            if (propertyName == nameSetToRefresh)
            {
                OnRefresh();
            }
        }

        //-------------------------------------------------------------
        // SetToRefresh
        //-------------------------------------------------------------
        private const string nameSetToRefresh = "SetToRefresh";

        public static readonly BindableProperty SetToRefreshProperty =
            BindableProperty.Create(nameSetToRefresh, typeof(int), typeof(XamCell), 0); //, BindingMode.TwoWay

        public int SetToRefresh
        {
            get { return (int) GetValue(SetToRefreshProperty); }
            set { SetValue(SetToRefreshProperty, value); }
        }

        public XamCell()
        {
            Uid = Guid.NewGuid().ToString();
        }
    }
}