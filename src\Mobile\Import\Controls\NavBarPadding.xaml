﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.UI.NavBarPadding"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:nifty="clr-namespace:AppoMobi.Xam"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    VerticalOptions="Start">
    <ContentView.Content>

        <BoxView
            x:Name="boxControl"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Start" />

    </ContentView.Content>
</ContentView>