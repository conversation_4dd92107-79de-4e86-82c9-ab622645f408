﻿
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

using FFImageLoading.Maui;


namespace AppoMobi.Nifty
{


    public class NiftyNewsList : Microsoft.Maui.Controls.ContentView
    {
        Microsoft.Maui.Controls.Grid MyGrid = new Microsoft.Maui.Controls.Grid();
        private int SelectedRow = -1;
        private AppoMobi.Touch.LegacyGesturesBoxView SelectionBox = null;
        private Color OldColor;
        private CachedImage SelectedIcon = null;



        public NiftyNewsList()
        {


            RedrawList();

        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameList:
                    RedrawList();//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;

                //                case nameLabelIcon:
                //                    ControlLabelIcon.Source = LabelIcon;
                //                    break;
            }

        }


        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(NiftyNewsList), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }

        
        // SelectedParams
        
        private const string nameSelectedParams = "SelectedParams";
        public static readonly BindableProperty SelectedParamsProperty = BindableProperty.Create(nameSelectedParams, typeof(string), typeof(NiftyNewsList), string.Empty);
        public string SelectedParams
        {
            get { return (string)GetValue(SelectedParamsProperty); }
            set { SetValue(SelectedParamsProperty, value); }
        }


        
        // List
        
        private const string nameList = "List";
        public static readonly BindableProperty ListProperty = BindableProperty.Create(nameList, typeof(IEnumerable), typeof(NiftyNewsList), null);
        public IEnumerable List
        {
            get { return (IEnumerable)GetValue(ListProperty); }
            set { SetValue(ListProperty, value); }
        }




        
        // Tapped
        
        public event EventHandler Tapped = null;
        //private async void OnTapped(object sender, EventArgs e)
        //{
        //  Tapped?.Invoke(this, EventArgs.Empty);
        //}


        //---------------------------------------------------------
        public class MySel : AppoMobi.Touch.LegacyGesturesBoxView
            //---------------------------------------------------------
        {
            public string Tag { get; set; }
            public int Position { get; set; }
            public string Params { get; set; }
            public AppoMobi.Touch.LegacyGesturesBoxView SelectionBox { get; set; }
            public CachedImage SelectedIcon { get; set; }

            public MySel()
            {
                Tag = "";

                Position = -1;
                SelectionBox = null;
            }

        }


        //---------------------------------------------------------
        private void RedrawList()
            //---------------------------------------------------------
        {
            if (List == null) return;
            //cleanup
            MyGrid.Children.Clear();

            CachedImage img;
            Microsoft.Maui.Controls.Label lab;

            Microsoft.Maui.Controls.BoxView sep;
            MySel sel;


            //setup new template table
            var MyTable = new Microsoft.Maui.Controls.Grid();


            List<CMyListItem> items = (List<CMyListItem>)List;

            int row = -1;
            for (int a = 0; a < items.Count; a++)
            {
                row++;
                //separator

                sel = new MySel();
                img = new CachedImage();
                lab = new Microsoft.Maui.Controls.Label();


                //selection box
                sel.Tag = items[a].Tag;
                sel.Params = items[a].Parameters;
                sel.HorizontalOptions = LayoutOptions.FillAndExpand;
                sel.VerticalOptions = LayoutOptions.FillAndExpand;
                sel.Margin = new Thickness(8, 0, 8, 0);
                sel.Color = Colors.Transparent;
                OldColor = sel.Color;
                sel.Position = row;
                sel.SelectionBox = sel;
                sel.SelectedIcon = img;

                MyGrid.Add(sel, 0, row);
                Microsoft.Maui.Controls.Grid.SetColumnSpan(sel, 2);


                //image
                img.InputTransparent = true;
                img.Source = items[a].Image;
                img.HeightRequest = img.WidthRequest = 24;
                img.DownsampleToViewSize = true;
                img.VerticalOptions = LayoutOptions.Center;
                img.Margin = new Thickness(16, 16, 0, 16);
                MyGrid.Add(img, 0, row);

                //label
                lab.InputTransparent = true;
                lab.FontSize = 14.5;
                lab.TextColor = Color.Parse("747575");
                lab.VerticalOptions = LayoutOptions.Center;
                lab.Margin = new Thickness(0, 16, 17, 16);
                //todo style!!!!!!
                lab.Text = items[a].Desc;
                MyGrid.Add(lab, 1, row);



                MyGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                //hello gestures

                /*
                //---------------------------------------------------------
                sel.LongPressed += (s, e) =>
                //---------------------------------------------------------
                {
                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    cc = (Color)Application.Current.Resources["primary"];//.FromHex("#c7ac56").MultiplyAlpha(0.1);
                    cc = cc.MultiplyAlpha(0.05);
                    AppoMobi.Touch.BoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OldColor;
                        }
                        SelectionBox = sel1;
                        sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        //MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));

                    }

                };
                
                
                */



                //---------------------------------------------------------
                sel.Down += (s, e) =>
                    //---------------------------------------------------------
                {
                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    //                    cc = Color.Parse("#c7ac56").MultiplyAlpha(0.1);
                    cc = AppColors.Primary;//.FromHex("#c7ac56").MultiplyAlpha(0.1);
                    cc = cc.MultiplyAlpha(0.05f);

                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OldColor;
                        }
                        OldColor = sel1.Color;
                        SelectionBox = sel1;
                        sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        //Grow(ss.SelectedIcon);
                    }

                };


                //gesture recognizer
                //var tapGestureRecognizer = new TapGestureRecognizer();
                //---------------------------------------------------------
                //tapGestureRecognizer.Tapped += (s, e) => 
                sel.Tapped += async (s, e) =>
                    //---------------------------------------------------------
                {
                    // handle the tap
                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    cc = (Color)Application.Current.Resources["accent"];//.FromHex("#c7ac56").MultiplyAlpha(0.1);
                    cc = cc.MultiplyAlpha(0.2f);
                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OldColor;
                        }
                        OldColor = sel1.Color;
                        SelectionBox = sel1;
                        //sel1.Color=OldColor;
                        sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        SelectedParams = ss.Params;
                        Tag = ss.Tag;
                        //MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        await Task.Delay(300);
                        Tapped?.Invoke(this, EventArgs.Empty);

                    }

                };
                //sel.GestureRecognizers.Add(tapGestureRecognizer);



                //separator
                // 
                if (a == items.Count - 1) //last row
                    continue;

                row++;
                sep = new Microsoft.Maui.Controls.BoxView();
                sep.HorizontalOptions = LayoutOptions.FillAndExpand;
                sep.VerticalOptions = LayoutOptions.Start;
                sep.HeightRequest = 1.0;
                sep.Margin = new Thickness(16, 0, 16, 0);
                sep.Color = Color.Parse("#e3e4e4");//dcdcdf
                //e3e4e4  //android listview
                //(Color)Application.Current.Resources["ListSeparator"];
                MyGrid.Add(sep, 0, row);
                Microsoft.Maui.Controls.Grid.SetColumnSpan(sep, 2);
                MyGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Absolute) });
            }

            Content = MyGrid;

        }

        bool _growbusy = false;
        public async Task Grow(CachedImage myobject)
        {
            //            if (_growbusy) return;

            _growbusy = true;
            //myobject.Transformations.Add(new TintTransformation("c7ac56"));
            try
            {
                await myobject.ScaleTo(1.5, 75);
                await myobject.ScaleTo(1.0, 75);
                await myobject.ScaleTo(1.35, 75);
                await myobject.ScaleTo(1.0, 75);
            }
            catch
            {

            }
            //myobject.Transformations.Clear();
            //await myobject.ScaleTo(1.4, 75);
            //await myobject.ScaleTo(1.0, 75);

            /*
                        //MainThread.BeginInvokeOnMainThread(() =>
                        //{
                        try
                        {
                                myobject.ScaleTo(1.4, 75).ContinueWith((t) =>
                                    {
                                        try
                                        {
                                            myobject.ScaleTo(1.0, 75);
                                        }
                                        catch
                                        {

                                        }
                                    },
                                    scheduler: TaskScheduler.FromCurrentSynchronizationContext());
                            }
                            catch
                            {

                            }
                            _growbusy = false;
                    //    }
                    //);
              */
        }

    }
}
