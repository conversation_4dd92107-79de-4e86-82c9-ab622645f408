﻿<?xml version="1.0" encoding="UTF-8"?>
<gestures:LegacyGesturesGrid xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:gestures="clr-namespace:AppoMobi.Touch"
             xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:xam="clr-namespace:AppoMobi.Xam"

             IsClippedToBounds="True"

             x:Class="AppoMobi.Xam.ImageFit">


    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>

    <!--this grid gonna be resized-->
    <Grid 
        IsClippedToBounds="True"
        VerticalOptions="CenterAndExpand"
        x:Name="ImageContainer" 
        Padding="0">
        <Grid.RowDefinitions>
            <RowDefinition 
                Height="0.1"
                x:Name="rowThumbnail"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

<!--VerticalOptions="CenterAndExpand"
                HorizontalOptions="FillAndExpand"-->




<!--<xam:SmartImage
    Opacity="0"
    IsOpaque="True"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Aspect="AspectFit"
    Error="ImgImage_OnError"
Success="CachedImage_OnSuccess"
    x:Name="imgImage"
    StyleId="image" />-->


      <!--<forms:CachedImage
            Opacity="0"
            IsOpaque="True"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            Aspect="AspectFit"
            Error="ImgImage_OnError"
            Success="CachedImage_OnSuccess"
            ErrorPlaceholder="sad.png"
            LoadingPriority="Highest"
            FadeAnimationEnabled="False"
            x:Name="imgImage"
            StyleId="image" />-->

    </Grid>
    
    

</gestures:LegacyGesturesGrid>