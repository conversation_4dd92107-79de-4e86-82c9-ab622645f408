﻿using AppoMobi.Models;
using AppoMobi.Touch;
using AppoMobi.Xam;
using System;
using System.Collections.Generic;
using System.Diagnostics;


namespace AppoMobi
{
    public partial class MenuPageX
    {
        public SkiaLayout ListView { get { return listView; } }

        public List<MenuPageContentItem> masterPageItems;

        private MenuPageContentItem Selected;

        //private RootPageX Daddy { get; set; }
        public bool HasFav = false;

        private static ImageSource imgNavBarDefault { get; set; }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            //   App.Preloader.Pages.Add(new FbNews());
        }

        public MenuPageX()
        {
            InitializeComponent();

            IsRoot = true;

            Selected = null;

            BackgroundColor = Color.Parse("#ffffff");
            DarkenWallpaper(PageOptions.FadeWallpaper);
            //   imgBrandLogo.Source = "resource://AppoMobi.Mobile.Images.Brand.brandlogo.png";


        }

        public void InitMenu(List<MenuPageContentItem> Items)//.InitMenu(finalMenu, this);

        {
            //Daddy = daddy;
            masterPageItems = Items;

            listView.ItemsSource = null;

            listView.ItemsSource = masterPageItems;
            if (masterPageItems.Count > 0)
            {
                //listView.SelectedItem = masterPageItems[0];
                masterPageItems[0].Selected = true;
                Selected = masterPageItems[0];
            }

        }

        private void Grid_OnTapped(object sender, EventArgs e)
        {
            Globals.Values.AppRoot?.CloseMenu();

        }





        private void OnMenuItemSelected(object sender, SelectedItemChangedEventArgs e)

        {
            if (e.SelectedItem == null) return;
            var tmp = (MenuPageContentItem)e.SelectedItem;

            Debug.WriteLine("Menu ItemSelected");

            for (int a = 0; a < masterPageItems.Count; a++)
            {
                if (masterPageItems[a].Id == tmp.Id)
                {
                    masterPageItems[a].Selected = true;
                }
                else
                {
                    masterPageItems[a].Selected = false;
                }
            }


            return;
            //}

            //l//istView.SelectedItem = masterPageItems[b];
            //Selected = masterPageItems[b];
            //Daddy.IsPresented = false;

        }


        //---------------------------------------------------------
        public void ClickMenuItem(int Index)
        //---------------------------------------------------------
        {
            //listView.SelectedItem = null;
            //listView.SelectedItem = masterPageItems[Index];
        }

        protected override bool OnBackButtonPressed()
        {
            if (Navigation.NavigationStack.Count < 3)
            {
                //Globals.Values.AppMenu.ShowMenu();
                Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_HOME);
                return true;
            }
            else
                return false;
        }

        private void MenuTapped(object sender, EventArgs e)
        {

            var ee = new SelectedItemChangedEventArgs(null, 0);
            OnMenuItemSelected(sender, ee);

        }


        //private void OnTapped_Item(object sender, TapEventArgs e)

        //{

        //    var item = sender as MenuPageContentItem;

        //    listView.CallItemTapped(item, e);
        //}

        private void TappedClose(object? sender, SkiaControl.ControlTappedEventArgs e)
        {
            Globals.Values.AppRoot?.CloseMenu();
        }

        private void MenuItemTapped(object? sender, SkiaControl.ControlTappedEventArgs e)
        {
            Globals.Values.AppRoot?.CloseMenu();
            var control = e.Control as SkiaControl;
            var args = control.BindingContext as MenuPageContentItem;
            App.Instance.Messager.All("MenuClicked", args);
        }

        private void CellContextChanged(object? sender, EventArgs e)
        {
            var control = sender as SkiaLayout;
            if (control.BindingContext is MenuPageContentItem item)
            {
                if (item.Separator)
                {
                    control.BackgroundColor = Colors.Transparent;
                    control.HeightRequest = 10;
                }
                else
                {
                    control.HeightRequest = -1;
                    control.BackgroundColor = Colors.White;
                }
            }
        }
    }
}
