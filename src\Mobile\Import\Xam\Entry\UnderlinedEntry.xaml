﻿<?xml version="1.0" encoding="UTF-8" ?>
<xam:CGrid
    x:Class="AppoMobi.Xam.UnderlinedEntry"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:input="clr-namespace:AppoMobi.Forms.Controls.Input"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Fill">

    <!--  placeholder  -->


    <!--  entry and line  -->
    <StackLayout Margin="0,0,0,15" Spacing="0">

        <Grid HorizontalOptions="FillAndExpand">

            <input:EntryCentered
                x:Name="cEntry"
                Margin="0"
                BackgroundColor="Transparent"
                FontSize="{x:Static xam:FontSizes.Entry}"
                HorizontalOptions="Fill"
                PlaceholderColor="{x:Static xam:TextColors.Placeholder}"
                TextColor="{x:Static xam:TextColors.Entry}" />

            <Label
                x:Name="cDisplay"
                FontSize="{x:Static xam:FontSizes.Entry}"
                HorizontalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                InputTransparent="True"
                TextColor="{x:Static xam:TextColors.Entry}"
                VerticalOptions="Center" />

        </Grid>

        <svg:GradientBox
            x:Name="cGradientLine"
            Margin="0,0,0,4"
            EndColor="{x:Static xam:BackColors.GradientEndLine}"
            GradientOrientation="Horizontal"
            HeightRequest="1"
            HorizontalOptions="FillAndExpand"
            StartColor="{x:Static xam:BackColors.GradientStartLine}" />

    </StackLayout>

    <Label
        x:Name="cPlaceholder"
        FontSize="{x:Static xam:FontSizes.EntrySmallPlaceholder}"
        HorizontalOptions="Fill"
        HorizontalTextAlignment="Center"
        TextColor="{x:Static xam:TextColors.Placeholder}"
        VerticalOptions="End" />


</xam:CGrid>