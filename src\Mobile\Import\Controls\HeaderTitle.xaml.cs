﻿


namespace AppoMobi.UI
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class HeaderTitle 
	{
		public HeaderTitle ()
		{
			InitializeComponent ();
		}

        //-------------------------------------------------------------
        // Text
        //-------------------------------------------------------------
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(HeaderTitle), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }

	    
	    protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	        
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {

	            case nameText:
	                cOne.Text = Text;
	                cTwo.Text = Text;
	                break;
            }

	    }




	}
}