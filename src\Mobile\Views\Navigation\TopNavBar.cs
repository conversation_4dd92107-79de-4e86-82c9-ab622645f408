﻿using AppoMobi.Xam;

namespace AppoMobi.Mobile.Views
{
    public class TopNavBar : VStack
    {
        private async Task AnimateIcon(SkiaControl icon)
        {
            try
            {
                var initial = 1f; //icon.Scale;
                await icon.ScaleToAsync(initial * 0.8, initial * 0.8, 40);
                await icon.ScaleToAsync(initial * 1.35, initial * 1.35, 125);
                await icon.ScaleToAsync(initial, initial, 200, Easing.SpringIn);
                await Task.Delay(10);
            }
            catch (Exception e)
            {
            }
        }

        private SkiaImage Icon;

        public TopNavBar()
        {
            Spacing = 0;
            UseCache = SkiaCacheType.GPU;
            Children = new List<SkiaControl>()
            {
                //statusbar
                new SkiaLayer()
                {
                    Opacity = 0.75,
                    HeightRequest = Super.StatusBarHeight,
                    Background =
                        new LinearGradientBrush(
                            new GradientStopCollection()
                            {
                                new GradientStop(BackColors.GradientStartNav, 0),
                                new GradientStop(BackColors.GradientEndNav, 1),
                            }, new(0, 0), new(1, 0)),
                }.Initialize((me) => { me.HeightRequest = Super.StatusBarHeight; }),

                //navbar
                new SkiaLayer()
                {
                    HeightRequest = 46,
                    Background =
                        new LinearGradientBrush(
                            new GradientStopCollection()
                            {
                                new GradientStop(BackColors.GradientStartNav, 0),
                                new GradientStop(BackColors.GradientEndNav, 1),
                            }, new(0, 0), new(1, 0)),
                    Children = new List<SkiaControl>()
                    {
                        new SkiaMarkdownLabel("Art Of Foto")
                        {
                            FontSize = 18,
                            FontFamily = "FontTextTitle",
                            TextColor = AppColors.Icons,
                            UseCache = SkiaCacheType.Operations
                        }.Center(),
                        new SkiaImage(@"Images\close3b.png")
                        {
                            HeightRequest = AppUI.NavbarIconSize,
                            RescalingQuality = SKFilterQuality.High,
                            RescalingType = RescalingType.MultiPass,
                            UseCache = SkiaCacheType.Image,
                            Aspect = TransformAspect.AspectFit,
                            Margin = new(16, 0, 8, 0),
                        }.CenterY().Assign(out Icon),
                        new SkiaHotspot()
                        {
                            WidthRequest = 60,
                            //BackgroundColor = Color.Parse("#33ff0000")
                        }.OnTapped(me =>
                        {
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                await AnimateIcon(Icon);
                                Globals.Values.AppRoot?.ShowMenu();
                                //OnDownLeftIcon1?.Invoke(this, e);
                            });
                        })
                    }
                }
            };
        }
    }
}