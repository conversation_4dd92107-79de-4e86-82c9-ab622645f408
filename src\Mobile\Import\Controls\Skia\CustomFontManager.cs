﻿using System;
using System.Collections.Generic;
using SkiaSharp;


namespace AppoMobi.Forms.Controls.Svg
{
    public class CustomFontManager
    {
        private static CustomFontManager _instance;
        public static CustomFontManager Instance
        {
            get
            {
 
                if (_instance == null)
                {
                    _instance = new CustomFontManager();
                }
                return _instance;
            }
        }


        public Dictionary<string, SKTypeface> Fonts { get; set; } = new Dictionary<string, SKTypeface>();

        public SKTypeface GetFont(string filename)
        {
            SKTypeface font = null;
            try
            {
                font = Fonts[filename];
            }
            catch (Exception e)
            {
            }

            if (font == null)
            {
                var native = DependencyService.Get<ISkiaNativeLocal>();
                font = native.GetTypeface(filename);
                if (font != null)
                {
                    Fonts[filename] = font;
                }
            }
            
            return font;

        }

    }
}