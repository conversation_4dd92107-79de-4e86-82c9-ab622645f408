﻿namespace AppoMobi
{
    using System;
    

    /// <summary>
    /// An extended <see cref="ListView"/> with scroll position tracking.
    /// </summary>
    public class CListView : Microsoft.Maui.Controls.ListView
    {




#if __IOS__
        public CListView() : base(ListViewCachingStrategy.RetainElement)
        {


        }
#else
        public CListView() : base(ListViewCachingStrategy.RecycleElement)
        {


        }
#endif

        public new event EventHandler<ScrolledEventArgs> Scrolled;
        public void OnScrolled(ScrolledEventArgs args)
        {
            Scrolled?.Invoke(this, args);
        }

    }
}