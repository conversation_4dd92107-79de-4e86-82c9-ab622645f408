﻿using System;
using System.ComponentModel;
using AppoMobi.Models;
using AppoMobi.Touch;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CellMenuItem
    {
        public override void Dispose()
        {
            try
            {
                if (!(BindingContext is MenuPageContentItem item)) return;
                item.PropertyChanged -= OnItemPropertyChanged;
                }
            catch (Exception e)
            {
            }
            base.Dispose();
        }

        //-------------------------------------------------------------
        public CellMenuItem()
        //-------------------------------------------------------------
        {
            InitializeComponent();

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                //cText.TranslationY = 2;
             
            }
            else
            {
                //android
                //cText.TranslationY = 1;//2;
            }

            cImage.TranslationY = -1;

            var item = BindingContext as MenuPageContentItem;
            if (item == null) return;
            SetupCell();
        
        }
        //-------------------------------------------------------------
        protected override void OnBindingContextChanged()
        //-------------------------------------------------------------
        {
            SetupCell();
            base.OnBindingContextChanged();
        }

        
        private void OnItemPropertyChanged(object sender, PropertyChangedEventArgs e)
        
        {
            //if (e.PropertyName == "Selected")
            //{
            //    var item = (AppoObjecttem)sender;
            //    if (item.Selected) cArrow.Text = "✓";
            //    else cArrow.Text = "";
            //}
        }

        //-------------------------------------------------------------
        public void SetupCell()
        //-------------------------------------------------------------
        {
            if (!(BindingContext is MenuPageContentItem item)) return;

            try { item.PropertyChanged -= OnItemPropertyChanged; }
            catch (Exception e) { }

            if (item.Separator)
            {
                HeightRequest = 10;
                BackgroundColor = Colors.Transparent;
            }
            else
            {
                InitGesturesForCell(SelectionBox);

                item.PropertyChanged += OnItemPropertyChanged;
                var ee = new PropertyChangedEventArgs("Selected");
                OnItemPropertyChanged(item, ee);

                cText.Text = item.NameInTabs;//?.ToTitleCase();
                cImage.Text = item.IconString;
            }

        }


        public event EventHandler<TapEventArgs> ItemTapped;
        
        public void CallItemTapped(object item, TapEventArgs e)
        
        {
            ItemTapped?.Invoke(item, e);

        }
        
        private void MenuItemTapped(object sender, TapEventArgs e)
        
        {
            if (!(BindingContext is MenuPageContentItem item)) return;

            if (item.Separator) return;

            CallItemTapped(BindingContext, e);

        }

    }
}
