﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
        <TargetFrameworks>net9.0-android;net9.0-ios;</TargetFrameworks>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0;</TargetFrameworks>
	
    <!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

    <!--hot reload not working on android for some reason anyway so make it faster-->
    <!-- <_MauiForceXamlCForDebug Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">True</_MauiForceXamlCForDebug> -->
		
    <RootNamespace>AppoMobi.Mobile</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
    
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>
    <WarningsAsErrors>CS0108</WarningsAsErrors>

    <!-- Display name -->
		<ApplicationTitle>Test Art Of Foto</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.appomobi.artoffoto.dev</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>9.0</ApplicationDisplayVersion>
		<ApplicationVersion>9</ApplicationVersion>
    <Version>9</Version>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
    <!--https://github.com/mono/SkiaSharp/issues/3113-->
    <WindowsPackageType>MSIX</WindowsPackageType>
    <OutputType>Exe</OutputType>
		<!--<WindowsPackageType>None</WindowsPackageType>-->
    <!--<WindowsSdkPackageVersion>10.0.19041.45</WindowsSdkPackageVersion>-->

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">23.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>

  </PropertyGroup>

    <!--personal provisioning for iPhone-->
    <Import Project="../../../Provisioning.targets" Condition="Exists('../../../Provisioning.targets')" />

    <!--IOS RELEASE-->
    <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-ios|AnyCPU'">
        <ApplicationId>com.appomobi.artoffoto</ApplicationId>
        <CreatePackage>false</CreatePackage>
        <BuildIpa>true</BuildIpa>
        <MtouchUseLlvm>true</MtouchUseLlvm>
        <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
        <OptimizePNGs>true</OptimizePNGs>
    </PropertyGroup>

    <!--ADROID RELEASE-->
    <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
        <EnableLLVM>true</EnableLLVM>
        <RunAOTCompilation>true</RunAOTCompilation>
        <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
        <!--STORE-->
        <AndroidPackageFormat>aab</AndroidPackageFormat>
    </PropertyGroup>

  <ItemGroup Condition="$(TargetFramework.Contains('ios')) == true">
    <BundleResource Include="Platforms\iOS\Assets.xcassets\**\*" />
  </ItemGroup>

  <ItemGroup>

    <!--<PackageReference Include="AppoMobi.Framework" Version="*******" />-->
    <!--<PackageReference Include="AppoMobi.Plugin.Navigation" Version="1.0.7" />-->
    <!--<PackageReference Include="AppoMobi.Specials" Version="8.0.3" />-->
    <!--<PackageReference Include="Rg.Plugins.Popup" Version="2.1.0" />-->
    <!--<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.3.56.1-pre" />-->
    <!--<PackageReference Include="CommunityToolkit.Maui" Version="11.0.0" />-->

    <PackageReference Include="AppoMobi.Maui.Navigation" Version="1.9.3-pre" />

      <PackageReference Include="AppoMobi.Preview.CommunityToolkit.Maui.Core" Version="1.1.1-pre1" />
      <PackageReference Include="AppoMobi.Preview.CommunityToolkit.Maui" Version="1.1.1-pre1" />

    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.30" />

    <PackageReference Include="FFImageLoading.Maui" Version="1.2.7" />

    <PackageReference Include="MathParser.org-mXparser" Version="6.1.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

	<ItemGroup>

    <!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#000000" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#000000" BaseSize="110,110" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <BundleResource Remove="Platforms\iOS\Resources\es.lproj\InfoPlist.strings" />
	</ItemGroup>

	<ItemGroup>
    <Compile Remove="Platforms\Android\AnimatedPage\Effects\ColoredSliderEffect.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\AnimationHelper.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\AnimationsId.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\LongAnimationsId.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\NormalAnimationsId.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\PageExtensions.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Helpers\SlowAnimationsId.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\NativeServices\DroidDevice.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Renderers\AppCompatAnimationNavRenderer.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Renderers\AppCompatNavRendererHelper.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Renderers\IXPage.cs" />
	  <Compile Remove="Platforms\Android\AnimatedPage\Renderers\XPage.cs" />
	  <Compile Remove="Platforms\Android\camera\MediaConstants.cs" />
	  <Compile Remove="Platforms\Android\PlatformNavigation.cs" />
	  <Compile Remove="Platforms\Android\Renderers\NavigationPage.Android.cs" />
	  <Compile Remove="Platforms\Android\Renderers\TabBar.Android.cs" />
	  <Compile Remove="Platforms\Android\TabBar.Android.cs" />
	  <Compile Remove="Platforms\Android\TabbedPage.Android.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Delegates\GestureRecognizerDelegate.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Delegates\NavigationControllerDelegate.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Effects\ColoredSliderEffect.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Helpers\AnimationHelper.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\NativeServices\AppleDevice.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Renderers\AnimationNavigationRenderer.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Transitions\AnimatedTransition.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Transitions\DefaultTransition.cs" />
	  <Compile Remove="Platforms\iOS\AnimatedPage\Utils\ViewExtensions.cs" />
	</ItemGroup>



  <ItemGroup>
    <Compile Remove="Platforms\Android\AnimatedPage\Effects\ColoredSliderEffect.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\AnimationHelper.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\AnimationsId.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\LongAnimationsId.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\NormalAnimationsId.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\PageExtensions.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Helpers\SlowAnimationsId.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\NativeServices\DroidDevice.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\PlatformNavigation.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Renderers\AppCompatAnimationNavRenderer.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Renderers\AppCompatNavRendererHelper.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Renderers\IXPage.cs" />
    <Compile Remove="Platforms\Android\AnimatedPage\Renderers\XPage.cs" />
    <Compile Remove="Platforms\Android\camera\MediaConstants.cs" />
    <Compile Remove="Platforms\Android\TabBar.Android.cs" />
    <Compile Remove="Platforms\Android\TabbedPage.Android.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Delegates\GestureRecognizerDelegate.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Delegates\NavigationControllerDelegate.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Effects\ColoredSliderEffect.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Helpers\AnimationHelper.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\NativeServices\AppleDevice.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Renderers\AnimationNavigationRenderer.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Transitions\AnimatedTransition.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Transitions\DefaultTransition.cs" />
    <Compile Remove="Platforms\iOS\AnimatedPage\Utils\ViewExtensions.cs" />
  </ItemGroup>


  <ItemGroup>
	  <None Remove="Platforms\Android\canond30.mp3" />
	  <None Remove="Platforms\Android\Resources\animator\empty_Animation.xml" />
	  <None Remove="Platforms\Android\Resources\animator\empty_Animation_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\empty_Animation_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_from_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_from_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_landing_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_roll_from_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_from_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_rotate_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_from_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\enter_scale_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_landing_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_roll_to_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_rotate_to_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_from_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_scale_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_bottom_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_left_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_right_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top_long.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top_long_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top_short.xml" />
	  <None Remove="Platforms\Android\Resources\animator\exit_to_top_short_bounce.xml" />
	  <None Remove="Platforms\Android\Resources\values\arrays.xml" />
	  <None Remove="Platforms\Android\Resources\values\attrs.xml" />
	  <None Remove="Platforms\Android\Resources\values\ic_launcher_background.xml" />
	  <None Remove="Platforms\Android\Resources\values\settings.xml" />
	  <None Remove="Platforms\Android\sound.mp3" />
	  <None Remove="Platforms\iOS\Resources\metal\default.metal" />
	  <None Remove="Resources\Images\Brand\back.jpg" />
	  <None Remove="Resources\Images\Brand\logo.png" />
	  <None Remove="Resources\Images\Brand\logoabout.jpg" />
	  <None Remove="Resources\Images\Navbar\bag.png" />
	  <None Remove="Resources\Images\Navbar\car.png" />
	  <None Remove="Resources\Images\Navbar\close.png" />
	  <None Remove="Resources\Images\Navbar\close3.png" />
	  <None Remove="Resources\Images\Navbar\close3a.png" />
	  <None Remove="Resources\Images\Navbar\down.png" />
	  <None Remove="Resources\Images\Navbar\eye.png" />
	  <None Remove="Resources\Images\Navbar\giftcard.png" />
	  <None Remove="Resources\Images\Navbar\info.png" />
	  <None Remove="Resources\Images\Navbar\left.png" />
	  <None Remove="Resources\Images\Navbar\menu.png" />
	  <None Remove="Resources\Images\Navbar\phones.png" />
	  <None Remove="Resources\Images\Navbar\search.png" />
	  <None Remove="Resources\Images\Navbar\sort.png" />
	  <None Remove="Resources\Images\Navbar\tbback.png" />
	  <None Remove="Resources\Images\Navbar\tbgps.png" />
	  <None Remove="Resources\Images\Navbar\tbsearch.png" />
	  <None Remove="Resources\Images\Navbar\tbsortabc.png" />
	  <None Remove="Resources\Images\Navbar\tbsortkm.png" />
	  <None Remove="Resources\Images\Navbar\tbwww.png" />
	  <None Remove="Resources\Images\Navbar\up.png" />
	  <None Remove="Resources\Images\Navbar\wback.png" />
	  <None Remove="Resources\Images\Navbar\wforward.png" />
	  <None Remove="Resources\Images\Navbar\wup.png" />
	  <None Remove="Resources\Images\nodpi\fx.png" />
	  <None Remove="Resources\Images\nodpi\spinner.gif" />
	  <None Remove="Resources\Images\nodpi\spinnerx.gif" />
	  <None Remove="Resources\Images\Svg\image.svg" />
	  <None Remove="Resources\Images\Svg\imagecolored.svg" />
	  <None Remove="Resources\Images\UI\carre.png" />
	  <None Remove="Resources\Images\UI\circlemask.png" />
	  <None Remove="Resources\Images\UI\fxbtn.png" />
	  <None Remove="Resources\Images\UI\fxbtn1.png" />
	</ItemGroup>

	<ItemGroup>
	  <AndroidAsset Include="Platforms\Android\canond30.mp3" />
	  <AndroidAsset Include="Platforms\Android\sound.mp3" />
	</ItemGroup>

	<ItemGroup>
	  <MauiImage Include="Resources\Images\Brand\back.jpg" />
	  <MauiImage Include="Resources\Images\Brand\logo.png" />
	  <MauiImage Include="Resources\Raw\Images\logoabout.jpg" />
	  <MauiImage Include="Resources\Images\Navbar\bag.png" />
	  <MauiImage Include="Resources\Images\Navbar\car.png" />
	  <MauiImage Include="Resources\Images\Navbar\close.png" />
	  <MauiImage Include="Resources\Images\Navbar\close3.png" />
	  <MauiImage Include="Resources\Images\Navbar\close3a.png" />
	  <MauiImage Include="Resources\Images\Navbar\down.png" />
	  <MauiImage Include="Resources\Images\Navbar\eye.png" />
	  <MauiImage Include="Resources\Images\Navbar\giftcard.png" />
	  <MauiImage Include="Resources\Images\Navbar\info.png" />
	  <MauiImage Include="Resources\Images\Navbar\left.png" />
	  <MauiImage Include="Resources\Images\Navbar\menu.png" />
	  <MauiImage Include="Resources\Images\Navbar\phones.png" />
	  <MauiImage Include="Resources\Images\Navbar\search.png" />
	  <MauiImage Include="Resources\Images\Navbar\sort.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbback.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbgps.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbsearch.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbsortabc.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbsortkm.png" />
	  <MauiImage Include="Resources\Images\Navbar\tbwww.png" />
	  <MauiImage Include="Resources\Images\Navbar\up.png" />
	  <MauiImage Include="Resources\Images\Navbar\wback.png" />
	  <MauiImage Include="Resources\Images\Navbar\wforward.png" />
	  <MauiImage Include="Resources\Images\Navbar\wup.png" />
	  <MauiImage Include="Resources\Images\nodpi\fx.png" />
	  <MauiImage Include="Resources\Images\nodpi\spinner.gif" />
	  <MauiImage Include="Resources\Images\nodpi\spinnerx.gif" />
	  <MauiImage Include="Resources\Images\Svg\image.svg" />
	  <MauiImage Include="Resources\Images\Svg\imagecolored.svg" />
	  <MauiImage Include="Resources\Images\UI\carre.png" />
	  <MauiImage Include="Resources\Images\UI\circlemask.png" />
	  <MauiImage Include="Resources\Images\UI\fxbtn.png" />
	  <MauiImage Include="Resources\Images\UI\fxbtn1.png" />
	</ItemGroup>

 
 

	<ItemGroup>
	  <AndroidResource Update="Platforms\Android\Resources\values\arrays.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\values\attrs.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\values\colors.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\values\ic_launcher_background.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\values\settings.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\values\styles.xml">
	    <CustomToolNamespace>MSBuild:UpdateGeneratedFiles</CustomToolNamespace>
	  </AndroidResource>
	</ItemGroup>




	<ItemGroup>
	  <Compile Update="Import\Common\ResX\ResStrings.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ResStrings.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Navigation\PageSwitcher.xaml.cs">
	    <DependentUpon>PageSwitcher.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Settings\ContentSettings.xaml.cs">
	    <DependentUpon>ContentSettings.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Settings\BlockSettings.xaml.cs">
	    <DependentUpon>BlockSettings.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Import\Common\ResX\ResStrings.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>
 

	<ItemGroup>
	  <Folder Include="Import\Features\Navigation\" />
	  <Folder Include="Import\Xam\AppColors\" />
	  <Folder Include="Views\News\Services\" />
	  <Folder Include="Views\Navigation\Shell\" />
	</ItemGroup>
 

	<ItemGroup>
	  <Metal Include="Platforms\iOS\Resources\metal\default.metal">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </Metal>
	</ItemGroup>
 

	<ItemGroup>
	  <ProjectReference Include="..\..\..\AppoMobi\src\AppoMobi.Framework.Maui\AppoMobi.Framework.Maui.csproj" />
	  <ProjectReference Include="..\..\..\DrawnUi.Maui\src\Maui\Addons\DrawnUi.Maui.Camera\DrawnUi.Maui.Camera.csproj" />
	</ItemGroup>
 

	<ItemGroup>
	  <MauiXaml Update="Views\Navigation\PageSwitcher.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\Settings\ContentSettings.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\Settings\BlockSettings.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>
 

</Project>
