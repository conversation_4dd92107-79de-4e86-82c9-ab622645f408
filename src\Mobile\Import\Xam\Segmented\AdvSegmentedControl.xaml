﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.Xam.AdvSegmentedControl"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:system="clr-namespace:System;assembly=netstandard">
    <ContentView.Content>
        <Frame
            x:Name="FrameView"
            Padding="0"
            IsClippedToBounds="True">
            <!--  Platform specific customization values for the border  -->
            <Frame.HasShadow>
                <OnPlatform x:TypeArguments="system:Boolean">
                    <On Platform="Android" Value="False" />
                    <On Platform="iOS" Value="True" />
                </OnPlatform>
            </Frame.HasShadow>
            <Frame.CornerRadius>
                <OnPlatform x:TypeArguments="system:Single">
                    <On Platform="Android" Value="0" />
                    <On Platform="iOS" Value="5" />
                </OnPlatform>
            </Frame.CornerRadius>
            <Frame.HeightRequest>
                <OnPlatform x:TypeArguments="system:Double">
                    <On Platform="Android" Value="50" />
                    <On Platform="iOS" Value="35" />
                    <On Platform="UWP" Value="50" />
                </OnPlatform>
            </Frame.HeightRequest>
            <!--  Platform specific customization values for the border  -->

            <!--  Holder of the Child Tab buttons  -->
            <Grid x:Name="TabButtonHolder" ColumnSpacing="0" />

        </Frame>
    </ContentView.Content>
</ContentView>