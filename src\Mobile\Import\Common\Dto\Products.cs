﻿using System.Collections.Generic;

namespace AppoMobi.Common.Dto
{
    //*************************************************************
    public class CompanyInfoDTO
    //*************************************************************
    {
        public string Id { get; set; }
        public string Subtitle { get; set; }
        public string Name { get; set; }
        public string FullName { get; set; }
        public string LogoUrl { get; set; }
        public string Description { get; set; }
        public string SubLogoUrl { get; set; }
        public string SubLogoText { get; set; }
        public string ContactUsTel { get; set; }
        public string ContactUsEmail { get; set; }
        public string ContactUsAddress { get; set; }
        public string ContactUsWebsite { get; set; }
        public string ContactUsFacebook { get; set; }
        public string ContactUsFacebookNumeric { get; set; }
        public string ContactUsVk { get; set; }        
        public string ContactUsInstagram { get; set; }
        public int Priority { get; set; }
        public string WeOnMapDesc { get; set; }
        public string ImageHowToFind { get; set; }
        public string HowToFindUs { get; set; }
        public string GalleryId { get; set; }

        public double MapX { get; set; }
        public double MapY { get; set; }
        public double MapZoom { get; set; }
    }

    //===================================================================
    public class ProdCatDTO
    //===================================================================
    {
        public string Id { get; set; }
        public string ParentId { get; set; }

        public string Name { get; set; }
        public string Desc { get; set; } //empty to date

        public string ImageUrl { get; set; }
        public double ImageWidth { get; set; }
        public double ImageHeight { get; set; }

        public int Priority { get; set; }

        public string MiniImageUrl { get; set; }
        public double MiniImageWidth { get; set; }
        public double MiniImageHeight { get; set; }

        //local mobile use :( i'm lazy
        public double DynamicWidth { get; set; }
        public double DynamicHeight { get; set; }
        public bool HasSubCats { get; set; }

    }

    //===================================================================
    public class ProdElemDTO : BaseMobileDto
    //===================================================================
    {

        public string ImageId { get; set; } // upload id
        public string Params { get; set; }
        public int Priority { get; set; }
    }


    //===================================================================
    public class JsonProductCategory
    //===================================================================
    {
        public List<ProdCatDTO> categories { get; set; }
        public string status { get; set; }
        public int count { get; set; }
        public double export { get; set; }
    }

    //===================================================================
    public class JsonProductList
    //===================================================================
    {
        public List<ProductDTO> products { get; set; }
        public string status { get; set; }
        public int count { get; set; }
        public double export { get; set; }
    }

    //===================================================================
    public class ProductDTO
    //===================================================================
    {
        public string Id { get; set; } //hello UID
        public string Category { get; set; }

        public string Name { get; set; }
        public string NameInt { get; set; }
        public string Desc { get; set; } 
        public string Like { get; set; } 
        public string Tip { get; set; }

        public decimal Price  { get; set; } //empty to date
        public double Volume { get; set; } //empty to date
        public string Units { get; set; } //empty to date

        public string Code { get; set; } 
        public string Keywords { get; set; } //NEW

        public string Contains { get; set; } //NEW

        public bool New { get; set; } //empty to date

        public int Priority { get; set; } //for news

        public string Link { get; set; } //for sharing!

        public string ImageUrl { get; set; }
        public double ImageWidth { get; set; }
        public double ImageHeight { get; set; }

        public double DynamicWidth { get; set; }
        public double DynamicHeight { get; set; }
    }


}