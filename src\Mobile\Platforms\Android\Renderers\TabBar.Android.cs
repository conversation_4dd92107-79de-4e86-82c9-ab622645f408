﻿using Android.Views;
using Google.Android.Material.Tabs;
using Microsoft.Maui.Handlers;
using Rect = Microsoft.Maui.Graphics.Rect;
using View = Android.Views.View;

//[assembly: ExportRenderer(typeof(NiftyTabbedPage), typeof(NiftyTabbedPageRenderer))]
namespace AppoMobi.Droid
{


    public partial class CustomTabs : TabbedPage
    {

    }

    public partial class HideTabbedBarPageHandler: TabbedViewHandler
    {
        protected override void ConnectHandler(View platformView)
        {
            base.ConnectHandler(platformView);

            //Update();
        }

        void Update()
        {
            if (PlatformView is ViewGroup rootView)
            {
                HideTabLayoutRecursively(rootView);
            }
        }
        
        public override void PlatformArrange(Rect frame)
        {
            //Update();

            base.PlatformArrange(frame);
        }

        private void HideTabLayoutRecursively(ViewGroup parent)
        {
            for (int i = 0; i < parent.ChildCount; i++)
            {
                var child = parent.GetChildAt(i);
                switch (child)
                {
                    case TabLayout tabLayout:
                        tabLayout.Visibility = ViewStates.Gone;
                        break;

                    // If you also want to remove the space the TabLayout occupies:
                    // tabLayout.LayoutParameters = new ViewGroup.LayoutParams(0, 0);

                    case ViewGroup vg:
                        HideTabLayoutRecursively(vg);
                        break;
                }
            }
        }

        /*
        protected override void ConnectHandler(UIView platformView)
        {
            base.ConnectHandler(platformView);

            // For iOS, .NET MAUI uses a UITabBarController under the hood.
            if (PlatformViewController is UITabBarController tabBarController)
            {
                // Hide the native iOS tab bar
                tabBarController.TabBar.Hidden = true;
            }
        }
        */
    }

 

}

