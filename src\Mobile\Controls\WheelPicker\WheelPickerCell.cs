﻿using AppoMobi.Models;
using TextColors = AppoMobi.Xam.TextColors;

namespace AppoMobi.Mobile.Views.Controls.WheelPicker
{

    public interface IWheelPickerCell
    {
        void UpdateContext(CellInfo ctx);
    }

    public class WheelPickerCell : SkiaShape, IWheelPickerCell
    {
        private readonly SkiaLabel _label;

        public WheelPickerCell()
        {

            HorizontalOptions = LayoutOptions.Fill; ;
            Padding = 0;
            UseCache = SkiaCacheType.Operations;

            _label = new SkiaLabel()
            {
                HeightRequest = 100,
                Padding = 4,
                FontAttributes = FontAttributes.Italic,
                FontSize = 17,
                HorizontalOptions = LayoutOptions.Center,
                HorizontalTextAlignment = DrawTextAlignment.Center,
                TextColor = TextColors.Result,
                VerticalTextAlignment = TextAlignment.Center
            };

            ApplyContext();

            Content = _label;
        }

        void ApplyContext()
        {
            if (BindingContext is ValueItem value)
            {
                _label.Text = value.Title;
            }
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            ApplyContext();
        }

        public void UpdateContext(CellInfo ctx)
        {
            Opacity = ctx.Opacity;
            //if (ctx.IsSelected)
            //{
            //    _label.FontAttributes = FontAttributes.Italic | FontAttributes.Bold;
            //}
            //else
            //{
            //    _label.FontAttributes = FontAttributes.Italic;
            //}
        }
    }
}
