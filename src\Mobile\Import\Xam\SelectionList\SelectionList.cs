﻿using System.Collections.Generic;
using System.Threading.Tasks;


namespace AppoMobi.Xam
{
    public class SelectionList
    {

        
        public SelectionList(string title, List<string> list, string cancel="Cancel", int selected=-1)
        
        {
            sMessage = title;
            sNo = cancel;
            Items = list;
            iResult = selected;
        }

        protected List<string> Items { get; set; }

        private bool bDisableBackgroundClick { get; set; }
        private bool bQuitOnBackPressed { get; set; }

        public void DisableBackgroundClick(bool value = true)
        {
            bDisableBackgroundClick = value;
        }

        public void QuitOnBackPressed(bool value = true)
        {
            bQuitOnBackPressed = value;
        }


        public bool Multiselect { get; set; }
        public int MultiselectMin { get; set; }
        public int MultiselectMax { get; set; }


        
        private void CallbackAfter(int ret)
        
        {
            iResult = ret;
            bFinished = true;
        }

        public string sNo { get; set; }
        public string sMessage { get; set; }
        public int iResult { get; set; } = -1;
        public bool bFinished { get; set; } = false;

        
        public async Task<int> ShowAsync(bool showNo = true)
        {
            new SelectionListView(CallbackAfter, sMessage, Items, sNo, bDisableBackgroundClick,
                bQuitOnBackPressed, iResult).Open();

            while (!bFinished)
            {
                await Task.Delay(100);
            }
            return iResult;
        }



    }
 
}
