﻿using System.Collections.Generic;
using AppoMobi.Framework.Api;
using AppoMobi.Common.Dto.Required;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.System
{
    public class BaseCustomerRequestDto: BaseFrameworkDto
    {
        public string Master { get; set; }
        public string Slave { get; set; }
        public string Question { get; set; }
        public string Answer { get; set; }
        public CustomerRequestStatus Status { get; set; }
        public CustomerRequestType Type { get; set; }

        public List<GalleryImageDTO> Images { get; set; }

        public bool HasAttachments { get; set; }
    }
}