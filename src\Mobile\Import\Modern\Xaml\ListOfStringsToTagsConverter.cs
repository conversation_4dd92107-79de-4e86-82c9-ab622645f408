﻿using System;
using System.Collections.Generic;
using System.Globalization;



namespace AppoMobi.Forms.Framework.Xaml
{
    public class ListOfStringsToTagsConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var tags = ((List<string>)value).ToTags();

                return tags;
            }
            catch (Exception e)
            {
            }

            return value;
        }
    }

  
}