﻿using System;
using System.Collections.Generic;
using AppoMobi.Common.Dto.Required;
using AppoMobi.Common.Enums.System;

namespace AppoMobi.Common.Dto.UserData
{
    /// <summary>
    /// Full details from server
    /// </summary>
    public class DocumentDetailsDto
    {
        public string Id { get; set; }

        public string PlayerKey { get; set; }

        public int Type { get; set; }

        public string TypeDesc { get; set; }

        public string Number { get; set; }

        public DateTime? Issued { get; set; }

        public string IssuedBy { get; set; }

        public DateTime? Expires { get; set; }

        public string UserComments { get; set; }

        public ConfirmationStatusType Status { get; set; }

        public string AdminComments { get; set; }

        public List<GalleryImageDTO> Images { get; set; }
    }
}