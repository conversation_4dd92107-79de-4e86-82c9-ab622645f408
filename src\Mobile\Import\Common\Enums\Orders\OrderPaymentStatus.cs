﻿using System.ComponentModel;
using AppoMobi.Specials.Localization;
using AppoMobi.Forms.Common.ResX;

namespace AppoMobi.Common.Enums.Orders
{
    [TypeConverter(typeof(LocalizedEnumConverter))]
    [FromResources(Type = typeof(ResStrings))]
    
    public enum OrderPaymentStatus
        
    {
        None,
        Processing,
        Pending,
        Payed,
        Failed,
        Canceled,
        Refunded
    }
}