﻿

namespace AppoMobi.UI
{
    public class GesturesGrid: AppoMobi.Touch.LegacyGesturesGrid
    {
        public GesturesGrid()
        {
            if (Core.IsIOS)
            {
                this.ColumnDefinitions.Add(new ColumnDefinition());
            }
        }
    }
    public class MyGrid : Grid
    {
        public MyGrid()
        {
            if (Core.IsIOS)
            {
                this.ColumnDefinitions.Add(new ColumnDefinition());
            }
        }
    }
}
