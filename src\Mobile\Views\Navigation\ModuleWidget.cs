﻿using AppoMobi.Maui.Gestures;
using AppoMobi.Models;
using AppoMobi.Xam;
using DrawnUi.Controls;

namespace AppoMobi.Mobile.Views
{
    public class ModuleWidget : SkiaDrawnCell
    {
        protected SkiaLabel LabelTitle;
        protected SkiaLabel SvgIcon;

        protected override void SetContent(object ctx)
        {
            base.SetContent(ctx);

            if (Children.Count == 0)
            {
                CreateContent();
            }

            if (ctx is TabbedMenuItem item)
            {
                LabelTitle.Text = item.NameInTabs;
                SvgIcon.Text = item.IconString;
            }
        }

        protected virtual void CreateContent()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaShape()
                    {
                        UseCache = SkiaCacheType.Image,
                        BackgroundColor = Colors.Black.WithAlpha(0.5f),
                        StrokeWidth = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        Padding = new(4, 10),
                        StrokeColor = Color.Parse("#AABDB7A7"),
                        CornerRadius = 7,
                        Content = new VStack()
                        {
                            VerticalOptions = LayoutOptions.Center,
                            Spacing =2,
                            Children = new List<SkiaControl>()
                            {
                                new FontIconLabelDrawn()
                                {
                                    Font = "FaSolid",
                                    HorizontalOptions = LayoutOptions.Center,
                                    FontSize = 32,
                                    LockRatio = 1,
                                    FillGradient =
                                        new SkiaGradient()
                                        {
                                            StartXRatio = 1,
                                            EndXRatio = 0,
                                            Colors =
                                                new Color[] { BackColors.GradientStartNav, BackColors.GradientEndNav }
                                        },
                                    UseCache = SkiaCacheType.Image
                                }.Assign(out SvgIcon),
                                new SkiaMarkdownLabel()
                                {
                                    FontFamily = "FontText",
                                    Tag = "Debug",
                                    FontSize = 13,
                                    TextColor = Color.Parse("#BDB7A7"),
                                    HorizontalOptions = LayoutOptions.Center,
                                    MaxLines = 1,
                                    LineHeight = 1.2,
                                    VerticalTextAlignment = TextAlignment.Center,
                                    UseCache = SkiaCacheType.Operations,
                                }.Assign(out LabelTitle),
                            }
                        }
                    }
                    .WithGestures((me, args, apply) =>
                    {
                        if (args.Type == TouchActionResult.Tapped)
                        {
                            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50),
                                () =>
                                {
                                    App.Instance.Messager.All("WidgetClicked", this.BindingContext as TabbedMenuItem);
                                });
                        }
                        else if (args.Type == TouchActionResult.Down)
                        {
                            var color = Colors.White;
                            var ptsInsideControl =
                                me.GetOffsetInsideControlInPoints(args.Event.Location, apply.ChildOffset);
                            me.PlayRippleAnimation(color, ptsInsideControl.X, ptsInsideControl.Y);
                        }

                        if (args.Type != TouchActionResult.Up)
                        {
                            return me;
                        }

                        return null;
                    })
            };
        }

        public ModuleWidget()
        {
            //UseCache = SkiaCacheType.Image;
            WidthRequest = 110;
            //HeightRequest = 65;
        }

 
    }
}
