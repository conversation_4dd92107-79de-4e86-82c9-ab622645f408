﻿using Android.Hardware.Camera2;

namespace AppoMobi.Droid.Camera.Views
{
    public partial class CameraFragment
    {
        public class CameraStateListener : CameraDevice.StateCallback
        {
            private readonly CameraFragment owner;

            public CameraStateListener(CameraFragment owner)
            {
                if (owner == null)
                    throw new System.ArgumentNullException("owner");
                this.owner = owner;
            }

            public override void OnClosed(CameraDevice camera)
            {
                //base.OnClosed(camera);

               // owner.StopBackgroundThread();
            }

            public override void OnOpened(CameraDevice cameraDevice)
            {
                // This method is called when the camera is opened.  We start camera preview here.
                owner.mCameraOpenCloseLock.Release();
                owner.mCameraDevice = cameraDevice;
                owner.CreateCameraPreviewSession();
            }

            public override void OnDisconnected(CameraDevice cameraDevice)
            {

                
                cameraDevice.Close();

                owner.mCameraDevice = null;

                owner.mCameraOpenCloseLock.Release();
            }

            public override void OnError(CameraDevice cameraDevice, CameraError error)
            {
                owner.mCameraOpenCloseLock.Release();
                cameraDevice.Close();
                owner.mCameraDevice = null;
                if (owner == null)
                    return;

                //Activity activity = owner.Activity;
                //if (activity != null)
                //{
                //    activity.Finish();
                //}
            }

        }
    }
}