﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:appoMobi="clr-namespace:AppoMobi"
             xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
             xmlns:gestures="clr-namespace:AppoMobi.Touch"
             x:Class="AppoMobi.UI.SearchDesc">
  <ContentView.Content>

      <!--  SEARCH STRING  -->
      <appoMobi:CFrame
          x:Name="cSearchDesc"
          Margin="8,0,8,4"
          Padding="8,6,6,5"
          BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
          HasShadow="False"
          HorizontalOptions="Fill"
          IsVisible="{Binding HasSearch}"
          BorderColor="{x:Static appoMobi:AppColors.DividerNav}"
          >
          <gestures:LegacyGesturesStackLayout HorizontalOptions="Fill" 
                                Tapped="OnTapped_ResetSearch"
                                Spacing="1">

              <Label
                  FontAttributes="Bold"
                  HorizontalOptions="Center"
                                TextColor="{x:Static appoMobi:AppColors.BwGrey}"             
                  FontSize="14.5"
                  x:Name="txtLabel"
                   />

              <Label IsVisible="False"
                  Margin="0,2,0,0"
                  FontSize="11"
                  HorizontalOptions="Center"
                  Text="{x:Static resX:ResStrings.Reset}"
                  TextColor="{x:Static appoMobi:AppColors.BwGreyLightest}" />

          </gestures:LegacyGesturesStackLayout>
      </appoMobi:CFrame>



    </ContentView.Content>
</ContentView>