﻿<?xml version="1.0" encoding="utf-8" ?>
<xam:PopupDialogBase
    x:Class="AppoMobi.Xam.EditableListView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:pages1="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    x:Name="ThisControl"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    OverlayColor="{x:Static appoMobi:AppColors.PopupOverlay}"
    VerticalOptions="Fill"
    Color="Transparent">

    <!--<pages1:PopupPage.Animation>
        <animations1:MoveAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"

            PositionIn="Right"
            PositionOut="Right" />
    </pages1:PopupPage.Animation>-->


    <!--  POPUP  -->

    <!--  this FUCKING bugs in dark mode Background is set INSTEAD of backgroundColor  -->
    <Grid HorizontalOptions="Fill" VerticalOptions="Fill">

        <StackLayout
            x:Name="Dialog"
            Background="{x:Static xam:BackColors.OptionLine}"
            HorizontalOptions="End"
            Spacing="0"
            VerticalOptions="Center"
            WidthRequest="250">

            <!--  HEADER  -->
            <xam:CGrid InputTransparent="True" VerticalOptions="StartAndExpand">

                <svg:GradientBox
                    EndColor="{x:Static xam:BackColors.GradientStartNav}"
                    GradientOrientation="Horizontal"
                    HorizontalOptions="Fill"
                    StartColor="{x:Static xam:BackColors.GradientEndNav}"
                    VerticalOptions="Fill" />

                <BoxView
                    BackgroundColor="#33000000"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

                <!--  header text  -->
                <Label
                    x:Name="cTitle"
                    Margin="20,8,8,8"
                    FontSize="15"
                    HorizontalOptions="Fill"
                    Text="{Binding Title}"
                    TextColor="WhiteSmoke"
                    VerticalOptions="Fill"
                    VerticalTextAlignment="Center" />

            </xam:CGrid>

            <ScrollView HorizontalOptions="Fill">

                <xam:NiftyDataStack
                    x:Name="cDataStack"
                    Margin="0,0,0,0"
                    Background="Transparent"
                    BackgroundColor="Transparent"
                    HorizontalOptions="Fill"
                    Spacing="0.5"
                    Tag="Popup"
                    VerticalOptions="Start">

                    <xam:NiftyDataStack.ItemTemplate>
                        <DataTemplate>

                            <StackLayout
                                x:Name="ControlMenu"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                Spacing="0"
                                VerticalOptions="Start">

                                <!--  List Item  -->
                                <Grid BackgroundColor="Transparent" HorizontalOptions="Fill">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="12" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="50" />
                                    </Grid.ColumnDefinitions>


                                    <Label
                                        Grid.Column="1"
                                        Margin="8,15,4,15"
                                        FontSize="15"
                                        Text="{Binding Title}"
                                        TextColor="{x:Static xam:TextColors.GreyDark}"
                                        VerticalOptions="Center" />

                                    <xam:FontIconLabel
                                        x:Name="IconDelete"
                                        Grid.Column="2"
                                        Margin="0,0,12,0"
                                        FontSize="13"
                                        HorizontalOptions="Center"
                                        Text="{x:Static xam:FaPro.CircleXmark}"
                                        TextColor="#ccaa8888"
                                        VerticalOptions="Center" />

                                    <!--<xam:FontIconLabel
                        Margin="0,0,8,0"
                        FontSize="14"
                        HorizontalOptions="End"
                        InputTransparent="True"
                        Opacity="0.5"
                        Text="{x:Static xam:FaPro.CheckCircle}"
                        TextColor="{x:Static xam:TextColors.Entry}"
                        VerticalOptions="Center" />-->

                                    <controls:Hotspot
                                        Grid.Column="1"
                                        DownCommandParameter="{Binding .}"
                                        Tapped="OnTapped_Item" />

                                    <controls:Hotspot
                                        Grid.Column="2"
                                        DownCommandParameter="{Binding .}"
                                        Tapped="OnTapped_ItemDelete"
                                        TransformView="{x:Reference IconDelete}" />

                                </Grid>

                                <!--  divider  -->
                                <BoxView
                                    BackgroundColor="LightGray"
                                    HeightRequest="0.5"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="End" />



                            </StackLayout>

                        </DataTemplate>
                    </xam:NiftyDataStack.ItemTemplate>


                </xam:NiftyDataStack>
            </ScrollView>

            <!--  ADD  -->
            <controls:TouchFrame
                x:Name="BtnAdd"
                Margin="16,16,16,4"
                Padding="1"
                BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                HorizontalOptions="Fill"
                StrokeShape="RoundRectangle 8,8,8,8"
                StrokeThickness="1"
                TimeLockDownMs="1000"
                Up="OnTapped_Add">



                <ContentView IsClippedToBounds="True">

                    <Grid
                        BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                        ColumnSpacing="1"
                        IsClippedToBounds="True"
                        VerticalOptions="StartAndExpand">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <xam:GesturesContentView
                            Grid.Column="0"
                            BackgroundColor="{x:Static xam:BackColors.Selected}"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                        <svg:GradientBox
                            Grid.Column="0"
                            EndColor="{x:Static xam:BackColors.GradientEndNav}"
                            GradientOrientation="Vertical"
                            InputTransparent="True"
                            Opacity="0.33"
                            StartColor="{x:Static xam:BackColors.GradientStartNav}"
                            VerticalOptions="FillAndExpand" />

                        <Label
                            Grid.Column="0"
                            Margin="6"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{x:Static resX:ResStrings.Add}"
                            TextColor="{x:Static xam:BackColors.Page}"
                            VerticalOptions="Center" />


                        <!--<xam:FontIconLabel
                        Margin="0,0,8,0"
                        FontSize="14"
                        HorizontalOptions="End"
                        InputTransparent="True"
                        Opacity="0.5"
                        Text="{x:Static xam:FaPro.Plus}"
                        TextColor="{x:Static xam:BackColors.Page}"
                        VerticalOptions="Center" />-->

                    </Grid>


                </ContentView>




            </controls:TouchFrame>

            <!--  OK  -->
            <controls:TouchFrame
                x:Name="BtnOk"
                Margin="16,16,16,16"
                Padding="1"
                BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                HorizontalOptions="Fill"
                StrokeShape="RoundRectangle 8,8,8,8"
                StrokeThickness="1"
                TimeLockDownMs="1000"
                Up="OnTapped_OK">



                <ContentView IsClippedToBounds="True">

                    <Grid
                        BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                        ColumnSpacing="1"
                        IsClippedToBounds="True"
                        VerticalOptions="StartAndExpand">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  0  -->
                        <!--  back hotspot  -->
                        <xam:GesturesContentView
                            Grid.Column="0"
                            BackgroundColor="{x:Static xam:BackColors.Selected}"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                        <svg:GradientBox
                            Grid.Column="0"
                            EndColor="{x:Static xam:BackColors.GradientEndNav}"
                            GradientOrientation="Vertical"
                            InputTransparent="True"
                            Opacity="0.33"
                            StartColor="{x:Static xam:BackColors.GradientStartNav}"
                            VerticalOptions="FillAndExpand" />

                        <Label
                            Grid.Column="0"
                            Margin="6"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{x:Static resX:ResStrings.BtnClose}"
                            TextColor="{x:Static xam:BackColors.Page}"
                            VerticalOptions="Center" />


                        <!--<xam:FontIconLabel
                        Margin="0,0,8,0"
                        FontSize="14"
                        HorizontalOptions="End"
                        InputTransparent="True"
                        Opacity="0.5"
                        Text="{x:Static xam:FaPro.CheckCircle}"
                        TextColor="{x:Static xam:TextColors.Entry}"
                        VerticalOptions="Center" />-->

                    </Grid>


                </ContentView>





            </controls:TouchFrame>


        </StackLayout>

    </Grid>

</xam:PopupDialogBase>