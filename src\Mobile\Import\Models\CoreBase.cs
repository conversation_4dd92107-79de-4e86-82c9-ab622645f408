﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Helpers;
using AppoMobi.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace AppoMobi.Xam
{

    //*****************************************************************
    public partial class CoreBase : BaseViewModelBak
    //*****************************************************************
    {

        private bool _IsActive;
        public bool IsActive
        {
            get { return _IsActive; }
            set
            {
                if (_IsActive != value)
                {
                    _IsActive = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _IsServerConnected;
        public bool IsServerConnected
        {
            get { return _IsServerConnected; }
            set
            {
                if (_IsServerConnected != value)
                {
                    _IsServerConnected = value;
                    OnPropertyChanged();
                }
            }
        }




        #region Tools

        public static Task RunOnMainThreadAsync(Action action)

        {
            var tcs = new TaskCompletionSource<object>();
            MainThread.BeginInvokeOnMainThread(
                () =>
                {
                    try
                    {
                        action();
                        tcs.SetResult(null);
                    }
                    catch (Exception e)
                    {
                        tcs.SetException(e);
                    }
                });

            return tcs.Task;
        }


        public static Task RunOnMainThreadAsync(Task action)

        {
            var tcs = new TaskCompletionSource<object>();
            MainThread.BeginInvokeOnMainThread(
                async () =>
                {
                    try
                    {
                        await action;
                        tcs.SetResult(null);
                    }
                    catch (Exception e)
                    {
                        tcs.SetException(e);
                    }
                });
            return tcs.Task;
        }


        //-----------------------------------------------------------------
        public static void Await(Task action)
        //-----------------------------------------------------------------
        {
            Task task = Task.Run(async () => await action);
            task.Wait();
        }

        //-----------------------------------------------------------------
        public static T Await<T>(Task<T> action)
        //-----------------------------------------------------------------
        {
            Task<T> task = Task.Run(async () => await action);
            return task.Result;
        }

        //-----------------------------------------------------------------
        public static T RunAsyncAsSync<T>(Task<T> action)
        //-----------------------------------------------------------------
        {
            Task<T> task = Task.Run(async () => await action);
            return task.Result;
        }

        //-----------------------------------------------------------------
        public static void RunAsync(Action function)
        //-----------------------------------------------------------------
        {
            System.Threading.Tasks.Task.Run(function).ConfigureAwait(false);
        }


        /*
            //super async
            System.Threading.Tasks.Task.Run(async () =>
            {

             }).ConfigureAwait(false);
         */


        #endregion




        public TimeSpan? TimeSync { get; set; }



        #region NATIVE

        public static bool IsIOS { get; set; }

        public static int BottomInsets { get; set; }


        public static bool IsAndroid { get; set; }

        private static INativeTasks _native = new NativeTasks();
        public static INativeTasks Native
        {
            get
            {
                return _native;//DependencyService.Get<INativeTasks>();
            }
        }

        public static void Mail(string address)
        {
            Native.OpenUrl($"mailto:{address}");
        }

        #endregion

        #region Fonts

        public void SetMainFont(FontPreset preset)
        {
            Native.ExecuteTask("setMainFont", preset.Id); //INativeTasks
        }

        public void SetInterfaceFont(FontPreset preset)
        {
            Native.ExecuteTask("setInterfaceFont", preset.Id); //INativeTasks
        }

        public void RegisterFont(FontPreset preset, string alias)
        {
            Native.ExecuteTask("registerFont", preset.Id, alias); //INativeTasks
        }


        #endregion


        #region NAVIGATION

   
        public bool NavigationPopupEnabled { get; set; }




        #endregion


        #region LANGUAGES

        //public NiftyObservableCollection<TaggedTitle> Languages = new NiftyObservableCollection<TaggedTitle>();

        public virtual void OnLanguageChanged()
        {

        }




        /// <summary>
        /// Can call before using some culture method like converting numbers to strings etc.
        /// Useful after changed language ta run-time.
        /// </summary>


        //public static string GetLanguageName(string twoDigitsCode)

        //{
        //    var culture = CultureInfo.CreateSpecificCulture(twoDigitsCode);
        //    return culture.NativeName.ToTitleCase().ToTitleCase("(");
        //}

        #endregion




        #region Device Info and Logging

        public string DeviceId
        {
            get
            {
                var has = Preferences.Get("DeviceId", "");
                if (string.IsNullOrEmpty(has))
                {
                    var uid = Guid.NewGuid().ToString();
                    Preferences.Set("DeviceId", uid);
                    has = uid;
                }
                return has;
            }
            set
            {
                Preferences.Set("DeviceId", value);
            }
        }

        public new static string BuildDesc
        {
            get { return $"Build {VersionTracking.CurrentBuild}"; }
        }

        public static string DeviceModel => DeviceInfo.Model;

        public static string Build
        {
            get
            {
                return $"{VersionTracking.CurrentBuild}";
            }
        }

        public static string DeviceOs
        {
            get
            {
                var platform = "Unknown";
                var ver = DeviceInfo.Version;
                if (DeviceInfo.Current.Platform == DevicePlatform.Android) platform = "Android";
                else
                if (DeviceInfo.Current.Platform == DevicePlatform.iOS) platform = "iOS";
                return $"{platform} {ver}";
            }
        }

        #endregion






    }




    //*********************************************************
    public class TaggedTitle
    //*********************************************************
    {
        public string Tag { get; set; }
        public string Title { get; set; }
    }
}
