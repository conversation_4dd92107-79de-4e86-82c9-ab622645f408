﻿using System;
using AppoMobi.Touch;
using AppoMobi.Xam.Antispam;



namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
     //****************************************************
	public partial class OptionLine 
     //****************************************************
	{
        
		public OptionLine ()
        
		{
			InitializeComponent ();

		    //cMyArrow.SetIcon(FontIcons.fa_chevron_right, "FaLight");
            //cMyArrow.Font = "FaLight";

		    //cMyArrow.TextColor = BackColors.Outline;
		   // cMyArrow.Update();

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
		    {
		        cBoolSelector.Margin = new Thickness(0,0,8,0);
		    }


            //BindingContext = this;
		}



        //-------------------------------------------------------------
        // ShowLine
        //-------------------------------------------------------------
        private const string nameShowLine = "ShowLine";
        public static readonly BindableProperty ShowLineProperty = BindableProperty.Create(nameShowLine, typeof(bool), typeof(OptionLine), true); //, BindingMode.TwoWay
        public bool ShowLine
        {
            get { return (bool)GetValue(ShowLineProperty); }
            set { SetValue(ShowLineProperty, value); }
        }


        //-------------------------------------------------------------
        // CanEdit
        //-------------------------------------------------------------
        private const string nameCanEdit = "CanEdit";
        public static readonly BindableProperty CanEditProperty = BindableProperty.Create(nameCanEdit, typeof(bool), typeof(OptionLine), true); //, BindingMode.TwoWay
        public bool CanEdit
        {
            get { return (bool)GetValue(CanEditProperty); }
            set { SetValue(CanEditProperty, value); }
        }	


	    //-------------------------------------------------------------
	    // ActionDesc
	    //-------------------------------------------------------------
	    private const string nameActionDesc = "ActionDesc";
	    public static readonly BindableProperty ActionDescProperty = BindableProperty.Create(nameActionDesc, typeof(string), typeof(OptionLine), string.Empty); //, BindingMode.TwoWay
	    public string ActionDesc
	    {
	        get { return (string)GetValue(ActionDescProperty); }
	        set { SetValue(ActionDescProperty, value); }
	    }

        //-------------------------------------------------------------
        // SelectionDesc
        //-------------------------------------------------------------
        private const string nameSelectionDesc = "SelectionDesc";
        public static readonly BindableProperty SelectionDescProperty = BindableProperty.Create(nameSelectionDesc, typeof(string), typeof(OptionLine), string.Empty); //, BindingMode.TwoWay
        public string SelectionDesc
        {
            get { return (string)GetValue(SelectionDescProperty); }
            set { SetValue(SelectionDescProperty, value); }
        }	
       
        //-------------------------------------------------------------
        // SwitchValue
        //-------------------------------------------------------------
        private const string nameSwitchValue = "SwitchValue";
        public static readonly BindableProperty SwitchValueProperty = BindableProperty.Create(nameSwitchValue, typeof(bool), typeof(OptionLine), false, BindingMode.TwoWay); 
        public bool SwitchValue
        {
            get { return (bool)GetValue(SwitchValueProperty); }
            set { SetValue(SwitchValueProperty, value); }
        }



        //-------------------------------------------------------------
        // IsSwitch
        //-------------------------------------------------------------
        private const string nameIsSwitch = "IsSwitch";
        public static readonly BindableProperty IsSwitchProperty = BindableProperty.Create(nameIsSwitch, typeof(bool), typeof(OptionLine), false); //, BindingMode.TwoWay
        public bool IsSwitch
        {
            get { return (bool)GetValue(IsSwitchProperty); }
            set { SetValue(IsSwitchProperty, value); }
        }	


        #region EventHandlers

	    public event EventHandler OnDownHandler;

	    public event EventHandler OnTappedHandler;


	    
	    private void OnTapped(object sender, TapEventArgs e)
	    
        {
            try
            {
                if (!CanEdit)
                    return;

               // this.DisableSpamClicks();

                OnTappedHandler?.Invoke(this, e);
            }
            catch (Exception exception)
            {
                App.Logger.Error(this.GetType().Name, exception);
            }

          
	    }

        
        private void CGrid_OnUp(object sender, DownUpEventArgs e)
            
        {
            this.Scale = 1.0;
            //    imgAvatar.Scale=1.0;
        }
        
        private void CGrid_OnDown(object sender, DownUpEventArgs e)
            
        {
            this.Scale = 0.99;

            SwitchValue = !SwitchValue;
            OnDownHandler?.Invoke(this, e);
            //  imgAvatar.Scale = 1.05;
        }

        private AntiSpamClick Clicker_Grid_OnTapped { get; set; }
	    private void OnDown(object sender, DownUpEventArgs e)
        
        {
	        if (Clicker_Grid_OnTapped == null)
	        {
	            Clicker_Grid_OnTapped = new AntiSpamClick(sender, e);

	            Clicker_Grid_OnTapped.ClickFunc += async (sEnder, pArams) =>
	            {
                    
	                SwitchValue = !SwitchValue;
	                OnDownHandler?.Invoke(this, e);
                    
                };
	        }
	        Clicker_Grid_OnTapped.Click(sender, e);
	    }


        #endregion

	    
	    protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {
                case nameSelectionDesc:
                    cSelectedDesc.Text = SelectionDesc;
                    break;

	            case nameActionDesc:
	                cAction.Text = ActionDesc;
	                break;

	            //property changed
	            case nameShowLine:
	                BottomLine.IsVisible = ShowLine;
	                break;


                case nameCanEdit:
                    cIndicator.IsVisible = CanEdit;
                    break;

                case nameIsSwitch:

	                if (IsSwitch)
	                {
	                    cSelected.IsVisible = false;
	                    cBoolSelector.IsVisible = true;
	                }
	                else
	                {
	                    cBoolSelector.IsVisible = false;
	                    cSelected.IsVisible = true;
	                }
                    break;

                case nameSwitchValue:
                    cBoolSelector.IsToggled = SwitchValue;
                    break;



            }

	    }


	}
}