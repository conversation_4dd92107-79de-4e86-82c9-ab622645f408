﻿using System;
using System.Globalization;


namespace AppoMobi.Forms.Framework.Xaml
{
    public class AddValueResultOverZeroConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if ((double)value < 0.0)
                    return 0.0;

                var divider = ((string)parameter).ToDouble();
                var ret = (double)value + divider;

                if (ret > 0)
                    return ret;

                return 0.0;
            }
            catch (Exception e)
            {
            }

            return 0.0;
        }


    }
}