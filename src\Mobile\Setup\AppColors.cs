﻿
using AppoMobi.Tenant;

using AppoMobi.Xam;


namespace AppoMobi
{



    public class AppColors
    {

        public static Color PopupOverlay = Color.Parse("#AA000000");

        public static Color ControlPrimary => Colors.Black;

        //derivatives
        public static string ColorPrimary_dark => TenantOptions.ColorPrimary.MakeDarker(25);
        public static string ColorPrimary_darkest => TenantOptions.ColorPrimary.MakeDarker(50); //copyright text
        public static string ColorPrimary_light => TenantOptions.ColorPrimary.MakeLighter(25);//"#d0e3e4";
        public static string ColorPrimary_lightest => TenantOptions.ColorPrimary.MakeLighter(85);//"#d0e3e4";
        public static string ColorPrimary_icons => ColorPrimary_lightest.MakeDarker(61);
        public static string ColorPrimary_highlight => TenantOptions.ColorPrimary.MakeLighter(93);//"#d0e3e4"; 

        public static string ColorControlsDark => TenantOptions.ColorControls.MakeDarker(25); //copyright text
        public static string ColorControlsLightest => TenantOptions.ColorControls.MakeLighter(85);//"#d0e3e4";


        public static string text => TenantOptions.ColorText;//"#74aaad";//"62a0a4";//"4fa7b7";//"#62a0a4"; //navigation 

        public static string icons => TenantOptions.ColorIcons;//"#74aaad";//"62a0a4";//"4fa7b7";//"#62a0a4"; //navigation 

        public static string navigation => TenantOptions.ColorNavigation;//"#74aaad";//"62a0a4";//"4fa7b7";//"#62a0a4"; //navigation 

        public static string primary => TenantOptions.ColorPrimary;//"#74aaad";//"62a0a4";//"4fa7b7";//"#62a0a4"; //navigation 

        //public static  string secondary => Constants.ColorSecondary;//"#74aaad";//"62a0a4";//"4fa7b7";//"#62a0a4"; //navigation 

        public static string cards => TenantOptions.CardsBack;//buttons, //tabbbedbar background
        public static string cards2 => TenantOptions.Cards2Back;//buttons, //tabbbedbar background



        //try primary +19 +44 +0 => 4791be

        //brand 
        public static string primary_dark => primary.MakeDarker(25);//buttons, //tabbbedbar background
        public static string primary_dark_trans => primary_dark.SetHexAlpha(50);//buttons, //tabbbedbar background
        //        public static  string primary_dark => "52787b";//buttons, //tabbbedbar background
        public static string primary_darkest => primary.MakeDarker(50); //copyright text
        public static string primary_darkestest => primary.MakeDarker(80); //copyright text
        public static string primary_light => primary.MakeLighter(25);//"#d0e3e4";
        public static string primary_lightest => primary.MakeLighter(85);//"#d0e3e4";
        public static string primary_transparent => primary.MakeLighter(95);//"#d0e3e4"; 
        public static string primary_icons => primary_lightest.MakeDarker(61);
        public static string primary_highlight => primary.MakeLighter(93);//"#d0e3e4"; 


        public static double interface_prodcat_h => 110;//"#d0e3e4"; 
        public static double interface_prodcat_h_small => 92;//"#d0e3e4"; 
        public static double interface_product_h_small => 250;//"#d0e3e4"; 
        public static double interface_product_h_big => 350;//"#d0e3e4"; 

        public static double interface_prodcat_h_Tab => 250;//"#d0e3e4"; 
        public static double interface_prodcat_h_small_Tab => 190;//"#d0e3e4"; 

        public static double interface_product_h_small_Tab => 250;//"#d0e3e4"; 
        public static double interface_product_h_big_Tab => 350;//"#d0e3e4"; 

        //accent

        //public static  string controls_special => "#e3ae2a";//"#ffc601";
        //public static  string controls_special_light => controls_special.MakeLighter(25);//"#d0e3e4";
        //public static  string controls_special_dark => controls_special.MakeDarker(25);

        public static Color ProgressBar => Color.Parse(TenantOptions.ProgressBar);

        public static string accent => TenantOptions.ColorAccent;//"#b99e54";//"#c7ac56";

        public static string controls => TenantOptions.ColorControls;//"#b99e54";//"#c7ac56";
        public static string controls_darker => controls.MakeDarker(10);
        public static string controls_dark => controls.MakeDarker(25);//"#a3905c";
        public static string controls_darkest => controls.MakeDarker(50); //copyright text
        public static string controls_lighter => controls.MakeLighter(10);
        public static string controls_light => controls.MakeLighter(25);//"#d0e3e4";
        public static string controls_lightest => controls.MakeLighter(60);//"#d0e3e4";
        public static string controls_highlight => controls.MakeLighter(93);//"#d0e3e4";
        public static string controls_dark_desat => controls_dark.MakeLighter(60);//"#a3905c";
        public static string controls_highlight_manual => "#f5f3ee";

        public static Color AccentHM => Color.Parse(controls_highlight_manual);

        public static Color ColoredIcons => Color.Parse(TenantOptions.ColoredIcons);

        public static string site_panelgrey => "#484848";
        public static Color Site_Panel => Color.Parse(site_panelgrey).MultiplyAlpha(0.60f);
        public static Color Site_PanelHider => Colors.Black.MultiplyAlpha(0.9f);
        public static Color Site_PanelX => Color.Parse(site_panelgrey).MultiplyAlpha(0.5f);
        public static Color Site_PanelXX = Color.Parse(site_panelgrey).MultiplyAlpha(0.25f);
        public static Color Site_TitleGrey => Color.Parse(site_panelgrey);

        public static Color WallpaperBackground => Color.Parse(TenantOptions.WallpaperBAckground);

        //b&w
        public static string bw_highlight => "#ffffff";

        public static string bw_white_almost => "#fefefe";

        public static string bw_grey_lightest => bw_highlight.MakeDarker(25);
        public static string bw_grey_light => bw_highlight.MakeDarker(47);//"#878787";
        public static string bw_grey => bw_highlight.MakeDarker(70);//"#555555"); //cards etc
        public static string bw_grey_dark => bw_highlight.MakeDarker(80);//"#333333"; //news
        public static string bw_darkest => bw_highlight.MakeDarker(89);//"#202020";
        public static string bw_black => bw_highlight.MakeDarker(100);

        public static string bw_footer => "#f6f6f6";
        public static Color Footer => Color.Parse(bw_footer);


        //monochrome a bit tinted TODO make mono transparent
        //public static  string txt_disabled => "#cedadb"; //droid tabbed bar disabled text
        public static string txt_disabled_muted => "#9ab1b3"; //droid tabbed bar disabled text

        //use: 
        // 

        //public static  string controls_muted => "#aba05f";
        //public static  string controls_darkest => "#86743a";


        //manual colors
        public static Color _TransBlack => Color.Parse("#77000000");// cc 57 

        public static Color White => Colors.White;

        public static Color WhiteAlmost => Color.Parse(bw_white_almost);// cc 57 

        //MENU
        public static Color MenuBack => Color.Parse(TenantOptions.MenuBack);
        public static Color TabsIcons => Color.Parse(TenantOptions.TabsIcons);

        public static Color Divider => Color.Parse(TenantOptions.ColorDivider);
        public static Color CardsHeaderText => Color.Parse(TenantOptions.CardsHeaderText);

        public static Color Shadow => PrimaryDark.MultiplyAlpha(0.5f);


        //White
        public static Color WhiteSemiVisible => Colors.White.MultiplyAlpha(0.33f);
        public static Color WallpaperWhite => Colors.White.MultiplyAlpha(0.55f);
        public static Color WhiteTransparent => Colors.White.MultiplyAlpha(0.65f);
        public static Color ColorLoadingMask => Colors.White.MultiplyAlpha(0.6f);
        public static Color WhiteTrans => White.MultiplyAlpha(0.85f);
        public static Color WhiteMaybeTrans => White.MultiplyAlpha(0.98f);

        //Black
        public static Color BlackTransparentDarker => Colors.Black.MultiplyAlpha(0.1f);
        public static Color Overlay => Colors.Black.MultiplyAlpha(0.12f);
        public static Color Wallpaper => Colors.Black.MultiplyAlpha(0.20f);
        public static Color WallpaperNav => Colors.Black.MultiplyAlpha(0.20f);
        public static Color WallpaperA => Colors.Black.MultiplyAlpha(0.50f);

        public static Color BlackTransparent => Colors.Black.MultiplyAlpha(0.085f);
        public static Color BlackProduct => Colors.Black.MultiplyAlpha(0.15f);

        public static Color PopupDarken => Colors.Black.MultiplyAlpha(0.5f);
        public static Color OverlayBtns => Colors.Black.MultiplyAlpha(0.2f);


        public static Color BlackTransparent2 => Colors.Black.MultiplyAlpha(0.75f);
        public static Color BlackNav => Colors.Black.MultiplyAlpha(0.90f);


        public static Color Text => Color.Parse(text);
        public static Color Cards => Color.Parse(cards);
        public static Color Cards2 => Color.Parse(cards2);

        public static Color Icons = Color.Parse(icons);
        public static Color IconsNavRight = Icons;

        public static Color Navigation => Color.Parse(navigation);

        //Xamarin Forms Color types
        public static Color Primary => Color.Parse(primary);

        public static Color PrimaryDark => Color.Parse(primary_dark);
        public static Color PrimaryDarkTransparent => Color.Parse(primary_dark).MultiplyAlpha(0.75f);
        public static Color PrimaryDarkTransparent50 => Color.Parse(primary_dark).MultiplyAlpha(0.5f);
        public static Color PrimaryIcons => Color.Parse(primary_icons);
        public static Color PrimaryDarkest => Color.Parse(primary_darkest);
        public static Color PrimaryLight => Color.Parse(primary_light);
        public static Color PrimaryLightest => Color.Parse(primary_lightest);
        public static Color PrimaryTransparent => Color.Parse(primary_transparent);
        public static Color PrimaryHighlight => Color.Parse(primary_highlight);


        public static Color PrimaryDark75 => Color.Parse(primary_dark).MultiplyAlpha(0.75f);
        public static Color PrimaryTrans => Color.Parse(primary).MultiplyAlpha(0.65f);

        public static Color DarkPrimaryPanel => Color.Parse(primary_dark).MultiplyAlpha(0.65f);
        public static Color DarkenWallpaper => Color.Parse(primary_darkestest).MultiplyAlpha(0.75f);

        public static Color Controls => Color.Parse(TenantOptions.ColorControls);

        public static Color Accent => Color.Parse(accent);

        //public static  Color AccentSpecialLight => Color.Parse(controls_special_light);
        //public static  Color AccentSpecial => Color.Parse(controls_special);
        //public static  Color AccentSpecialDark => Color.Parse(controls_special_dark);
        public static Color AccentHighlight => Color.Parse(controls_highlight);
        public static Color AccentLightest => Color.Parse(controls_lightest);
        public static Color AccentLight => Color.Parse(controls_light);
        public static Color AccentLighter => Color.Parse(controls_lighter);
        public static Color AccentDarker => Color.Parse(controls_darker);
        public static Color AccentTrans => Accent.MultiplyAlpha(0.50f);
        public static Color AccentTrans75 => Accent.MultiplyAlpha(0.75f);
        public static Color AccentDark => Color.Parse(controls_dark);
        public static Color AccentDarkest => Color.Parse(controls_darkest);
        public static Color AccentDark75 => AccentDark.MultiplyAlpha(0.60f);
        public static Color AccentDark90 => AccentDark.MultiplyAlpha(0.90f);
        public static string HexAccentDark75 => AccentDark75.GetHexString();


        public static Color BwHighlight => Color.Parse(bw_highlight);
        public static Color BwDarkest => Color.Parse(bw_darkest);
        public static Color BwBlack => Color.Parse(bw_black);

        public static Color BwGrey => Color.Parse(bw_grey);
        public static Color BwGreyDark => Color.Parse(bw_grey_dark);
        public static Color BwGreyLight => Color.Parse(bw_grey_light);
        public static Color BwGreyLightest => Color.Parse(bw_grey_lightest);

        //popups
        public static Color PopupPageBackground => Color.Parse("#50000000");
        public static Color PopupHeaderBackground => BlackNav;
        public static Color PopupListBackground => PrimaryDark75;
        public static Color PopupHeaderText => BackgroundNav;
        public static Color PopupListText => BwHighlight;
        public static Color PopupListDivider => BlackProduct;

        //Assignements

        public static Color CardsBack => Color.Parse(TenantOptions.CardsBack);
        //public static  Color CardsHeader => Color.Parse(TenantOptions.CardsHeader);


        public static Color Accent90 => Accent.MultiplyAlpha(0.90f);
        public static Color Primary90 => Primary.MultiplyAlpha(0.90f);

        public static Color LoadingBack => BwGreyLight.MultiplyAlpha(0.66f);

        public static Color BackgroundNav => Color.Parse(navigation).MultiplyAlpha(0.98f);

        public static Color DividerNav => PrimaryDark;
        public static Color DividerNavTabs = BackgroundNav.MakeDarker(33);

        public static Color TitleBarBackground => BackgroundNav;//PrimaryTransparent.MultiplyAlpha(0.98);//BackgroundNav;  //omg
        public static Color StatusBarBackground => Colors.Black;//PrimaryTransparent.MultiplyAlpha(0.98);//BackgroundNav;  //omg
        public static Color TitleBarDivider => DividerNav;

        public static Color TitleBarBackgroundA => Color.Parse("#fafafa").MultiplyAlpha(0.55f);


        //Tabbed Menu iOS
        //options
        public static bool ios_tabs_showtext = true; //tabbar items unselected
        public static bool ios_tabs_showtextshadow = false; //tabbar items unselected



        //status bar
        public static float ios_statusbar_a = 0.70f; //15%

        //background
        public static string ios_tabs_background => TenantOptions.TabsBack;// primary_transparent;//"e1eded"); //"2b4446";//
        public static string ios_tabs_background_img = "";//"back2.jpg";
        public static float ios_tabs_background_img_a = 0.10f; //15%
        //selector image
        public static string ios_tabs_selected_img = "";//"tb.bk.sel.png";
        public static float ios_tabs_selected_img_a = 0.15f;



        //selected 
        public static string ios_tabs_selected => TenantOptions.TabsIconsSel;// primary.MakeDarker(12);//"749ba2";//"b99e54";//"91e3e6";//"77f4ff";//"519093";//"598285";//controls_muted;
        public static string ios_tabs_selected_txt => ios_tabs_selected;//"77f4ff";//"598285";//controls_muted;//accent;

        //unselected
        public static string ios_tabs_unselected => TenantOptions.TabsIcons;// primary.MakeLighter(65); //"#96b9bb".MakeDarker(20);//"889fa0");//"acbebf";//"a5c9cb";//primary_lightest;//"7f9393";//primary_dark;//"99aeaf";
        //        public static  string ios_tabs_unselected = "767676";//"a5c9cb";//primary_lightest;//"7f9393";//primary_dark;//"99aeaf";
        public static string ios_tabs_unselected_txt => ios_tabs_unselected;//"a5c9cb";//primary_lightest;//"7f9393";//primary_dark;//txt_disabled_muted; //tabbar items unselected


        //   public static  float ios_tabs_unselected_a = 0.75f;
        //   public static float ios_tabs_selected_a = 0.9f;
        //  public static float ios_tabs_selected_txt_a => ios_tabs_selected_a;
        //     public static float ios_tabs_unselected_txt_a => ios_tabs_unselected_a;

        //light skin
        public static float ios_tabs_selected_notxt_a = 0.85f;//> ios_tabs_unselected_notxt_a;//0.75f;
        public static float ios_tabs_unselected_notxt_a = 0.85f;//0.66f;

        //dark branded
        public static float tabs_unselected_a = 0.95f;//0.66f;
        public static float tabs_selected = 1.0f;


        //font
        //public static  string ios_tabs_font => TenantOptions.AppleTabsFont;//default
        public static string ios_tabs_font_sel => "";


        
        //second skin: icons only
        
        public static string ios_tabs_background_notxt => TenantOptions.TabsBack;//;//primary;//"5e9597";

        public static string ColorTabsIconUnselected => TenantOptions.TabsIcons; //primary_darkest;
        public static string ColorTabsTextUnselected => TenantOptions.TabsText;


        public static string ios_tabs_selected_notxt => TenantOptions.TabsIconsSel;//primary.MakeLighter(85);//TenantOptions.TabsIconsSel;//"#b99e54";//"71dee2";

        //hello


        //Tabs
        public static Color TabsBackgroud => Color.Parse(ios_tabs_background_notxt);
        public static Color TabsUnselected => Color.Parse(ColorTabsIconUnselected).MultiplyAlpha(ios_tabs_unselected_notxt_a);
        public static Color TabsSelected => Color.Parse(ios_tabs_selected_notxt).MultiplyAlpha(ios_tabs_selected_notxt_a);








    }

    //******************************************************************
}
