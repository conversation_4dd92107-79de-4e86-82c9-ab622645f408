﻿<?xml version="1.0" encoding="UTF-8"?>
<appoMobi:CFrame xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:appoMobi="clr-namespace:AppoMobi"
             xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
             x:Class="AppoMobi.UI.InfoCard"
                 x:Name="cCatDesc"
                 IsVisible="False"
                 Margin="20,12,20,8"
                 Padding="0"
                 BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                 HasShadow="False"
                 HorizontalOptions="Fill"
                 BorderColor="{x:Static appoMobi:AppColors.DividerNav}">
    <StackLayout HorizontalOptions="Fill" Spacing="0" Margin="9,8,9,8">
        <Label
            x:Name="txtLabel"
            Margin="{StaticResource Thickness_InfoCard}"
            Text="{x:Static resX:ResStrings.SeaAlso}"
            HorizontalOptions="Center"
            FontSize="{StaticResource FontSize_CellProductDesc}"
            Style="{StaticResource Style_ProductDescPreview}"/>
    </StackLayout> 
</appoMobi:CFrame>