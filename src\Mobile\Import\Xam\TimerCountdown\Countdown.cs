﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Timers;
using Timer = System.Timers.Timer;

//using AppoMobi.Forms.Common.ResX;
 

namespace AppoMobi.Xam
{
    //****************************************************
    public class Countdown : INotifyPropertyChanged
    //****************************************************
    {
        /// <summary>
        /// Gets the start date time.
        /// </summary>
        public DateTime StartDateTime { get; private set; }

        /// <summary>
        /// Gets the remain time in seconds.
        /// </summary>
        public double RemainTime
        {
            get { return remainTime; }

            private set
            {
                remainTime = value;
                OnPropertyChanged();
                OnPropertyChanged("RemainTimeSecondsText");
                OnPropertyChanged("RemainTimeFullText");
            }
        }

        /// <summary>
        /// Gets the remain time in seconds.
        /// </summary>
        
        public string RemainTimeSecondsText
        
        {
            get
            {
                secondsToExplain = (int) remainTime;
                OnPropertyChanged("RemainTimeExplainSeconds");
                if (secondsToExplain < 1.0) return "0";
                return secondsToExplain.ToString("##");
            }
        }


        
        public string RemainTimeFullText
        
        {
            get
            {
//                secondsToExplain = (int)remainTime;
                TimeSpan time = TimeSpan.FromSeconds(remainTime);
                //here backslash is must to tell that colon is
                //not the part of format, it just a character that we want in output
//                string str = time.ToString(@"hh\:mm\:ss\:fff");
                string str = time.ToString(@"hh\:mm\:ss");
//                OnPropertyChanged("RemainTimeExplainSeconds");

                // if (secondsToExplain < 1.0) return "00:00:00";

                return str;
            }
        }

        
        public string StartTimeFullText
        
        {
            get
            {
                TimeSpan time = TimeSpan.FromSeconds(startTime);
                //here backslash is must to tell that colon is
                //not the part of format, it just a character that we want in output
                //                string str = time.ToString(@"hh\:mm\:ss\:fff");
                string str = time.ToString(@"hh\:mm\:ss");
                //                OnPropertyChanged("RemainTimeExplainSeconds");

                // if (secondsToExplain < 1.0) return "00:00:00";

                return str;
            }
        }


        private int secondsToExplain { get; set; }

        /*
        
        public string RemainTimeExplainSeconds
        
        {
            get
            {
                var seconds = secondsToExplain.ExplainToString(
                    ResStrings.ExplainSeconds_0,
                    ResStrings.ExplainSeconds_1,
                    ResStrings.ExplainSeconds_X1,
                    ResStrings.ExplainSeconds_X2,
                    ResStrings.ExplainSeconds_X);

                //if (remainTime < 1.0) return "0";
                return " "+seconds;
            }
        }
        */

        /// <summary>
        /// Occurs when completed.
        /// </summary>
        public event Action Completed;

        /// <summary>
        /// Occurs when ticked.
        /// </summary>
        public event Action Ticked;

        public event Action OnHalfMinute;
        public event Action OnFullMinute;


        /// <summary>
        /// The timer.
        /// </summary>
        Timer timer;

        /// <summary>
        /// The remain time.
        /// </summary>
        double remainTime;

        /// <summary>
        /// The remain time total.
        /// </summary>
        double remainTimeTotal;

        private double startTime;

        //
        //public void StartUpdating(double total, Action actionOnComplete, double period = 1.0)
        //
        //{
        //    Completed = actionOnComplete;
        //    StartUpdating(total, period);
        //}

        
        /// <summary>
        /// Starts the updating with specified period, total time and period are specified in seconds.
        /// </summary>
        
        public void StartUpdating(double total, double millisecsUpdate = 1000)
        
        {
            if (timer != null)
            {
                StopUpdating();
            }

            Set(total);

            StartDateTime = DateTime.Now;

            timer = new Timer(millisecsUpdate);
            //timer = new Timer();
            timer.Elapsed += InvokeTicked;
            timer.Enabled = true;
        }

        private void InvokeTicked(object sender, ElapsedEventArgs e)
        {
             Tick();
        }

        
        /// <summary>
        /// Stops the updating.
        /// </summary>
        
        public void StopUpdating()
            
        {
            //RemainTime = 0;
            remainTimeTotal = 0;

            if (timer != null)
            {
                timer.Elapsed -= InvokeTicked;
                timer.Enabled = false;
                timer = null;
            }
        }

        
        public void Set(double total)
        
        {
            startTime = total;
            remainTimeTotal = total;
            RemainTime = total;
            lastHalfMinute = -1;
        }

        private int lastHalfMinute = -1;



        public bool NoMillisecs=true;

        public TimeSpan TimeRemaining
        {
            get
            {
                return TimeSpan.FromSeconds(RemainTime);
            }
        }

        /// <summary>
        /// Updates the time remain.
        /// </summary>
        
        public void Tick()
        
        {
            var delta = (DateTime.Now - StartDateTime).TotalSeconds;

            bool stop = false;

            if (delta < remainTimeTotal && delta > 0)
            {
                RemainTime = remainTimeTotal - delta;

                var remainingTime = TimeSpan.FromSeconds(RemainTime);

                if (remainingTime.Seconds == 30)
                {
                    if (remainingTime.Seconds != lastHalfMinute)
                    {
                        lastHalfMinute = remainingTime.Seconds;
                        OnHalfMinute?.Invoke();
                    }
                }

                if (remainingTime.Seconds == 0)
                {
                    if (remainingTime.Seconds != lastHalfMinute)
                    {
                        lastHalfMinute = remainingTime.Seconds;
                        OnFullMinute?.Invoke();
                    }
                }

                if (RemainTime < 1.0) stop = true;

                Ticked?.Invoke();
            }
            else
            {
                stop = true;
            }

            if (stop)
            {
                StopUpdating();

                RemainTime = 0;

                var completed = Completed;
                if (completed != null)
                {
                    completed();
                }
            }

        }

        #region INotifyPropertyChanged implementation

        /// <summary>
        /// Occurs when property changed.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the property changed event.
        /// </summary>
        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null)
            {
                handler(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        #endregion
    }
}
