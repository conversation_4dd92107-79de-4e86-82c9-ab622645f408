﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class StringIsEqual : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {

            try
            {
                if ((string)value == (string)parameter)
                {
                    return true;
                }
                return false;
            }
            catch (Exception e)
            {
                return value;
            }

        }
    }
}