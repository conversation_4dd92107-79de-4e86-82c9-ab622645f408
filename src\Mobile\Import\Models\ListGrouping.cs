﻿using System.Collections.Generic;
using System.Collections.Specialized;
using AppoMobi.Xam;


namespace AppoMobi.Models
{
    public class ListGrouping<TKey, TItem> : NiftyObservableCollection<TItem>
    {
        /// <summary>
        /// Gets the key.
        /// </summary>
        /// <value>The key.</value>
        public TKey Key
        {
            get;
        }

        /// <summary>
        /// Returns list of items in the grouping.
        /// </summary>
        public new IList<TItem> Items => base.Items;

        /// <summary>
        /// Initializes a new instance of the Grouping class.
        /// </summary>
        /// <param name="key">Key.</param>
        /// <param name="items">Items.</param>
        public ListGrouping(TKey key, IEnumerable<TItem> items)
        {
            Key = key;
            base.AddRange(items, NotifyCollectionChangedAction.Add);
        }
    }
}
