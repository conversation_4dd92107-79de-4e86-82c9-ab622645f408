﻿<?xml version="1.0" encoding="UTF-8"?>
<StackLayout xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
             
             Padding="5,0,5,5" Spacing="2"
             HorizontalOptions="Center"
             
             x:Class="AppoMobi.UI.DevFooter">

            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="AppoMobi_OnTapped" />
            </StackLayout.GestureRecognizers>
            <Label
                HorizontalOptions="Center"
                FontSize="10.0"                 
                TextColor ="#55FFFFFF"
                x:Name="txtDev"
                Text="{x:Static resX:ResStrings.Settings_Copyright}" />
            <Label
                HorizontalOptions="Center"
                FontSize="10.0"                 
                TextColor ="#55FFFFFF"
                Text="www.artoffoto.com" />
            <Label
                IsVisible="False"
                HorizontalOptions="Center"
                
                FontSize="10.0"                 
                TextColor ="#55FFFFFF"
                
                x:Name="txtAppVersion" />
        </StackLayout>
