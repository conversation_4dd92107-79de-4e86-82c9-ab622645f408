﻿


namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ScrollWithBanner
    {
        //-----------------------------------------------------------------
        public ScrollWithBanner()
        //-----------------------------------------------------------------
        {
            InitializeComponent();

            // *** the only PARALLAX line ***
            MainScroll.ParallaxView = HeaderView;
        }

        
        // DO NOT CLOSE - OnPropertyChanged
        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            switch (propertyName)
            {

                case nameBannerBack:
                    HeaderView.Content = BannerBack;
                    break;
                
                case nameBannerOverlay:
                    ViewBannerOverlay.Content = BannerOverlay;
                    break;

                case nameScrollContent:
                    ViewScrollContent.Content = ScrollContent;
                    break;
            }



            base.OnPropertyChanged(propertyName);
        }

        
        // BannerBack
        
        private const string nameBannerBack = "BannerBack";
        public static readonly BindableProperty BannerBackProperty = BindableProperty.Create(nameBannerBack, typeof(Microsoft.Maui.Controls.Grid), typeof(ScrollWithBanner), null); //, BindingMode.TwoWay
        public Microsoft.Maui.Controls.Grid BannerBack
        {
            get { return (Microsoft.Maui.Controls.Grid)GetValue(BannerBackProperty); }
            set { SetValue(BannerBackProperty, value); }
        }

        
        // BannerOverlay
        
        private const string nameBannerOverlay = "BannerOverlay";
        public static readonly BindableProperty BannerOverlayProperty = BindableProperty.Create(nameBannerOverlay, typeof(Microsoft.Maui.Controls.Grid), typeof(ScrollWithBanner), null); //, BindingMode.TwoWay
        public Microsoft.Maui.Controls.Grid BannerOverlay
        {
            get { return (Microsoft.Maui.Controls.Grid)GetValue(BannerOverlayProperty); }
            set { SetValue(BannerOverlayProperty, value); }
        }

        
        // ScrollContent
        
        private const string nameScrollContent = "ScrollContent";
        public static readonly BindableProperty ScrollContentProperty = BindableProperty.Create(nameScrollContent, typeof(Microsoft.Maui.Controls.StackLayout), typeof(ScrollWithBanner), null); //, BindingMode.TwoWay
        public Microsoft.Maui.Controls.StackLayout ScrollContent
        {
            get { return (Microsoft.Maui.Controls.StackLayout)GetValue(ScrollContentProperty); }
            set { SetValue(ScrollContentProperty, value); }
        }

    }
}