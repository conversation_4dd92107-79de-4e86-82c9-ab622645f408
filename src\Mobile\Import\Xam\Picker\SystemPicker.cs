﻿using System;
using System.Collections.Generic;

namespace AppoMobi.Xam
{
    public class SystemPicker : ContentView
    {
        
        public SystemPicker()
        
        {
            Opacity = 0;
            IsClippedToBounds = true;
            HeightRequest = 1;
        }

        //-----------------------------------------------------------------
        protected void Init()
        //-----------------------------------------------------------------
        {
            if (MainPicker != null)
            {
                MainPicker.SelectedIndexChanged -= OnIndexChanged_Picker;
                Content = null;
            }
            MainPicker = new Picker();
            MainPicker.SelectedIndexChanged += OnIndexChanged_Picker;
            foreach (var position in Items)
            {
                MainPicker.Items.Add(position);
            }
            Content = MainPicker;
        }

        private Picker MainPicker { get; set; } = null;
        
        public void Show()
        
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                Init();
                MainPicker.Focus();
            });
        }

        
        public void Hide()
        
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                MainPicker.Unfocus();
            });
        }

        //-------------------------------------------------------------
        // SelectedValue
        //-------------------------------------------------------------
        private const string nameSelectedValue = "SelectedValue";
        public static readonly BindableProperty SelectedValueProperty = BindableProperty.Create(nameSelectedValue, typeof(string), typeof(SystemPicker), string.Empty); //, BindingMode.TwoWay
        public string SelectedValue
        {
            get { return (string)GetValue(SelectedValueProperty); }
            set { SetValue(SelectedValueProperty, value); }
        }

        //-------------------------------------------------------------
        // Items
        //-------------------------------------------------------------
        private const string nameItems = "Items";
        public static readonly BindableProperty ItemsProperty = BindableProperty.Create(nameItems, typeof(IList<string>), typeof(SystemPicker), null); //, BindingMode.TwoWay
        public IList<string> Items
        {
            get { return (IList<string>)GetValue(ItemsProperty); }
            set { SetValue(ItemsProperty, value); }
        }

        
        private void OnIndexChanged_Picker(object sender, EventArgs e)
        
        {
            try
            {
                var maybeValue = Items[MainPicker.SelectedIndex];
                SelectedValue = maybeValue;
            }
            catch (Exception exception)
            {
            }
             
            OnSelected?.Invoke(sender, MainPicker.SelectedIndex);
        }

        public event EventHandler<int> OnSelected;

    }
}
