﻿using System;
using AppoMobi.Forms.Common.ResX;


namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DevFooter 
    {
        
        public DevFooter()
        
        {
            InitializeComponent();
            var AppVersion = ", v"+  Core.Native.GetAppVersion();
            txtAppVersion.Text = AppVersion;
            txtDev.Text = ResStrings.Settings_Copyright  + AppVersion;
        }
        
        private void AppoMobi_OnTapped(object sender, EventArgs e)
        
        {


        }
    }
}