﻿using System;
using System.Globalization;

namespace AppoMobi.Xam.Converters
{
    public class FadeOutConverter : ConverterBase
    {

        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            double result=1.0;

            if (value is bool input)
            {
                if ((bool) value == true)
                {
                    result = 0.85;
                }
            }

            return result;
        }
    }
}
