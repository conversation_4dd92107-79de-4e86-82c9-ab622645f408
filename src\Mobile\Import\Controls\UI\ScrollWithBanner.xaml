﻿<?xml version="1.0" encoding="UTF-8"?>
<Grid xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:appoMobi="clr-namespace:AppoMobi"
 
              RowSpacing="0"
              HorizontalOptions="FillAndExpand"
              VerticalOptions="FillAndExpand"
             x:Class="AppoMobi.ScrollWithBanner">



        <Grid.RowDefinitions>
            <RowDefinition x:Name="Row1Header" Height="200" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--banner background-->
    <ContentView IsClippedToBounds="True" x:Name="HeaderView"/>

    <!--InsertBannerBack GRID-->

        <!--ROW 0-1 parallax-->
        <appoMobi:ParallaxScrollView
                x:Name="MainScroll"
                Grid.RowSpan="2"
                VerticalOptions="FillAndExpand">

            <!--this is streching okay-->
        <Grid
                RowSpacing="0"
                VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition x:Name="Row1Content" Height="180" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <ContentView IsClippedToBounds="True" x:Name="ViewBannerOverlay"/>

            <!--DATA row1-->
            <ContentView IsClippedToBounds="True" x:Name="ViewScrollContent" 
                         Grid.Row="1"/>

        </Grid>
    </appoMobi:ParallaxScrollView>



</Grid>