﻿using System;
using System.Collections.Generic;

namespace AppoMobi.Services
{
    public class LoggerMock
    {
        public void Debug(string tag, string message)
        {
            System.Diagnostics.Debug.WriteLine($"[{tag}]: {message}");
        }


        public void Info(string tag, string message, bool full = false)
        {
            var tags = new Dictionary<string, string>
            {
                { "Message",  message },
            };

            //#if !DEBUG

            //YandexMetrica.Implementation.ReportEvent($"{tag}", tags);

            //#endif

            System.Diagnostics.Debug.WriteLine($"[{tag}]: {message}");
        }

        public void Error(string tag, Exception exception)
        {
            if (exception == null)
                exception = new Exception("empty");

            Console.WriteLine($"--ERROR-- {tag} {exception}"); //!!!!

            var tags = new Dictionary<string, string>
            {
                {"Message", tag},
            };

            //#if !DEBUG

            //            YandexMetrica.Implementation.ReportEvent($"ERROR", tags);

            //            YandexMetrica.Implementation.ReportError(tag, exception);

            //#endif

        }

    }
}
