﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using AppoMobi.Tenant;
using AppoMobi.Xam;
using FFImageLoading.Maui;
using FFImageLoading.Transformations;



namespace AppoMobi.UI
{
    

    public class IconsRow : AppoMobi.Touch.LegacyGesturesContentView
    {
        Microsoft.Maui.Controls.Grid MyGrid = new Microsoft.Maui.Controls.Grid();
        private int SelectedRow = -1;
        private AppoMobi.Touch.LegacyGesturesBoxView SelectionBox = null;
        private Color OriginalBackColor = Colors.Transparent;
        private object SelectedIcon = null;
        private double VertMargin = 16;

        public bool IsPressed { get; set; } = false;
        public bool IsPanned { get; set; } = false;


        private float alpha = 0.15f;
       
        
        public IconsRow()
        
        {


            //MyGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Auto) });
            //MyGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            MyGrid.RowSpacing = 0;

            MyGrid.ColumnSpacing = 16.0; //(double)Application.Current.Resources["StandardSpacing"];


            this.Down += (s, e) => { Debug.WriteLine("*** GRID DOWN"); };
            this.Up += (s, e) => { Debug.WriteLine("*** GRID UP"); };
            // this.Panning += (s, e) => { Debug.WriteLine("*** GRID PANNING"); };


            /*
            var th = (Microsoft.Maui.Controls.OnPlatform<Microsoft.Maui.Controls.Thickness>)Application.Current.Resources["ContactPadding"];

            switch (Device.OS)
            {
                case TargetPlatform.Android:
                    MyGrid.Padding = th.Android;
                    break;
                case TargetPlatform.iOS:
                    MyGrid.Padding = th.iOS;
                    break;
                default:
                    MyGrid.Padding = th.WinPhone;
                    break;
            }
            */

            VertMargin = 14.0;

            //var _vm = (Microsoft.Maui.Controls.OnPlatform<double>)Application.Current.Resources["NiftyListVerticalPadding"];
            //switch (Device.OS)
            //{
            //    case TargetPlatform.Android:
            //        VertMargin = _vm.Android;
            //        break;
            //    case TargetPlatform.iOS:
            //        VertMargin = _vm.iOS;
            //        break;
            //    case TargetPlatform.WinPhone:
            //        VertMargin = _vm.WinPhone;
            //        break;
            //}




            RedrawList();

        }


        

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameList:
                    RedrawList();//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;
     
                //                case nameLabelIcon:
                    //                    ControlLabelIcon.Source = LabelIcon;
                    //                    break;
            }

        }


        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(IconsRow), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }

        
        // SelectedParams
        
        private const string nameSelectedParams = "SelectedParams";
        public static readonly BindableProperty SelectedParamsProperty = BindableProperty.Create(nameSelectedParams, typeof(string), typeof(IconsRow), string.Empty);
        public string SelectedParams
        {
            get { return (string)GetValue(SelectedParamsProperty); }
            set { SetValue(SelectedParamsProperty, value); }
        }


        
        // List
        
        private const string nameList = "List";
        public static readonly BindableProperty ListProperty = BindableProperty.Create(nameList, typeof(IEnumerable), typeof(IconsRow), null);
        public IEnumerable List
        {
            get { return (IEnumerable)GetValue(ListProperty); }
            set { SetValue(ListProperty, value); }
        }


        
        // Tapped
        
        public new event EventHandler LongPressed = null;


        
        // Tapped
        
        public new event EventHandler Tapped = null;
        //private async void OnTapped(object sender, EventArgs e)
        //{
          //  Tapped?.Invoke(this, EventArgs.Empty);
        //}


        //---------------------------------------------------------
        public class MySel : AppoMobi.Touch.LegacyGesturesBoxView
        //---------------------------------------------------------
        {
            public  string Tag { get; set; }
            public int Position { get; set; }
            public string Params { get; set; }

            //public IconsRow Daddy { get; set; } = null;

            
        

            public AppoMobi.Touch.LegacyGesturesBoxView SelectionBox { get; set; }
            public CachedImage SelectedIcon { get; set; }

            public MySel()
            {
                Tag = "";

                Position = -1;
                SelectionBox=null;
            }

        }
        //This is for accessing the passed listview item object        
        public class MySelEventArgs : EventArgs
        {
            public string Tag { get; set; }
            public string Params { get; set; }
        }

        

        //---------------------------------------------------------
        private void RedrawList()
        //---------------------------------------------------------
        {
            if (List == null) return;
            //cleanup
            MyGrid.ColumnSpacing = 0;
            MyGrid.Children.Clear();

            CachedImage img;
            //Microsoft.Maui.Controls.Label lab;

            FontIconLabel fontIcon;
            MySel sel;

            //setup new template table
            //   var MyTable = new Microsoft.Maui.Controls.Grid();



            var SelMargin = new Thickness(8, 0, 0, 0);
            

            List<CMyListItem> items = (List<CMyListItem>)List;

            int pos = -1;
            for (int a = 0; a < items.Count; a++)
            {
                pos++;
  
                fontIcon = new FontIconLabel();
                sel = new MySel();
                img = new CachedImage();

                sel.Tag = items[a].Tag;
                sel.Params = items[a].Parameters;
                sel.HorizontalOptions = LayoutOptions.Fill;
                sel.VerticalOptions = LayoutOptions.Fill;
                sel.Margin = SelMargin;
                sel.Color = OriginalBackColor;
                //OldColor = sel.Color;
                sel.Position = pos;
                sel.SelectionBox = sel;
                sel.SelectedIcon = img;

                MyGrid.Add(sel, pos, 0);

                //Font Icon
                if (items[a].FontIcon != null)
                {
                    var subGrid = new Microsoft.Maui.Controls.Grid();
                    subGrid.Margin = new Thickness(4, VertMargin, 4, VertMargin);
                    subGrid.VerticalOptions = LayoutOptions.FillAndExpand;
                 //   subGrid.BackgroundColor = AppColors.Site_Panel;
                    subGrid.HeightRequest = img.WidthRequest = 24;
                    subGrid.WidthRequest = 24 ;
                    subGrid.InputTransparent = true;
                    //subGrid.BackgroundColor = Colors.Red;

                    fontIcon.FontSize = 18;
                    if (items[a].FontOverride != null)
                    {
                        fontIcon.Font = items[a].FontOverride;
                    }

                    if (TenantOptions.DarkSkin)
                    {
                        fontIcon.TextColor = AppColors.ColoredIcons;
                    }
                    else
                    {
                        fontIcon.TextColor = AppColors.PrimaryDarkTransparent;
                    }

                    fontIcon.TextColor = AppColors.ColoredIcons;

                    fontIcon.VerticalOptions = LayoutOptions.Center;
                    fontIcon.HorizontalOptions = LayoutOptions.Center;
                    fontIcon.SetIcon(items[a].FontIcon);
                    subGrid.Add(fontIcon);

                    MyGrid.Add(subGrid, pos, 0);
                }
                else
                {
                    //image
                   // img.BackgroundColor = AppColors.Site_Panel;
                    img.InputTransparent = true;
                    img.Source = items[a].Image;
                    img.HeightRequest = img.WidthRequest = 23;
                    img.DownsampleToViewSize = true;
                    img.VerticalOptions = LayoutOptions.Center;
                    img.Margin = new Thickness(16, VertMargin, 0, VertMargin);
                    MyGrid.Add(img, pos, 0);
                }




               //todo
                //MyGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

                //hello gestures

                


                //---------------------------------------------------------
                sel.Down += (s, e) =>
                //---------------------------------------------------------
                {
                    IsPressed = true;
                    Debug.WriteLine("*** DOWN");

                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    //                    cc = Color.Parse("#c7ac56").MultiplyAlpha(alpha);
                    cc = AppColors.Controls.MultiplyAlpha(alpha);                    //Color.Parse("edf4f4");//(Color)Application.Current.Resources["primary"];//.FromHex("#c7ac56").MultiplyAlpha(alpha);
                    //cc = cc.MultiplyAlpha(0.015);

                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OriginalBackColor;
                        }
                        //OldColor = sel1.Color;
                        SelectionBox = sel1;
                        sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        
                   //     MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        //Grow(ss.SelectedIcon);
                    }

                };

                //---------------------------------------------------------
                sel.Up += (s, e) =>
                //---------------------------------------------------------
                {
                    IsPressed = false;
                    if (SelectionBox != null && DeviceInfo.Platform != DevicePlatform.Android)
                    {
                        SelectionBox.Color = OriginalBackColor;
                    }
                    Debug.WriteLine("*** UP");
                };


                //gesture recognizer
                //var tapGestureRecognizer = new TapGestureRecognizer();
                //---------------------------------------------------------
                //tapGestureRecognizer.Tapped += (s, e) => 
                sel.Tapped += async (s, e) =>
                //---------------------------------------------------------
                {
                    // handle the 
                    IsPressed = false;
                    var ss = (MySel) s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    cc = AppColors.Controls.MultiplyAlpha(alpha);
                    //cc = cc.MultiplyAlpha(0.025);
                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OriginalBackColor;
                        }
                        //OldColor = sel1.Color;
                        SelectionBox = sel1;
                        sel1.Color= OriginalBackColor;
                        sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        SelectedParams = ss.Params;
                        Tag = ss.Tag;
                        //MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        await Task.Delay(300);
                        Tapped?.Invoke(this, EventArgs.Empty);

                    }

                };
                //sel.GestureRecognizers.Add(tapGestureRecognizer);



                if (a == items.Count - 1) //last pos
                {

                }

               
               //todo coldef 
             //   MyGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Absolute) });
            }

            Content = MyGrid;

        }
        
        bool _growbusy=false;
        public async Task Grow(CachedImage myobject)
        
        {
            if (_growbusy) return;

            _growbusy = true;
            try
            {
                while (IsPressed)
                {
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                { HexColor = AppColors.controls_dark, EnableSolidColor = true });
                myobject.ReloadImage();
                await myobject.ScaleTo(1.25, 75);
                await myobject.ScaleTo(1.0, 75);
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                    {
                        HexColor = AppColors.controls_darkest, EnableSolidColor = true
                    });
                myobject.ReloadImage();
                await myobject.ScaleTo(1.20, 75);
                await myobject.ScaleTo(1.0, 75);
                }
            }
            catch
            {
       
            }
            myobject.Transformations.Clear();
            myobject.ReloadImage();
            _growbusy = false;
           
        }

    }
}
