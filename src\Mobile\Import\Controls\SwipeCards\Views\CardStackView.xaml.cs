﻿using System;

using System.Collections;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Collections.Specialized;
using Microsoft.Maui.Controls.Compatibility;
using SwipeCards.Controls.Arguments;
using SwipedEventArgs = SwipeCards.Controls.Arguments.SwipedEventArgs;
using SwipeDirection = SwipeCards.Controls.Arguments.SwipeDirection;

namespace AppoMobi.UI
{
    public partial class CardStackView : ContentView
    {
        #region ItemsSource Property

        public static readonly BindableProperty ItemsSourceProperty =
            BindableProperty.Create(
                nameof(ItemsSource), typeof(IList),
                typeof(CardStackView),
                null,
                BindingMode.TwoWay,
                propertyChanged: OnItemsSourcePropertyChanged);

        private static NotifyCollectionChangedEventHandler CollectionChangedEventHandler;
        private static void OnItemsSourcePropertyChanged(BindableObject bindable, object oldValue, object newValue)
        {
            // (Re-)subscribe to source changes
            if (newValue is INotifyCollectionChanged)
            {
                // If ItemSource is INotifyCollectionChanged, it can notify us about collection changes
                // In this case, we can use this, as a trigger for Setup()

                // Unsubscibe before
                if (CollectionChangedEventHandler != null)
                    ((INotifyCollectionChanged)newValue).CollectionChanged -= CollectionChangedEventHandler;

                // Subscribe event handler
                CollectionChangedEventHandler = (sender, e) => ItemsSource_CollectionChanged(sender, e, (CardStackView)bindable);
                ((INotifyCollectionChanged)newValue).CollectionChanged += CollectionChangedEventHandler;
            }

            // Even if ItemsSource is not INotifyCollectionChanged, we need to 
            // call Setup() whenever the whole collection changes
            ((CardStackView)bindable).Setup();
        }

        static void ItemsSource_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e, CardStackView cardStackView)
        {
            cardStackView.Setup();
        }

        public IList ItemsSource
        {
            get { return (IList)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }

        #endregion

        #region ItemTemplate Property

        public static readonly BindableProperty ItemTemplateProperty =
            BindableProperty.Create(
                nameof(ItemTemplate),
                typeof(DataTemplate),
                typeof(CardStackView),
                new DataTemplate(() =>
                {
                    var label = new Label { VerticalOptions = LayoutOptions.Center, HorizontalOptions = LayoutOptions.Center };
                    label.SetBinding(Label.TextProperty, "Binding");
                    return new ViewCell { View = label };
                }),
                propertyChanged: OnItemTemplatePropertyChanged);

        private static void OnItemTemplatePropertyChanged(BindableObject bindable, object oldValue, object newValue)
        {
            ((CardStackView)bindable).Setup();
        }

        public DataTemplate ItemTemplate
        {
            get { return (DataTemplate)GetValue(ItemTemplateProperty); }
            set { SetValue(ItemTemplateProperty, value); }
        }

        #endregion

        #region Misc Properties

        public static readonly BindableProperty CardMoveDistanceProperty = BindableProperty.Create(nameof(CardMoveDistance), typeof(int), typeof(CardStackView), -1);

        /// <summary>
        /// Distance, that a card has to be dragged into one direction to trigger the flip
        /// </summary>
        /// <value>The card move distance.</value>
        public int CardMoveDistance
        {
            get { return (int)GetValue(CardMoveDistanceProperty); }
            set { SetValue(CardMoveDistanceProperty, value); }
        }

        public static BindableProperty SwipedRightCommandProperty = BindableProperty.Create(nameof(SwipedRightCommand), typeof(ICommand), typeof(CardStackView), null);
        public ICommand SwipedRightCommand
        {
            get { return (ICommand)GetValue(SwipedRightCommandProperty); }
            set { SetValue(SwipedRightCommandProperty, value); }
        }

        public static BindableProperty SwipedLeftCommandProperty = BindableProperty.Create(nameof(SwipedLeftCommand), typeof(ICommand), typeof(CardStackView), null);
        public ICommand SwipedLeftCommand
        {
            get { return (ICommand)GetValue(SwipedLeftCommandProperty); }
            set { SetValue(SwipedLeftCommandProperty, value); }
        }

        //public static readonly BindableProperty HasShadowProperty = BindableProperty.Create(nameof(HasShadow), typeof(bool), typeof(CardStackView), false);
        //public bool HasShadow
        //{
        //    get { return (bool)GetValue(HasShadowProperty); }
        //    set { SetValue(HasShadowProperty, value); }
        //}

        #endregion

        public event EventHandler<SwipedEventArgs> Swiped;
        public event EventHandler<DraggingEventArgs> StartedDragging;
        public event EventHandler<DraggingEventArgs> FinishedDragging;

        public event EventHandler<EventArgs> Tapped;

        private const int numberOfCards = 3;
        private const int defaultAnimationLength = 250;
        private float defaultSubcardScale = 0.8f;
        private float cardDistance = 0;

        public int ItemIndex  {get; private set;}

        
        public CardStackView()
        
        {
            InitializeComponent();

            // Register pan gesture
            var panGesture = new PanGestureRecognizer();
            panGesture.PanUpdated += OnPanUpdated;
            TouchObserber.GestureRecognizers.Add(panGesture);

            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += OnTapped;
            TouchObserber.GestureRecognizers.Add(tapGesture);


            Setup();
        }

        
        private void OnTapped(object sender, EventArgs e)
        
        {
            try
            {
                Tapped?.Invoke(sender, e);
            }
            catch (Exception exception)
            {
            }
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameStartupIndex:
                    Setup();
                    break;


            }

        }

        
        public void SetIndex(int index)
        
        {
            try
            {
                StartupIndex = index;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

        }

        
        private void Setup()
        
        {
            // TODO: Reduce Setup() calls
            // When starting the app, Setup() gets called multiple times (OnItemsSourcePropertyChanged, OnItemTemplatePropertyChanged, ...). Try to reduce that to 1

            // Reset CardStack first
            CardStack.Children.Clear();

            // Reset item index
            ItemIndex = StartupIndex;

            once = true;

            // Add two cards (one for the front, one for the background) to the stack
            // Use inverse direction to ensure that first card is on top
            for (var i = numberOfCards - 1; i >= 0; i--)
            {
                // Create CardView
                var cardView = new CardView(ItemTemplate)
                {
                    IsVisible = false,
                    Scale = (i == 0) ? 1.0f : defaultSubcardScale,
                    IsEnabled = false
                };

                // Add CardView to UI
                CardStack.Children.Add(
                    cardView, 
                    Constraint.Constant(0), // X
                    Constraint.Constant(0), // Y
                    Constraint.RelativeToParent((parent) => { return parent.Width; }), // Width
                    Constraint.RelativeToParent((parent) => { return parent.Height; }) // Height
                );
            }

            // Start displaying card content
            ShowNextCard();
        }

        
        protected override void OnSizeAllocated(double width, double height)
        
        {
            base.OnSizeAllocated(width, height);

            // Recalculate move distance
            // When not set differently, this distance is 1/3 of the control's width
            if (CardMoveDistance == -1 && !width.Equals(-1))
                CardMoveDistance = (int)(width / 3);
        }

        #region Handle Touch Swiping 

        
        async void OnPanUpdated(object sender, PanUpdatedEventArgs e)
        
        {
            switch (e.StatusType)
            {
                case GestureStatus.Started:
                    HandleTouchStart();
                    break;
                case GestureStatus.Running:
                    HandleTouchRunning((float)e.TotalX);
                    break;
                case GestureStatus.Completed:
                    await HandleTouchCompleted();
                    break;
                case GestureStatus.Canceled:
                    break;
            }
        }
        
        void HandleTouchStart()
        
        {
            if (ItemIndex < ItemsSource.Count)
                StartedDragging?.Invoke(this, new DraggingEventArgs(ItemsSource[ItemIndex]));
        }
        
        void HandleTouchRunning(float xDiff)
        
        {
            if (ItemIndex >= ItemsSource.Count)
                return;

            var topCard = CardStack.Children[numberOfCards - 1];
            var backCard = CardStack.Children[numberOfCards - 2];

            // Move the top card
            if (topCard.IsVisible)
            {
                // Move the card
                topCard.TranslationX = (xDiff);
                var mycheck = topCard.HorizontalOptions;
                if (topCard.BackgroundColor != null && topCard.BackgroundColor !=Colors.Transparent)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        // Update the UI
                        topCard.BackgroundColor = Colors.Transparent;
                    });
                }

                // Calculate a angle for the card
                float rotationAngel = (float)(0.3f * Math.Min(xDiff / this.Width, 1.0f));
                topCard.Rotation = rotationAngel * 57.2957795f;

                // Keep a record of how far it is moved
                cardDistance = xDiff;
            }

            // Scale the backcard
            ScaleBackCard(backCard);
        }

        
        private void ScaleBackCard(View card)
        
        {
            card.Scale = Math.Min(defaultSubcardScale + Math.Abs((cardDistance / CardMoveDistance) * (1.0f - defaultSubcardScale)), 1.0f);
        }

        
        async Task HandleTouchCompleted()
        
        {
            if (ItemIndex >= ItemsSource.Count)
                return;

            var topCard = CardStack.Children[numberOfCards - 1];
            var backCard = CardStack.Children[numberOfCards - 2];

            // Check if card has been dragged far enough to trigger action
            if (Math.Abs(cardDistance) >= CardMoveDistance)
            {
                // Move card off the screen

        
                // Update the UI
                try
                {
                    await topCard.TranslateTo(cardDistance > 0 ? this.Width * 2 : -this.Width * 2, 0, defaultAnimationLength, Easing.SinIn);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
         

                topCard.IsVisible = false;

                // Fire events
                if (cardDistance > 0)
                {
                    Swiped?.Invoke(this, new SwipedEventArgs(ItemsSource[ItemIndex], SwipeDirection.Right));
                    if (SwipedRightCommand != null && SwipedRightCommand.CanExecute(ItemsSource[ItemIndex]))
                        SwipedRightCommand.Execute(ItemsSource[ItemIndex]);
                }
                else
                {
                    Swiped?.Invoke(this, new SwipedEventArgs(ItemsSource[ItemIndex], SwipeDirection.Left));
                    if (SwipedLeftCommand != null && SwipedLeftCommand.CanExecute(ItemsSource[ItemIndex]))
                        SwipedLeftCommand.Execute(ItemsSource[ItemIndex]);
                }

                // Next card
                SetNextIndex();
                ShowNextCard();
            }
            else
            {
                // Run animations simultaniously
                try
                {
                    await Task.WhenAll(
                        // Move card back to the center
                        topCard.TranslateTo((-topCard.X), -topCard.Y, defaultAnimationLength, Easing.SpringOut),
                        topCard.RotateTo(0, defaultAnimationLength, Easing.SpringOut),
                        // Scale the back card down
                        backCard.ScaleTo(defaultSubcardScale, defaultAnimationLength, Easing.SpringOut)
                    );
                }
                catch (Exception e)
                {
                }
            }

            if (ItemIndex < ItemsSource.Count)
                FinishedDragging?.Invoke(this, new DraggingEventArgs(ItemsSource[ItemIndex]));
        }

        #endregion


        //-------------------------------------------------------------
        // InfiniteLoop
        //-------------------------------------------------------------
        private const string nameInfiniteLoop = "InfiniteLoop";
        public static readonly BindableProperty InfiniteLoopProperty = BindableProperty.Create(nameInfiniteLoop, typeof(bool), typeof(CardStackView), true); //, BindingMode.TwoWay
        public bool InfiniteLoop
        {
            get { return (bool)GetValue(InfiniteLoopProperty); }
            set { SetValue(InfiniteLoopProperty, value); }
        }


        //-------------------------------------------------------------
        // StartupIndex
        //-------------------------------------------------------------
        private const string nameStartupIndex = "StartupIndex";
        public static readonly BindableProperty StartupIndexProperty = BindableProperty.Create(nameStartupIndex, typeof(int), typeof(CardStackView), 0); //, BindingMode.TwoWay
        public int StartupIndex
        {
            get { return (int)GetValue(StartupIndexProperty); }
            set { SetValue(StartupIndexProperty, value); }
        }	

        private bool once { get; set; } = true; //todo set on reset

        
        void ShowNextCard()
        
        {
            if (ItemsSource == null || ItemsSource?.Count == 0)
                return;

            try
            {
                var topCard = CardStack.Children[numberOfCards - 1];
                var backCard = CardStack.Children[numberOfCards - 2];

                // Switch cards if this method has been called after a swipe and not at init
                if (!once) //(ItemIndex != 0)
                {
                    // Remove swiped-away card (topcard) from stack
                    CardStack.Children.Remove(topCard);

                    // Scale swiped-away card (topcard) down and add it at the bottom of the stack
                    topCard.Scale = defaultSubcardScale;
                    topCard.BackgroundColor = Colors.Black;
                    CardStack.Children.Insert(0, topCard);
                }
                else
                    once = false;

                // Update cards from top to back
                // Start with the first card on top which is the last one on the CardStack
                for (var i = numberOfCards - 1; i >= 0; i--)
                {
                    var cardView = (CardView)CardStack.Children[i];
                    cardView.Rotation = 0;
                    cardView.TranslationX = 0;

                    // Check if an item for the card is available
                    var index = Math.Min((numberOfCards - 1), ItemsSource.Count) - i + ItemIndex;
                    if (ItemsSource.Count > index || InfiniteLoop)
                    {
                        var ii = index;
                        if (index > ItemsSource.Count - 1)
                            ii = 0;

                        //   if (ItemIndex != ii)
                        //        ScaleBackCard(cardView);

                        var item = ItemsSource[ii];

                        cardView.Update(item);
                        cardView.IsVisible = true;
                    }


                }
            }
            catch (Exception e)
            {

            }


        }

        
        private void SetNextIndex()
        
        {
            if (InfiniteLoop)
            {
                if (ItemsSource.Count-1 == ItemIndex)
                {
                    ItemIndex = 0;
                    return;
                }
            }
            ItemIndex++;
        }

        
        public async void Swipe(SwipeDirection direction, uint animationLength = defaultAnimationLength)
        
        {
            // Check if there is something to swipe
            if (ItemIndex >= ItemsSource?.Count)
                return;

            var topCard = CardStack.Children[numberOfCards - 1];
            var backCard = CardStack.Children[numberOfCards - 2];

            // Fire events
            Swiped?.Invoke(this, new SwipedEventArgs(ItemsSource[ItemIndex], direction));
            if (direction == SwipeDirection.Left)
            {
                if (SwipedLeftCommand != null && SwipedLeftCommand.CanExecute(ItemsSource[ItemIndex]))
                    SwipedLeftCommand.Execute(ItemsSource[ItemIndex]);
            }
            else if (direction == SwipeDirection.Right)
            {
                if (SwipedRightCommand != null && SwipedRightCommand.CanExecute(ItemsSource[ItemIndex]))
                    SwipedRightCommand.Execute(ItemsSource[ItemIndex]);
            }

            // Increase item index
            // Do that before the animation runs
            SetNextIndex();

            // Animate card
            try
            {
                await Task.WhenAll(

                    // Move card left or right            
                    topCard.TranslateTo(direction == SwipeDirection.Right ? this.Width * 2 : -this.Width * 2, 0, animationLength, Easing.SinIn),

                    // Rotate card (57.2957795f/3=17.18873385f)
                    topCard.RotateTo(direction == SwipeDirection.Right ? 17.18873385f : -17.18873385f, animationLength, Easing.SinIn),

                    // Scale back card up
                    backCard.ScaleTo(1.0f, animationLength)
                );
                topCard.IsVisible = false;

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            // Next card
            ShowNextCard();
        }
    }
}
