﻿


namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class InfoCard
    {
        public InfoCard()
        {
            InitializeComponent();
            Initialize();
        }

        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                
            }
            else
            {
               // txtLabel.FontAttributes = FontAttributes.None;

            }

        }

        
        // Text
        
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(InfoCard), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }

        
        // TextColor
        
        private const string nameTextColor = "TextColor";
        public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameTextColor, typeof(Color), typeof(InfoCard), Colors.Black); //, BindingMode.TwoWay
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }


        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {

                //property changed

                case nameText:
                    txtLabel.Text = Text;
                    break;
                //property changed
                case nameTextColor:
                    txtLabel.TextColor = TextColor;
                    break;

            }

        }

    }
}