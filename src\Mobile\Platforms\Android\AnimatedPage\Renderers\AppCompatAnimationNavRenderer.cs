﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Content.Res;
using Android.Views;
using AnimatedNavi.Forms;
using AnimatedNavi.Droid;

using Fragment = AndroidX.Fragment.App.Fragment;
using FragmentManager = AndroidX.Fragment.App.FragmentManager;
using FragmentTransaction = AndroidX.Fragment.App.FragmentTransaction;
using View = Android.Views.View;
using AppoMobi.Plugin.Navigation;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Controls.Compatibility.Platform.Android.AppCompat;

using Microsoft.Maui.Controls.Internals;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
using Context = Android.Content.Context;
using Platform = Microsoft.Maui.ApplicationModel.Platform;

//[assembly: ExportRenderer(typeof(AnimationNavigationPage), typeof(AppCompatAnimationNavRenderer))]
namespace AnimatedNavi.Droid
{
    public class AppCompatAnimationNavRenderer : NavigationPageRenderer
    {
        #region KEYBOARD

        private bool _lieAboutCurrentFocus;
        private bool _listener;
        public override bool DispatchTouchEvent(MotionEvent e)
        {
            var activity = Platform.CurrentActivity;

            var focused = activity.CurrentFocus;
            bool locked = false;
            if (focused != null)
            {
                //Case of leaving current focus
                var renderer = focused.Parent as IVisualElementRenderer;
                if (this.Element != null)
                {
                    if (renderer != null)
                    {
                        VisualElement element = renderer.Element;
                        if (!((AnimationNavigationPage)Element).CanChangeFocus(element))
                        {
                            locked = true;
                        }
                        else
                            ((AnimationNavigationPage)Element).OnFocusedElementChanged(element);
                    }
                    else
                    {
                        ((AnimationNavigationPage)Element).OnFocusedElementChanged(null);
                    }
                }
            }

            _lieAboutCurrentFocus = locked;
            var result = base.DispatchTouchEvent(e);
            _lieAboutCurrentFocus = false;

            //now setup listener to see what will be the focus after this event
            if (!_listener)
            {
                _listener = true;
                Device.StartTimer(TimeSpan.FromMilliseconds(10), () =>
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        var focused = activity.CurrentFocus;
                        if (focused != null)
                        {
                            var renderer = activity.CurrentFocus.Parent as IVisualElementRenderer;
                            if (this.Element != null)
                            {
                                if (renderer != null)
                                {
                                    VisualElement element = renderer.Element;
                                    if (!((AnimationNavigationPage)Element).CanChangeFocus(element))
                                    {
                                        locked = true;
                                    }
                                    else
                                        ((AnimationNavigationPage)Element).OnFocusedElementChanged(element);
                                }
                                else
                                {
                                    ((AnimationNavigationPage)Element).OnFocusedElementChanged(null);
                                }
                            }
                        else
                        {
                            ((AnimationNavigationPage)Element).OnFocusedElementChanged(null);
                        }
                       
                        }
                        else
                        {
                            ((AnimationNavigationPage)Element).OnFocusedElementChanged(null);
                        }
                    });
                    _listener = false;
                    return false;
                });
            }
          

            return result;
        }


        #endregion

        private const int TransitionDuration = 700;

        private readonly AppCompatNavRendererHelper _helper;

        public AppCompatAnimationNavRenderer(Context context)
        : base(context)
        {
            _helper = new AppCompatNavRendererHelper(this, context);
        }




        protected override void OnElementChanged(ElementChangedEventArgs<NavigationPage> e)
        {
            base.OnElementChanged(e);

            if (e.OldElement != null)
            {
                this.FocusChange -= OnFocusChange;

                UnsubscribeFromNavigationEvents(e.OldElement);
            }
            if (e.NewElement != null)
            {
                this.FocusChange += OnFocusChange;

                try
                {
                    int[][] states = new int[][]
                    {
                        new int[] { 0 }, // enabled
                    };


                    //set page if any to the view, we do not have to wait before pushrequested this created blink of our backgroundcolor
                    var pageInside = Element.Pages.FirstOrDefault();
                    if (pageInside != null)
                    {
                        int[] colors = new int[]
                        {
                            //Android.Graphics.Colors.Black
                            pageInside.BackgroundColor.ToAndroid(),
                        };
                        BackgroundTintList = new ColorStateList(states, colors);

                        SetContent(pageInside);
                    }

                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine(@"            ERROR: ", ex.Message);
                }

                _helper.UnsubscribeFromStandardNavigationEvents(e.NewElement);
                SubscribeToNavigationEvents(e.NewElement);
            }
        }

        private void OnFocusChange(object sender, FocusChangeEventArgs e)
        {
            var stop = true;
        }


        protected override void Dispose(bool disposing)
        {
            if (Element != null)
            {
                UnsubscribeFromNavigationEvents(Element);
            }
            base.Dispose(disposing);
        }

        private void SubscribeToNavigationEvents(INavigationPageController page)
        {
            page.PushRequested += OnPushedWithAnimation;
            page.PopRequested += OnPoppedWithAnimation;
            page.PopToRootRequested += OnPoppedToRootWithAnimation;
        }

        private void UnsubscribeFromNavigationEvents(INavigationPageController page)
        {
            page.PushRequested -= OnPushedWithAnimation;
            page.PopRequested -= OnPoppedWithAnimation;
            page.PopToRootRequested -= OnPoppedToRootWithAnimation;
        }

        private void OnPushedWithAnimation(object sender, NavigationRequestedEventArgs e)
        {
            e.Task = SwitchContentAsync(e.Page, e.Animated);
        }

        private void OnPoppedWithAnimation(object sender, NavigationRequestedEventArgs e)
        {
            var pageToShow = ((INavigationPageController)Element).Pages.Skip(1).FirstOrDefault();
            e.Task = pageToShow == null ? Task.FromResult(false) : SwitchContentAsync(pageToShow, e.Animated, true);
        }

        private void OnPoppedToRootWithAnimation(object sender, NavigationRequestedEventArgs e)
        {
            e.Task = SwitchContentAsync(e.Page, e.Animated, true, true);
        }

        private double GetAnimationDuration(IPageAnimation animation)
        {
            switch (animation.Duration)
            {
                case AnimationDuration.Short:
                    return Durations.ShortDuration;
                case AnimationDuration.Medium:
                    return Durations.MediumDuration;
                case AnimationDuration.Long:
                    return Durations.LongDuration;
                default:
                    return Durations.ZeroDuration;
            }
        }


        public bool SetContent(Page page)
        {
            _helper.CurrentPage?.SendDisappearing();

            AndroidX.Fragment.App.Fragment fragment = _helper.GetFragment(page, false, false);
            List<Fragment> fragments = _helper.FragmentStack;
            FragmentManager fm = _helper.FragmentManager;
            var currentPage = page;
            var currentAnimPage = currentPage as IAnimationPage;
            var animation = AnimationNavigationPage.GetAnimation(currentPage, false);
            
            page?.SendAppearing();

            _helper.CurrentPage = page;
            _helper.SetNavAnimationInProgress(Element, true);
            FragmentTransaction transaction = fm.BeginTransaction();
            
            transaction.DisallowAddToBackStack();
            if (fragments.Count == 0)
            {
                transaction.Add(Id, fragment);
                fragments.Add(fragment);
            }
            else
            {
  
                    // push
                    Fragment currentToHide = fragments.Last();
                    transaction.Hide(currentToHide);
                    transaction.Add(Id, fragment);
                    fragments.Add(fragment);
    
            }
            // We don't currently support fragment restoration, so we don't need to worry about
            // whether the commit loses state
            transaction.CommitAllowingStateLoss();

            fragment.UserVisibleHint = true;
            _helper.UpdateToolbar();
                    
            Context.HideKeyboard(this);

            currentAnimPage?.OnAnimationFinished(false);

            _helper.SetNavAnimationInProgress(Element, false);

            return true;
        }

        private Task<bool> SwitchContentAsync(Page page, bool animated, bool removed = false, bool popToRoot = false)
        {


            var tcs = new TaskCompletionSource<bool>();
            Fragment fragment = _helper.GetFragment(page, removed, popToRoot);



            if (removed)
                page?.SendDisappearing();
            else
                page?.SendAppearing();

            List<Fragment> fragments = _helper.FragmentStack;
            FragmentManager fm = _helper.FragmentManager;
            var currentPade = (removed ? _helper.CurrentPage : page);

            var currentAnimPade = currentPade as IAnimationPage;
            var animation = AnimationNavigationPage.GetAnimation(currentPade, animated);
            _helper.CurrentPage = page;

            _helper.SetNavAnimationInProgress(Element, true);


            FragmentTransaction transaction = fm.BeginTransaction();
        AnimationHelper.SetupTransition(transaction, animation, !removed, animated);



        if (animation.Type != AnimationType.Empty && animation.Duration != AnimationDuration.Zero)
        {
            currentAnimPade?.OnAnimationStarted(removed);
        }
        transaction.DisallowAddToBackStack();
        if (fragments.Count == 0)
        {
            transaction.Add(Id, fragment);
            fragments.Add(fragment);
        }
        else
        {
            if (removed)
            {
                // pop only one page, or pop everything to the root
                var popPage = true;
                var fragmentsToRemove = new List<Fragment>();
                while (fragments.Count > 1 && popPage)
                {
                    var currentToRemove = fragments.Last();
                    fragments.RemoveAt(fragments.Count - 1);
                    fragmentsToRemove.Add(currentToRemove);
                    transaction.Hide(currentToRemove);
                    popPage = popToRoot;
                }
                // we need it for poping pages with animation
                RemoveFragments(fragmentsToRemove);
                Fragment toShow = fragments.Last();
                // Execute pending transactions so that we can be sure the fragment list is accurate.
                fm.ExecutePendingTransactions();
                if (fm.Fragments.Contains(toShow))
                    transaction.Show(toShow);
                else
                    transaction.Add(Id, toShow);
            }
            else
            {
                // push
                Fragment currentToHide = fragments.Last();
                transaction.Hide(currentToHide);
                transaction.Add(Id, fragment);
                fragments.Add(fragment);
            }
        }
        // We don't currently support fragment restoration, so we don't need to worry about
        // whether the commit loses state
        transaction.CommitAllowingStateLoss();

        //if (!removed && !popToRoot) //push
        //{
        //    Task.Delay(new TimeSpan(2000)).Wait();
        //}

        // The fragment transitions don't really SUPPORT telling you when they end
        // There are some hacks you can do, but they actually are worse than just doing this:
        if (animated)
        {
            if (!removed)
            {
                _helper.UpdateToolbar();
                if (_helper.DrawerToggle != null && ((INavigationPageController)Element).StackDepth == 2)
                    _helper.AnimateArrowIn();
            }
            else if (_helper.DrawerToggle != null && ((INavigationPageController)Element).StackDepth == 2)
                _helper.AnimateArrowOut();

            //todo replace this with animation ended override!
            Device.StartTimer(TimeSpan.FromMilliseconds(TransitionDuration), () =>
            {
                tcs.TrySetResult(true);
                fragment.UserVisibleHint = true; 
                if (removed)
                {
                    _helper.UpdateToolbar();
                }
                return false;
            });
        }
        else
        {
            Device.StartTimer(TimeSpan.FromMilliseconds(1), () =>
            {
                tcs.TrySetResult(true);
                fragment.UserVisibleHint = true;
                _helper.UpdateToolbar();
                return false;
            });
        }
        Context.HideKeyboard(this);

        if (animation.Type != AnimationType.Empty && animation.Duration != AnimationDuration.Zero && currentAnimPade != null)
        {
            Device.StartTimer(TimeSpan.FromMilliseconds(GetAnimationDuration(currentAnimPade.PageAnimation)), delegate
            {
                currentAnimPade.OnAnimationFinished(removed);
                return false;
            });
        }
        _helper.SetNavAnimationInProgress(Element, false);


            return tcs.Task;
        }

        private async void RemoveFragments(List<Fragment> fragmentsToRemove)
        {
            await Task.Delay(Context.Resources.GetInteger(AppoMobi.Plugin.Navigation.Resource.Integer.animation_duration)).ConfigureAwait(true);
            FragmentManager fm = _helper.FragmentManager;
            FragmentTransaction transaction = fm.BeginTransaction();
            transaction.DisallowAddToBackStack();
            foreach (var fragment in fragmentsToRemove)
            {
                transaction.Remove(fragment);
            }
            transaction.Commit();
        }


    }
}