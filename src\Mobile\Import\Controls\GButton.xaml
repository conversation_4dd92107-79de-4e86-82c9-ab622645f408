﻿<?xml version="1.0" encoding="UTF-8" ?>
<controls:TouchFrame
    x:Class="AppoMobi.UI.FrameButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam1="clr-namespace:AppoMobi.Xam"
    x:Name="ThisButton"
    Padding="0"
    BackgroundColor="{x:Static xam1:BackColors.Page}"
    Down="CGrid_OnDown"
    DownOpacity="1.0"
    DownScale="0.95"
    HorizontalOptions="Fill"
    StrokeShape="RoundRectangle 16,16,16,16"
    StrokeThickness="0"
    TimeLockDownMs="1000"
    Up="CGrid_OnUp"
    VerticalOptions="Start">

    <Grid
        x:Name="cGrid"
        ColumnSpacing="0"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="colCaption" Width="*" />
        </Grid.ColumnDefinitions>

        <svg:GradientBox
            EndColor="{Binding Source={x:Reference ThisButton}, Path=EndColor}"
            GradientOrientation="Vertical"
            HorizontalOptions="Fill"
            Light="{Binding Source={x:Reference ThisButton}, Path=Light}"
            StartColor="{Binding Source={x:Reference ThisButton}, Path=StartColor}"
            VerticalOptions="Fill" />

        <!--  caption  -->
        <Label
            x:Name="cLabel"
            Margin="0"
            Padding="8,0,8,0"
            FontSize="28"
            HorizontalOptions="Fill"
            HorizontalTextAlignment="Center"
            Text="{Binding Source={x:Reference ThisButton}, Path=Text}"
            TextColor="{x:Static xam1:TextColors.StandartOnDark}"
            VerticalOptions="FillAndExpand"
            VerticalTextAlignment="Center" />


    </Grid>


</controls:TouchFrame>