﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class StringEmptyConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                if (string.IsNullOrEmpty((string)value)) return true;
            }
            else
            {
                return true;
            }
            return false;
        }
    }
}