﻿using AppoMobi.Common.Dto;
using AppoMobi.Tenant;


namespace AppoMobi.Models
{
    //-------------------------------------------------------------------
    public class ImageInfo
    //-------------------------------------------------------------------
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Desc { get; set; }
        public string ThumbnailMicro { get; set; }
        public string ThumbnailMini { get; set; }
        public string ThumbnailMedium { get; set; }
        public string ThumbnailNormal { get; set; }
        public string ThumbnailLarge { get; set; }
        public Color Color { get; set; }

        //-------------------------------------------------------------------
        public ImageInfo()
        //-------------------------------------------------------------------
        {

        }

        //-------------------------------------------------------------------
        public ImageInfo(string imageId, Color color, string title = null, string desc = null)
        //-------------------------------------------------------------------
        {
            ThumbnailMicro = BaseMobileDto.GetThumbnailUrl(imageId, "micro", TenantOptions.TenantKey);
            ThumbnailMini = BaseMobileDto.GetThumbnailUrl(imageId, "mini", TenantOptions.TenantKey);
            ThumbnailMedium = BaseMobileDto.GetThumbnailUrl(imageId, "medium", TenantOptions.TenantKey);
            ThumbnailNormal = BaseMobileDto.GetThumbnailUrl(imageId, "normal", TenantOptions.TenantKey);
            ThumbnailLarge = BaseMobileDto.GetThumbnailUrl(imageId, "large", TenantOptions.TenantKey);
            Title = title;
            Desc = desc;
            Id = imageId;
            Color = color;
        }

        //-------------------------------------------------------------------
        public ImageInfo(string imageId, string title = null, string desc = null)
        //-------------------------------------------------------------------
        {
            ThumbnailMicro = BaseMobileDto.GetThumbnailUrl(imageId, "micro", TenantOptions.TenantKey);
            ThumbnailMini = BaseMobileDto.GetThumbnailUrl(imageId, "mini", TenantOptions.TenantKey);
            ThumbnailMedium = BaseMobileDto.GetThumbnailUrl(imageId, "medium", TenantOptions.TenantKey);
            ThumbnailNormal = BaseMobileDto.GetThumbnailUrl(imageId, "normal", TenantOptions.TenantKey);
            ThumbnailLarge = BaseMobileDto.GetThumbnailUrl(imageId, "large", TenantOptions.TenantKey);
            Title = title;
            Desc = desc;
            Id = imageId;
        }
    }
}
