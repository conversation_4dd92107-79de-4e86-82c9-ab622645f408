﻿using AppoMobi.Framework.Api;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class ProfileRelatedDto<T>
    {
        /// <summary>
        /// Required
        /// </summary>
        public string ProfileFrom { get; set; }

        /// <summary>
        /// Optional
        /// </summary>
        public string ProfileTo { get; set; }

        public T Options { get; set; }
    }

    public class FinancialRateDto : BaseFrameworkDto
    {
        public FinancialRateDto()
        {

        }

        public string Profile { get; set; }

        public ProUserType Type { get; set; }

        public string Languages { get; set; }

        public decimal MinutePrice { get; set; }

    }
}