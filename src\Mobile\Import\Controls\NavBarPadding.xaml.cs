﻿


namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NavBarPadding
    {
        public NavBarPadding()
        {
            InitializeComponent();
            Initialize();
        }

        
        // NavBarVisible
        
        private const string nameNavBarVisible = "NavBarVisible";
        public static readonly BindableProperty NavBarVisibleProperty = BindableProperty.Create(nameNavBarVisible, typeof(bool), typeof(NavBarPadding), true); //, BindingMode.TwoWay
        public bool NavBarVisible
        {
            get { return (bool)GetValue(NavBarVisibleProperty); }
            set {
                SetValue(NavBarVisibleProperty, value);}
        }



        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            var add = Core.NavBarHeight;
            if (!NavBarVisible) add = 0;

            boxControl.HeightRequest = add + Core.StatusBarHeightRequest;
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {


                //property changed
                case nameNavBarVisible:
                    Initialize();
                    break;

            }

        }

    }

}