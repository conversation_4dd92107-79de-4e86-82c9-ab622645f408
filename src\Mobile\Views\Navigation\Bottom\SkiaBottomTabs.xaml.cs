﻿using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Windows.Input;
using AppoMobi.Maui.Gestures;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using DrawnUi.Views;

namespace AppoMobi.Mobile.Views
{
    [ContentProperty("BottomTabsContent")]
    public partial class SkiaBottomTabs : Canvas, IDisposable //NOT USED YET
    {

        // NOT YET USED !!!

        private void OnTabItemPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            var tabItem = (TabItem)sender;
            if (e.PropertyName == nameof(TabItem.IsVisible))
            {
                UpdateCellsDestinations();
            }
        }




        public static readonly BindableProperty SelectedIndexProperty = BindableProperty.Create(
            nameof(SelectedIndex),
            typeof(int),
            typeof(SkiaBottomTabs),
            defaultValue: -1,
            propertyChanged: SelectedIndexPropertyChanged);
        public int SelectedIndex
        {
            get => (int)GetValue(SelectedIndexProperty);
            set
            {
                SetValue(SelectedIndexProperty, value);

                var selectedIndex = value;
                if (lastIndex != selectedIndex || selectedIndex == -1)
                {
                    lastIndex = selectedIndex;
                    //CommandTabSelected?.Execute(selectedIndex);
                }
                //else
                //{
                //    CommandTabReselected?.Execute(selectedIndex);
                //}
            }
        }
        /*
            //-------------------------------------------------------------
            // StartupIndex
            //-------------------------------------------------------------
            private const string nameStartupIndex = "StartupIndex";
            public static readonly BindableProperty StartupIndexProperty = BindableProperty.Create(nameStartupIndex, typeof(int), typeof(SkiaBottomTabs), 0); //, BindingMode.TwoWay
            public int StartupIndex
            {
                get { return (int)GetValue(StartupIndexProperty); }
                set { SetValue(StartupIndexProperty, value); }
            }
            */

        public static readonly BindableProperty ShadowTypeProperty = BindableProperty.Create(
            nameof(ShadowType),
            typeof(ShadowType),
            typeof(SkiaBottomTabs),
            defaultBindingMode: BindingMode.OneWayToSource);

        private const int ShadowHeight = 6;




        private List<TabItem> _selectableTabs = new();


        //private BoxView _contentBackgroundView;
        //private ShadowBoxView _shadow;

        //private ColumnDefinition _lastFillingColumn;

        int lastTapIndex;

        public ICommand TabItemTappedCommand
        {
            get
            {
                return new Command<object>((object parameter) =>
                {
                    //check for developer
                    if (parameter is not int)
                    {
                        throw new Exception("TouchEffect parameter not integer");
                        return;
                    }

                    SelectedIndex = (int)parameter;
                });
            }
        }

        public SkiaBottomTabs()
        {
            InitializeComponent();

            //TabItemTappedCommand = new TapCommand(OnTabItemTapped);

            //UpdateTabsSource();

            UpdateTabs();

        }



        public event EventHandler<SelectedPositionChangedEventArgs> SelectedTabIndexChanged;

        //-------------------------------------------------------------
        // Tabs
        //-------------------------------------------------------------
        private const string nameTabs = "Tabs";
        public static readonly BindableProperty TabsProperty = BindableProperty.Create(nameTabs,
            typeof(ObservableCollection<View>), typeof(SkiaBottomTabs), null,
            propertyChanging: OnTabsChanging,
            propertyChanged: OnTabsChanged);
        public ObservableCollection<View> Tabs
        {
            get { return (ObservableCollection<View>)GetValue(TabsProperty); }
            set { SetValue(TabsProperty, value); }
        }


        //public static readonly BindableProperty TabsProperty = BindableProperty.Create(
        //    nameof(Tabs),
        //    typeof(ObservableCollection<TabItem>),
        //    typeof(SkiaBottomTabs),
        //    defaultValueCreator: _ => new ObservableCollection<TabItem>());



        public TabType TabType
        {
            get => (TabType)GetValue(TabTypeProperty);
            set => SetValue(TabTypeProperty, value);
        }
        public static readonly BindableProperty TabTypeProperty = BindableProperty.Create(
            nameof(TabType),
            typeof(TabType),
            typeof(SkiaBottomTabs),
            defaultValue: TabType.Fixed,
            propertyChanging: OnTabsChanging,
            propertyChanged: OnTabsChanged,
        defaultBindingMode: BindingMode.OneWayToSource);

        private static void OnTabsChanged(BindableObject bindable, object oldvalue, object newvalue)
        {
            var control = bindable as SkiaBottomTabs;
            control.UpdateTabs();
        }

        private static void OnTabsChanging(BindableObject bindable, object oldvalue, object newvalue)
        {
            var control = bindable as SkiaBottomTabs;
            var old = oldvalue as ObservableCollection<View>;
            if (old != null)
            {
                control.Destroy(old);
            }
        }




        public ShadowType ShadowType
        {
            get => (ShadowType)GetValue(ShadowTypeProperty);
            set => SetValue(ShadowTypeProperty, value);
        }

        public new List<SkiaControl> Views
        {
            get => base.Views;
            set =>
                throw new NotSupportedException(
                    "You can only add TabItem to the SkiaBottomTabs through the Tabs property");
        }



        //public View BottomTabsContent
        //{
        //    set =>
        //        throw new NotSupportedException(
        //            "You can only add TabItem to the SkiaBottomTabs through the Tabs property");
        //}

        public bool ShowScrollbar { get; set; }

        //private ICommand TabItemTappedCommand { get; }




        public void UpdateTabs()
        {
            Destroy(Tabs);

            if (Tabs != null)
            {
                foreach (var tab in Tabs)
                {
                    OnChildAdded((TabItem)tab);
                }
                Tabs.CollectionChanged += OnTabsCollectionChanged;

                SelectedIndex = 0;
            }


        }

        void Destroy(ObservableCollection<View> tabs)
        {
            if (tabs == null)
                return;

            tabs.CollectionChanged -= OnTabsCollectionChanged;
            _selectableTabs.Clear();
            this.Views.Clear();
        }

        public override void OnDisposing()
        {
            base.OnDisposing();

            if (disposed)
                return;
            disposed = true;

            if (Tabs != null)
            {
                Destroy(Tabs);
                Tabs = null;
            }

            _selectableTabs.Clear();

            this.ClearChildren();

        }

        protected bool disposed;



        protected override void OnPropertyChanging(string propertyName = null)
        {
            base.OnPropertyChanging(propertyName);

            //switch (propertyName)
            //{
            //    case nameof(Tabs):
            //        if (Tabs != null)
            //        {
            //            Tabs.CollectionChanged -= OnTabsCollectionChanged;

            //            foreach (var tab in Tabs)
            //            {
            //                if (tab is IDisposable)
            //                    ((IDisposable)tab).Dispose();
            //            }

            //            Tabs.Clear();
            //            _selectableTabs.Clear();
            //            _grid.ColumnDefinitions.Clear();
            //            _grid.Children.Clear();

            //        }
            //        break;
            //}
        }



        private static void SelectedIndexPropertyChanged(BindableObject bindable, object oldvalue, object newvalue)
        {
            var tabHostView = (SkiaBottomTabs)bindable;

            int selectedIndex = (int)newvalue;
            if (selectedIndex < 0)
            {
                return;
            }

            tabHostView.ApplySelectedIndex();
        }

        private void ApplySelectedIndex()
        {
            var selectedIndex = SelectedIndex;

            //clamp
            if (_selectableTabs.Count == 0)
            {
                selectedIndex = 0;
            }
            if (selectedIndex > _selectableTabs.Count)
            {
                selectedIndex = _selectableTabs.Count - 1;
            }

            //set tabs views visual selection state
            for (int index = 0; index < _selectableTabs.Count; index++)
            {
                _selectableTabs[index].IsSelected = selectedIndex == index;
            }

            //selected and reselected events
            if (lastTapIndex != selectedIndex || selectedIndex == -1)
            {
                lastTapIndex = selectedIndex;
                CommandTabSelected?.Execute(selectedIndex);
            }
            else
            {
                CommandTabReselected?.Execute(selectedIndex);
                RaiseSelectedTabIndexChanged(new SelectedPositionChangedEventArgs(selectedIndex));
            }
        }

        protected int lastIndex = -1;


        private void OnTabsCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            var action = NotifyCollectionChangedAction.Reset;
            try
            {
                action = e.Action;
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                return;
            }

            switch (action)
            {
            case NotifyCollectionChangedAction.Add:
            foreach (var tab in e.NewItems)
            {
                OnChildAdded((TabItem)tab);
            }

            break;

            case NotifyCollectionChangedAction.Remove:
            foreach (var tab in e.OldItems)
            {
                OnChildRemoved((TabItem)tab);
            }

            break;

            case NotifyCollectionChangedAction.Move:
            case NotifyCollectionChangedAction.Replace:
            case NotifyCollectionChangedAction.Reset:
            default:
            throw new NotSupportedException();
            }

            //UpdateTabs();
        }

        private void AddTapCommand(SkiaControl tabItem, Color color, int selectedIndex)
        {
            TouchEffect.SetCommandTapped(tabItem, TabItemTappedCommand);
            TouchEffect.SetCommandTappedParameter(tabItem, selectedIndex);
        }

        private void AddTapCommand(TabItem tabItem, int selectedIndex)
        {
            var index = _selectableTabs.IndexOf((TabItem)tabItem);

            var gesture = new TapGestureRecognizer()
            {
                Command = TabItemTappedCommand,
                CommandParameter = selectedIndex
            };

            tabItem.GestureRecognizers.Add(gesture);
        }

        protected int GetTabIndex(View tabItem)
        {
            return Tabs.IndexOf(tabItem);
        }


        protected ColumnDefinitionCollection ColumnDefinitions { get; } = new();

        public void UpdateCellsDestinations()
        {
            int i = 0;
            foreach (var view in Views.ToList())
            {
                var tabItem = this.Tabs[i] as TabItem;
                double width = 0.0;
                if (tabItem.IsVisible)
                {
                    //detect width
                    if (tabItem.TabWidth.IsAbsolute)
                    {
                        width = tabItem.TabWidth.Value;
                    }
                }

                view.WidthRequest = width;
                view.HorizontalOptions = LayoutOptions.Start;
            }

            Update();
        }
        private void OnChildAdded(TabItem tabItem)
        {
            if (tabItem.Content is Canvas skiaLayoutView)
            {
                tabItem.PropertyChanged += OnTabItemPropertyChanged;

                var container = new SkiaLayout() //new SkiaLayout(this)
                {
                    Children = skiaLayoutView.Children.ToList(),
                    Padding = skiaLayoutView.Padding,
                    VerticalOptions = LayoutOptions.Fill,
                    HorizontalOptions = LayoutOptions.Fill
                };
                Children.Add(container);

                if (tabItem.IsSelectable)
                {
                    _selectableTabs.Add(tabItem);
                    var index = _selectableTabs.IndexOf((TabItem)tabItem);

                    //AddTapCommand(container, tabItem.SelectedTabColor, index);
                    AddTapCommand(tabItem, index);
                }

                if (tabItem.TabAlign.Alignment == LayoutAlignment.End && tabItem.TabAlign.Expands)
                {
                    ColumnDefinitions.Add(new ColumnDefinition
                    {
                        Width = GridLength.Star
                    });
                }
                else
                if (tabItem.TabAlign.Alignment == LayoutAlignment.Start || tabItem.TabAlign.Alignment == LayoutAlignment.End)
                {
                    if (tabItem.TabAlign.Expands)
                        ColumnDefinitions.Add(new ColumnDefinition
                        {
                            Width = GridLength.Star
                        });
                    else
                        ColumnDefinitions.Add(new ColumnDefinition
                        {
                            Width = tabItem.TabWidth
                        });

                }
                else
                {
                    if (tabItem.TabWidth.IsAbsolute)
                    {
                        ColumnDefinitions.Add(new ColumnDefinition
                        {
                            Width = tabItem.TabWidth
                        });
                    }
                    else
                    {
                        ColumnDefinitions.Add(new ColumnDefinition
                        {
                            Width = GridLength.Star
                        });
                    }
                }

                UpdateCellsDestinations();

                ApplySelectedIndex();
            }

        }

        private void OnChildRemoved(TabItem tabItem)
        {
            if (Views.Count == 0)
            {
                return;
            }

            //if (TabType == TabType.Scrollable)
            //{
            //    if (Tabs.Count == 0)
            //    {
            //        _grid.ColumnDefinitions.Remove(_lastFillingColumn);
            //    }
            //}

            tabItem.PropertyChanged -= OnTabItemPropertyChanged;
            if (tabItem.IsSelectable)
            {
                _selectableTabs.Remove(tabItem);
            }

            var index = this.Tabs.IndexOf(tabItem);
            Children.RemoveAt(index);

            ApplySelectedIndex();
        }



        private void RaiseSelectedTabIndexChanged(SelectedPositionChangedEventArgs e)
        {

            SelectedTabIndexChanged?.Invoke(this, e);
        }


        //-------------------------------------------------------------
        // CommandTabReselected
        //-------------------------------------------------------------
        private const string nameCommandTabReselected = "CommandTabReselected";
        public static readonly BindableProperty CommandTabReselectedProperty = BindableProperty.Create(nameCommandTabReselected, typeof(ICommand), typeof(SkiaBottomTabs), null); //, BindingMode.TwoWay
        public ICommand CommandTabReselected
        {
            get { return (ICommand)GetValue(CommandTabReselectedProperty); }
            set { SetValue(CommandTabReselectedProperty, value); }
        }

        //-------------------------------------------------------------
        // CommandTabSelected
        //-------------------------------------------------------------
        private const string nameCommandTabSelected = "CommandTabSelected";
        public static readonly BindableProperty CommandTabSelectedProperty = BindableProperty.Create(nameCommandTabSelected, typeof(ICommand), typeof(SkiaBottomTabs), null); //, BindingMode.TwoWay

        //  private ObservableCollection<View> _oldTabs;

        public ICommand CommandTabSelected
        {
            get { return (ICommand)GetValue(CommandTabSelectedProperty); }
            set { SetValue(CommandTabSelectedProperty, value); }
        }

    }

}