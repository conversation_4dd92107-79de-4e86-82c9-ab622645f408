﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using AppoMobi.Xam;


namespace AppoMobi.Client
{
    /// <summary>
    /// <PERSON> is watching for the App health to help avoid bugs, leaks etc
    /// </summary>
    public class Doctor
    {
        private const string MessageMe = "ForTheDoctor";
        private const string SettingsKey = "DoctorId";

        #region CONSTRUCTOR

        protected string Id { get; set; }

        static Doctor _instance;
        public static <PERSON>
        {
            get
            {
                if (_instance == null)
                {
                    string existing = Settings.Current.GetValueOrDefault("DoctorId", "");
                    if (!string.IsNullOrEmpty(existing))
                    {
                        throw new Exception("[DOCTOR] Already existing. Multi-instance conflict or maybe You forgot to call Doctor.Init() at app startup.");
                    }
                    _instance = new Doctor();
                    _instance.Id = Guid.NewGuid().ToString();
                    Settings.Current.AddOrUpdateValue(SettingsKey, _instance.Id);
                    App.Instance.Messager.Subscribe<string>(_instance, MessageMe, async (sender, arg) =>
                    {
                        await _instance.NotifyInstanceCreation(arg);
                    });
                }
                return _instance;
            }
        }

        private void Dispose()
        {
            App.Instance.Messager.Unsubscribe(_instance, MessageMe);
        }

        private string Hello()
        {
            return Guid.NewGuid().ToString();
        }

        /// <summary>
        /// Call this at App startup once per launch otherwise it won't work.
        /// </summary>
        /// <param name="uid"></param>
        public static void Init()
        {
            if (_instance != null)
            {
                Debug.WriteLine("[DOCTOR] *** Already existing! ***");
                //throw new Exception("[DOCTOR] Cannot Init() as Doctor is already existing.");
                //_instance.Dispose();
                return;
            }
            Settings.Current.AddOrUpdateValue(SettingsKey, string.Empty); //delete traces of previous doctor
            var newDoctor = Doctor.Mobi.Hello(); //new instance created
        }
        #endregion

        /// <summary>
        /// This is automatically called when a message for the doctor is recieved
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task NotifyInstanceCreation(string fromClassName)
        {
            if (Instances.ContainsKey(fromClassName))
            {
                throw new Exception($"[DOCTOR] {fromClassName} tryed creating another instance.");
            }
            Instances.Add(fromClassName,"");
        }

        protected Dictionary<string, string> Instances = new Dictionary<string, string>();

    }
}
