﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AppoMobi.Common.Dto;
using AppoMobi.Common.Extensions;
using AppoMobi.Models;
using Newtonsoft.Json;
using DateTimeExtensions = AppoMobi.Common.Extensions.DateTimeExtensions;

namespace AppoMobi
{
    public class AppUser : NiftyBaseModel
    {
        public class ContentItem
        {
            public string UID { get; set; }  //content item id, empty
            public DateTime AddedTime { get; set; } //when

            public ContentItem()
            {
                AddedTime = DateTimeExtensions.GetLocalTimeNow();
                //            Id = Guid.NewGuid().ToString();
            }
        }

        //  public List<ContentItem> Likes { get; set; } = new List<ContentItem>();
        public List<ContentItem> Favorites { get; set; } = new List<ContentItem>();

        public static readonly AppUser Current = new AppUser();


        //FAVORITES
        public bool FavsChanged { get; set; } = true; //need rebuild or not

        
        public void Reset()
        
        {
            Favorites.Clear();
            Settings.Current.JSONFavs = "";
        }

        
        public bool IsItemsInFavorites(ProductDTO item)
        
        {
            var has = Favorites.FirstOrDefault(r => r.UID == item.Id);
            if (has != null)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        //
        //public bool IsInFavorites(string id)
        //    
        //{
        //    var has = AppUser.Current.Favorites.FirstOrDefault(r => r.UID == id);
        //    if (has != null)
        //    {
        //        return true;
        //    }
        //    return false;
        //}
        
        public AppUser()
        
        {
            //love comments!
            LoadFavs();
        }
        
        public async Task SaveFavs()
        
        {
            var stringConverted = await Task.Run(() => JsonConvert.SerializeObject(Favorites));
            Settings.Current.JSONFavs = stringConverted;
        }
        
        public void LoadFavs()
        
        {
            var stringLoaded = Settings.Current.JSONFavs;
            Favorites.Clear();
            var import = JsonConvert.DeserializeObject<List<ContentItem>>(stringLoaded);
            if (import != null) Favorites.AddRange(import);
        }


        
        public async Task<bool> BookmarkRemove(string id, bool save = false)
        
        {
            //remove
            var has = AppUser.Current.Favorites.FirstOrDefault(r => r.UID == id);
            if (has == null) return false;

            AppUser.Current.Favorites.Remove(has);
            if (save)
            {
                AppUser.Current.SaveFavs(); //do not wait
            }
            return true;
        }
        
        public bool BookmarkRemove(ContentItem item)
        
        {
            //remove
            if (item == null || AppUser.Current.Favorites.Count > 99) return false;
            AppUser.Current.Favorites.Remove(item);
            return true;
        }
        
        public void BookmarkAdd(ContentItem item)
        
        {
            AppUser.Current.Favorites.Insert(0, item);
        }
        
        public async Task<bool> BookmarkSwitch(string id)
        
        {
            //remove
            var has = AppUser.Current.Favorites.FirstOrDefault(r => r.UID == id);
            if (has != null)
            {
                BookmarkRemove(has);
                AppUser.Current.SaveFavs(); //do not wait
                return false;
            }
            else
            {
                BookmarkAdd(new ContentItem { UID = id, AddedTime = DateTime.Now });
                AppUser.Current.SaveFavs(); //do not wait
                return true;
            }
        }
        
        
        public async Task SyncFavoritesOut()
        
        {
            //var stringConverted = await Task.Run(() => JsonConvert.SerializeObject(AppUser.Current.Favorites));
            //Settings.Current.JSONFavs = stringConverted;
            //var r = await DataService.Current.PostAsync("sync/favs", null, stringConverted);
        }

        
        public async Task SyncFavoritesIn()
        
        {
            //var r = await DataService.Current.GetAsync("sync/favs");
            //if (r != null && r.Code == HttpStatusCode.OK)
            //{
            //    var stringLoaded = r.Body.Replace("\\", "");
            //    Favorites.Clear();
            //    var import = JsonConvert.DeserializeObject<List<ContentItem>>(stringLoaded);
            //    if (import != null) Favorites.AddRange(import);
            //    await SaveFavs();
            //}
        }
        
    }
}
