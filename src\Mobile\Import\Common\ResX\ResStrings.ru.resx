﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
		Microsoft ResX Schema

		Version 1.3

		The primary goals of this format is to allow a simple XML format 
		that is mostly human readable. The generation and parsing of the 
		various data types are done through the TypeConverter classes 
		associated with the data types.

		Example:

		... ado.net/XML headers & schema ...
		<resheader name="resmimetype">text/microsoft-resx</resheader>
		<resheader name="version">1.3</resheader>
		<resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
		<resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
		<data name="Name1">this is my long string</data>
		<data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
		<data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
			[base64 mime encoded serialized .NET Framework object]
		</data>
		<data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
			[base64 mime encoded string representing a byte array form of the .NET Framework object]
		</data>

		There are any number of "resheader" rows that contain simple 
		name/value pairs.

		Each data row contains a name, and value. The row also contains a 
		type or mimetype. Type corresponds to a .NET class that support 
		text/value conversion through the TypeConverter architecture. 
		Classes that don't support this are serialized and stored with the 
		mimetype set.

		The mimetype is used for serialized objects, and tells the 
		ResXResourceReader how to depersist the object. This is currently not 
		extensible. For a given mimetype the value must be set accordingly:

		Note - application/x-microsoft.net.object.binary.base64 is the format 
		that the ResXResourceWriter will generate, however the reader can 
		read any of the formats listed below.

		mimetype: application/x-microsoft.net.object.binary.base64
		value   : The object must be serialized with 
			: System.Serialization.Formatters.Binary.BinaryFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.soap.base64
		value   : The object must be serialized with 
			: System.Runtime.Serialization.Formatters.Soap.SoapFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.bytearray.base64
		value   : The object must be serialized into a byte array 
			: using a System.ComponentModel.TypeConverter
			: and then encoded with base64 encoding.
	-->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Тест</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>Добавить новость</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Просмотреть</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Всего</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Редактирование</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Назад к списку</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>ru</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Язык</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>Ru</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>Русский</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Ширина</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Регионы</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Создать запись</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Код</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Заголовок</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Приближение карты</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Центр карты Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>Центр карты X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Регион в мобильном приложении</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Просмотр</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Новая запись</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Вы уверены, что хотите удалить эту запись?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Удаление</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>или</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Добавить регион</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Ваш секретный код: </value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Панель управления Art of Foto</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Art of Foto: подтвердите создание учетной записи</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art of Foto: Мы получили запрос на создание учетной записи пользователя панели&lt;br&gt;
управления, с использованием вашего адреса электронной почты ({0}). &lt;br&gt;
&lt;br&gt;
Чтобы продолжить создание учетной записи с использованием этого адреса,&lt;br&gt;
пожалуйста, посетите следующую ссылку:&lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="_blank" rel="noopener"&gt;{1}&lt;/a&gt;&lt;br&gt;
&lt;br&gt;
Если вы не хотите создавать учетную запись или, если этот запрос был сделан не&lt;br&gt;
вами, то вы можете просто игнорировать это сообщение.&lt;br&gt;
&lt;br&gt;
Если вышеуказанная ссылка не работает, или у вас есть другие вопросы, касающиеся&lt;br&gt;
учетной записи, пожалуйста, свяжитесь с нами по адресу <EMAIL>.&lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Создание учетной записи</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>Мы выслали вам электронное письмо для подтверждения адреса электронной почты на ящик {0}.
Пожалуйста, следуйте инструкциям в этом письме для завершения создания учетной записи.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Регион</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Время</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Действие</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Параметры действия</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Изображение (ссылка)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Автор</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Редактировал(а)</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Последняя редакция</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Высота изображения</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Ширина изображения</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Спасибо за подтверждение адреса электронной почты. Пожалуйста,</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>кликните здесь чтоб войти</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>Вы успешно прошли аутентификацию с помощью</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>Пожалуйста, введите ваше имя пользователя для использования на сайте и нажмите кнопку Зарегистрироваться, чтобы завершить регистрацию.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Свяжите свою учетную запись {0}.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Неудачный вход со внешним сервисом.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Ошибка входа в систему</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Вход</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>Или используйте внешний сервис для входа</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Использовать локальную учетную запись</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Запомнить меня</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>Войти</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Создать учетную запись</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Пароль</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Подтверждение пароля</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Создайте новую учетную запись</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Вход</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Привет, </value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(Не забудьте) Выйти</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>Дополнительно</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Центры</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Адрес</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Сайт</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Телефон</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>Почта</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Метро</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>Экспортировал(а)</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Статус</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Активный</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Архив</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>Экспортировано</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Подназвание</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Город</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Страна</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Файлы</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Загрузить изображение</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Поиск по имени</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>Не разрешен</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Разрешен</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, требуется!</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>На экспорт</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Следует ли экспортировать эту запись в мобильное приложение</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Сортировать</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>По алфавиту</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>По дате изменения</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>ОШИБКА: ссылка на изображение не рабочая!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Панель управления</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Экспортировать:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Тип экспорта</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Доступ запрещен</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>Похоже, что у вас нет прав доступа к разделу. Если вы считаете, что это ошибкa, пожалуйста, обратитесь в службу поддержки.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Экспортирование</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Вы уверены, что хотите экспортировать?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Экспорт успешно произведен!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>Базовый URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Список центров</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>в разделе</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Все</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>Не идет на экспорт</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>Экспортировано</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>Идет на экспорт</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>в регионе</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>по</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Продукция</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Категории</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Служебное</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Разработчик</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Администратор</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Редактор</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Посетитель</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Принадлежность</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Приоритет</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>Стандартно</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Информация</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Подкатегории</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Родительская категория, в которую вставить этот элемент</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Базовая категория</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>Новостной слайдер</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Вторичная базовая категория</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Загрузить МИНИ-изображение</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>Ссылка на МИНИ-изображение</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Категория</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Совет Thalion</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>Мне нравится</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Единицы измерения</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Ключевые слова</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Новинка</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Показать</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Поиск..</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Все категории</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>По коду</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>По категории</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Товаровед</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Базовая категория 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Напишите причину</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Изображение анонса</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Тело</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Лицо</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Да</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Наш выбор</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>Забыли пароль?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>Нет перевода RU</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Not Found Error</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Неизвестная ошибка</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Мы в Интернет</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>Ссылка</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Клики</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>Действительно удалить</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Уход</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Уходы</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>ОШИБКА заполнения полей, см. ниже:</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Товары</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Управление</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Пользователи</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Отменить правки</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Вернуться</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>Список</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Ваши заметки, видны лишь в панели.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;Art of Foto&lt;/strong&gt; Панель управления</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>Запомнить?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Вспомнили пароль?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Сбросить пароль</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Вы выслали вам ссылку сброса пароля на электронную почту.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Сброс пароля</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Уже есть учетная запись?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Зарегистрироваться</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Фамилия</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>Согласен c &lt;strong&gt;условиями&lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>Вы должны принять условия пользовательского соглашения.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>Пароль и пароль подтверждения не совпадают.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>Строка {0} должна быть длиной от {2} символов.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>Неверное имя пользователя или пароль.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>Электронный адрес '{0}' уже занят.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Сброс пароля</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Вы можете сбросить свой пароль, нажав на &lt;a href="{0}"&gt;эту ссылку&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Новый пароль</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Придумайте ваш новый пароль и введите ниже.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>Вспомнили старый пароль?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Ссылка для восстановления пароля устарела.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Ваш новый пароль был установлен.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Класс</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Политика конфиденциальности</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Версия от</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Быстрый вход с</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>или введите ваши данные</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>Вход через соцсеть</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Сменить пароль</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Управление учетной записью</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Изменить</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Двух-факторная аутентификация</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Ваш телефон</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Отключено</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Вкл.</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Включить</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Настроить</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Выключить</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Вход с помощью социальной сети</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>Скин</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Ключ</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>Спасибо!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Благодарим вас за подтверждение вашей электронной почты. Теперь вы можете использовать свои учетные данные для входа в систему.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Ваша учетная запись еще не была назначена существующему клиенту Art of Foto. Обратитесь в службу технической поддержки.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>Ничего не найдено!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Клиентская панель управления</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Мы проводим некоторые работы на сайте, и он скоро вернется. Благодарим за терпение!</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>В разработке</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Мы работаем над сайтом</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>Рабочий стол</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Содержит</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Компоненты продукции</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Компоненты</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Описание RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Описание EN</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Описание FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>Если включено, то запись не будет экспортирована из панели</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>Вы можете загрузить файл или ввести существующий URL-адрес в следующем поле</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>Без действия</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Открыть продукт в программе</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Открыть веб-ссылку</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>Значение поля '{0}' должно быть уникально</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Языки контента</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Введите 2-буквенные коды языков</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Неизвестно</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>О Компании</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Изменить..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>По дате выпуска</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>По приоритету сортировки</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>На странице</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Быстрый экспорт</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>Подтверждаете экспорт раздела?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>Экспорт прошел успешно.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Загрузка</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Пуш-уведомления</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Ваши новости</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Открыть веб-ссылку внутри программы</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Простое сообщение</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Отправить сейчас же</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Сохранить черновик</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Активные пользователи</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Заходили недавно</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Заходили давно</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>Необходимо заполнить текстовое поле для английского языка.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Андроид</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Разработчик</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Всего устройств</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Черновик</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Отправлено на {0}</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Push-сообщения еще не были для вас настроены.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Держатели</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Общие клиентские настройки</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Изменения сохранены</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>По региону показа</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Количество активных устройств</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Общее количество установок</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Придумайте пароль</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Ваш секретный код {0} для входа в панель управления Art of Foto. </value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Некорректный email адрес или номер телефона.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>Пользователь с таким номером телефона не найден. Пожалуйста, зарегистрируйтесь.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Проверьте ваш телефон</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>Мы выслали проверочный код на указанный вами номер</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>Пользователь с указанным номером телефона уже зарегистрирован.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Неверный проверочный код.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Отклонено</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Онлайн-запись</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Окно заявок</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Рабочий график</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Запросы на бронирование</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Объекты бронирования</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Создать событие</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Введите название события</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Перетащите событие на календарь</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Ошибка связи</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Последняя версия моб.приложения</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Устаревшая версия моб.приложения</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Начало</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>Конец</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Весь день</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 недели</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Бронирование запрещено</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>Вы уверены, что хотите удалить событие?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Секундочку..</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Карточка события</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>подтверждено</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>требуется подтверждение</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Объект</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Категории услуг</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Услуги</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Услуга</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Клиент</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Подробности</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Полное имя</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>GPS координата X</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Обязательный уникальный ключ, для использования в мобильном приложении.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>Не используется</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Ссылка, или код продукта (поле "ключ") итп..</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>Возможное действие при клике по новости</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>Текст новости</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Языковой регион, для которого предназначена новость</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Картинка новостей</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>Язык для международных названий</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>Чем выше приоритет, тем выше в списке будет показана запись</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Включенные модули</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Разлогинить пользователей</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Заставить всех пользователей данного клиента панели управления перелогиниться, чтобы визуальные изменения вступили в силу.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>Как использовать</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Артикул</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Платформа</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>Показываемый заголовок</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>Текст сообщения</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Получатели</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Имя клиента в панели управления</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Цена</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Рабочие дни</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Рабочие часы с</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Рабочие часы до</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Если вы клиент, то</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>вход здесь</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Команды</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Вратари</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Редактирование события</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Тренеры</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Дата основания</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Рейтинг</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>по рейтингу</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>ВКонтакте</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>Доп. инфо</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Уровень сложности</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"за час" итп..</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Детали стоимости</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Дни недели</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Не известно</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Ограничение по полу</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Время начала</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>Время окончания</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Команда</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Детали мероприятия</value>
  </data>
  <data name="_Empty" xml:space="preserve">
    <value> </value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>События</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Элементы событий</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Организации</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Организация</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Тип события</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Сборы</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Соревнование</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Тип расписания</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>По дням недели</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>С фиксированными датами</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>Если с фиксированными датами, то дни недели не используются и наоборот.</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Расписания</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Разлогинить пользователя</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Дата рождения</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Требуется нормальное имя пользователя</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Перерыв до</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Перерыв с</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Рабочие часы</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Не установлено</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>Отклонено</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>В архиве</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Запрос на бронирование</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Понедельник</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Воскресенье</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Суббота</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Пятница</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Четверг</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Вторник</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Среда</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Рабочие часы детально</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Автоподтверждение бронирований</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>Автоматически подтверждать бронирования на основании исходных данных</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Явно указывать доступное для бронирования время для каждого объекта</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Требуется указание доступного времени для бронирования</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>Запись</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Галерея</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Ваше имя</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>Записаться!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Онлайн-запись</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Обращение</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Фамилия</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Строка</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Обновление данных..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>Для заданных условий нет доступного времени. Попробуйте изменить условия ниже:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Ой!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Отменено</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>Клиентский ID</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} ждет вас в {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>ждем вас в {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Ожидайте подтверждения на {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>Подтверждение ожидается</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Панель</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Тексты</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Моб. приложение</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>Нет доступного времени</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>Только для онлайн-записи</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Секции</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Статья</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>Смотрите также: </value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Маска вывода цен</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Оформление</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>По заметкам</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>Наши контакты</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Как добраться</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Объекты</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Сегодня</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Завтра</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>Через {0} дней</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>Через {0} день</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>Через {0} дня</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Аутентификация..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>Вы пробовали слишком много раз, повторите попытку через {0} мин.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>Регистрация не удалась. Проверьте, что вы указали действительный номер телефона или повторите попытку позже.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>Проверьте корректность введенных данных.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>Запись не удалась.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Проверка кода...</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>Мы отправили вам код подтверждения по SMS. Пожалуйста, введите его ниже для оформления бронирования:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>Бронирование не удалось. Возможно, кто-то уже занял это время, повторите попытку</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>Не удалось подтвердить данный код.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Перезагрузка данных..</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} в {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Код из SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Отменено</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Выбор языка</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Нажмите или перетащите файл в это поле..</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Загружаю оригинал..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>Открыть</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Без описания.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Галереи</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>Не отображается в мобильном приложении, служебное имя, используемое для выбора этого элемента в списках и т.д.</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>Ждем вас {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>В {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Статьи</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Открыть статью</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Время выпуска</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Лого заставки</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Лого компании</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Показывается над Наши Контакты</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Вопрос</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Уровень</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>Простой</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>Сложный</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>Нормальный</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Очень сложный</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>Обычный</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>Аватар</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Ответы</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Ответ</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Правильно</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Вопросы</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>По сложности</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR-Код ссылка на изображение</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Викторина</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>Время на один вопрос</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>Викторины</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>Время на все ответы в сек.</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Бренды</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Акции</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Включить с тэгами</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Исключить с тэгами</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Ключевые слова для поиска этого элемента</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Сохраните текущую запись, чтобы иметь возможность добавить к ней дочерние.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Бренд</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>Вознаграждения</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Процент правильных ответов</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Скидка</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Акция</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Состояние</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Закрыто</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>Планируется</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Активно</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Выход</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>По состоянию</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Загрузка...</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>секунд</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>секунда</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>секунда</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>секунды</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>секунд</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>Успешно</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>Скидка на купоне</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>Ссылка узнать больше</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>Кол-во вопросов для показа</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Импорт</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>* - включить все вопросы. Можете указать также другие свои теги вопросов для включения.</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>Открыть промоакцию в программе</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>Всего призов</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>Осталось призов</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Профиль</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>Подтвержден</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>Отклонен</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>Ошибка связи</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>Неизвестная ошибка</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Пользователь</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>Всего запросов</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Запрос</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Запросы</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Заказ</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>Всего подтвержденных</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>Код уже использован</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>с</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>м</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>ч</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>Дюймы</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>Миллиметры</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>Единицы измерения</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Список центров</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Последние новости</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>Ваш салон</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>   </value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Сменить регион</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Маршрут</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Избранное</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>Наши центры</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, проверьте ваши настройки подключения к интернету и попробуйте еще раз.</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>Мой любимый салон</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>Добро пожаловать!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>При загрузке данных произошла ошибка.
Проверьте ваше подключение к сети.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Необходима внешняя программа для навигации.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Вебсайт</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>Мы на карте</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Позвонить</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>Сохраните его для быстрого доступа!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Добавить в избранное</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Повторить попытку</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Как найти</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>Действительно удалить центр из избранного?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Очистить избранное</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Теперь у вас есть быстрый доступ к данным этого центра из вкладки Избранное.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>О центре</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>Программе необходим доступ к вашей геопозиции, чтобы помочь нас найти. Включить доступ сейчас?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Ваш GPS отключен, пожалуйста, включите его, чтобы мы могли помочь вам с гео-поиском.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>На метро:</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Информация</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Свяжитесь с нами</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Мы на карте</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto и правообладатели</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Определяем местоположение..</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>Список</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>Регион</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Салонам</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Специалистам</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Вход для партнеров</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>Как нас найти</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>Вы можете добавить ваш любимый центр в этот раздел для быстрого доступа.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Проложить маршрут</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>Заменить избранный центр на этот?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>К списку центров</value>
  </data>
  <data name="km" xml:space="preserve">
    <value> км</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Найти свой салон</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>Поздравляем!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Отлично</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Центры</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>О нас</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Внешний вид</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>Нижнее меню без текста</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Показывать страницу Мой салон при запуске</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Начальная страница</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Отключить фоновую анимацию для экономии энергии</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Обратно к списку</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Показывать слайды приветствия при запуске программы</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>А ещё..</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Посмотреть заставку</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>НАЧАТЬ</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>Вышла новая версия программы, пожалуйста, обновитесь!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>До свидания!</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Беззвучные push-уведомления</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>Скрыть это приветствие?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>Найдите</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>Добавьте</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Дважды нажите</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>Следите</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>на иконку раздела, чтобы вернуться в его заголовок</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>По расстоянию</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>версия</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Каталог продукции</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Смотрите подкатегории:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>Вся продукция из раздела</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>КАК ИСПОЛЬЗОВАТЬ</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Результаты поиска</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>читать далее</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Поиск продукции</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Поиск</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>ПОПУЛЯРНОЕ</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>Знакомьтесь</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>Вы искали</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>Введите большее количество букв!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Поиск центров</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>Системные настройки</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Позже</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>Включить GPS</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>Сортировано по расстоянию</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>Сортировано по алфавиту</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>НЕ ПРОПУСТИТЕ</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>Вы можете добавлять товары из каталога в список желаний. 
Список удобно использовать для дальнейших покупок в салоне, или чтобы советоваться с вашим косметологом и с друзьями. </value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>Список желаний</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>О нас..</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Подтверждать удаления из списков</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>ДРУГИЕ КАТЕГОРИИ</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Перейти в каталог</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Поделиться</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Продукция</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>ПЕРЕЙТИ В КАТЕГОРИЮ</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Артикул</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>В каталог</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>Мой список желаний</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Очистить список</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>Продажа средств для домашнего ухода производится только через сертифицированные центры THALION.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>Если в вашем салоне не оказалось товара в наличии, пожалуйста, сообщите нам об этом и мы поможем вам с приобретением.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Где найти</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>Связаться с нами</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Удалить из списка желаний?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>Вы уверены, что хотите очистить ваш Список желаний?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>Чтобы рассчитать расстояние до нас, нам потребуются ваши координаты.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>В вашем списке {0} {1}.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>продуктов</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>продукт</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>продукт</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>продукта</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>продуктов</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Узнать больше</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Добавлено в список желаний</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Нажмите еще раз для выхода из программы</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>Весь каталог..</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>В оглавление</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Где найти</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>Влево</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>А еще..</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>СМОТРИТЕ ТАКЖЕ</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Каталог продукции</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Избранное</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>Мои предпочтения</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>Если вы хотите, чтобы мы могли найти ближайшие к вам центры THALION,  ответьте положительно в следующем окне.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Привет</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Подключиться</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Проверить настройки</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Обработка заказа ..</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>Время</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>Калькулятор времени</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>Мех</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>Растяжение меха</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>Фокусное расстояние</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>В этом разделе вы можете получить требуемую поправку к экспозиции при использовании камеры с мехом</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>мс</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>,</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>Проект Art of Foto основан в 2011 году и придерживается направления аналоговой черно-белой фотографии. 
В 2015 году мы открыли фото галерею Art of Foto, лабораторию ручной печати и фотостудию большого формата в Санкт-Петербурге.

Наши основные задачи: сохранение и популяризация фотографического наследия России, поддержание современной фотографии и развитие её художественного и технического уровня.

Коллекция галереи – это фотографии, представляющие историческую и художественную ценность. Собрание составляют работы таких известных авторов, как Валерий Плотников, Борис Смелов, Людмила Таболина, Валентин Самарин, Джон Уимберли, Джон Секстон, Сергей Леонтьев, Ян Шлегель, Александр Китаев и др. Также в коллекции имеются амбротипы, дагеротипы и большое количество исторических фотографий, датируемых XIX – XX вв.

Участники творческого союза Art of Foto ежегодно организовывают выставки чёрно-белых фотографий ручной печати в России и Европе с целью создания положительного образа русской традиционной и современной фотографии у ценителей и экспертов со всего мира.</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Большая Конюшенная улица, 1, Россия, Санкт-Петербург</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>Ср – Вс: 12.00 – 20.00
Без перерыва
Вход свободный
Пн, Вт – Выходные</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>Свернуть</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>Развернуть</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>С - нажмите один раз для сброса текущего значения, второй раз для полного сброса
% - используется в комбинации с предварительно введенной операцией.
Например: нажмите + затем % затем введите десятичное число. 
Результат: вы прибавите введенные вами проценты к существующему времени.
</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>Вы можете переключаться между мм и дюймами, нажимая на соответствующий текст справа на поле ввода.</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>Звуковые эфекты</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>Ввод часов во времени</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>Таймер стартовал от {0}</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>Закончилось время {0}</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>Не используется.</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>части</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>мл</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>Преобразование 35мм</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 мм</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>Смешать проявитель</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>Проявитель</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>Вы можете переключаться между мм и дюймами, нажимая на соответствующий текст справа на поле ввода.</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>В этом разделе вы можете получить фокусное расстояние вашего объектива в 35 мм эквиваленте.</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>Формат кадра</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>В пределах объема</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>От заданного</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>Результат (мл.)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>Часть А</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>Часть Б</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>Вода</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>Расчет составных частей проявителя, исходя из указанных объемов.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>Автоподбор объемов для получения указанного объема проявителя.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>Результат</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/Stop</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>Ваш результат {0:0.00}</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Выберите вкладки  ({0}/{1} минимум {2})</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Выбор элементов нижнего меню</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Тема оформления</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>Темная</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>Светлая</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>Приложение от AppoMobi</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, диагональ {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>Готовый раствор</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>Нравится {0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>Спасибо за ваш отзыв, мы постараемся улучшить наше приложение!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>Пожалуйста, поставьте нам оценку в GooglePlay, будем очень благодарны!</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Обязательно</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>Без фильтра</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>Расчёт выдержки с учетом влияния на неё эффекта Шварцшильда ("Reciprocity effect")</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Слишком большая</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>Выдержка</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>Мин</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>Сек</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>д</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>Данные от 2016 года
Производитель рекомендует делать поправку при проявке: 
&gt;2 сек: -10%
&gt;50 сек: -20%
&gt;20 мин: -30%</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>Есть некоторые вещи</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>Формула поправки не сообщается производителем..</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>Данное время проявки не рекомендуется производителем.</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>Шварцшильд</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>ПРЕДУПРЕЖДЕНИЕ

Значение поправки при съемке с фильтром усреднено по производителю и типу освещения. 

Рекомендуем тестировать фильтры и плёнки для получения стабильного и точного результата.</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>В данном диапазоне используется собственная формула поправки, в отсутствие данных от производителя</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>По данным производителя в этом диапазоне поправку делать не надо</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>Данные 2002 года
По данным производителя до 1 секунды поправку делать не надо, больше 1 секунды данных о поправке не предоставляет</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>Камера предназначеня для просмотра негативов в реальном времени. Можно сменить фильтр, камеру, а также сохранить кадр в галерею.</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>Негативная камера</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Камера</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>Этот модуль не может работать без разрешений. Авторизуйте приложение в системных настройках или удалите приложение и установите его с нуля, чтобы снова получить запрос на разрешения.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>Нет разрешений</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>Видоискатель</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>Видоискатель</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Выбор</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Применить</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>Объективы для "{0}"</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>Сменить формат</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>Редактировать пресеты</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>Модуль предназначен для примерной симуляции аналоговых видоискателей. Можно зуммировать экран пальцами. Зеленые значения реагируют на нажатие.</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>Преcет</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>Пленки</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Фильтры</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>Без объектива</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Формат</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>Добавить объектив (мм)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>Экран всегда включен</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>Поправка</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>Фотографии с геотегами</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>Потребуются разрешения для геотегирования фотографий</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Использовать папку Art Of Foto</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>Открыть</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>Проявка</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>Проявочный стол</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Повторить попытку</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>Выдержка</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>Экспозиция</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>Затвор</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Еще раз</value>
  </data>
  <data name="ExposureMeter" xml:space="preserve">
    <value>Замер экспозиции</value>
  </data>
  <data name="SpotMeter" xml:space="preserve">
    <value>Спотметр</value>
  </data>
  <data name="SpotMeterShort" xml:space="preserve">
    <value>Спотметр</value>
  </data>
</root>