﻿using AppoMobi.Forms.Common.ResX;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;



namespace AppoMobi.Models
{

    public class BaseViewModelBak : BindableObject
    {

        public string Uid { get; protected set; } = Guid.NewGuid().ToString();


        static BaseViewModelBak()
        {

            //AuthenticationService = DependencyService.Get<IAuthenticationService>();
            //     XSnackService = DependencyService.Get<IXSnack>();

        }

        public string ThisName
        {
            get
            {
                return this.GetType().Name;
            }
        }

#if DEBUG
        private bool _IsDebug = true;
#else
        private bool _IsDebug;
#endif
        public bool IsDebug
        {
            get { return _IsDebug; }
            set
            {
                if (_IsDebug != value)
                {
                    _IsDebug = value;
                    OnPropertyChanged();
                }
            }
        }

        public string BuildDesc
        {
            get
            {
                return App.BuildDesc;
            }
        }


        //    public static readonly IXSnack XSnackService;

        private bool _IsBusy;
        public bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    OnPropertyChanged("IsEmpty");
                }
            }
        }

        private string _Title;
        public string Title
        {
            get { return _Title; }
            set
            {
                if (_Title != value)
                {
                    _Title = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _ShowLogo;
        public bool ShowLogo
        {
            get { return _ShowLogo; }
            set
            {
                if (_ShowLogo != value)
                {
                    _ShowLogo = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _HasSearch;
        public bool HasSearch
        {
            get { return _HasSearch; }
            set
            {
                if (_HasSearch != value)
                {
                    _HasSearch = value;
                    OnPropertyChanged();
                }
            }
        }

        //public ICommand CommandOnSearch; 


        private string _Hint;
        public string Hint
        {
            get { return _Hint; }
            set
            {
                if (_Hint != value)
                {
                    _Hint = value;
                    OnPropertyChanged();
                }
            }
        }



        protected bool SetProperty<T>(ref T backingStore, T value,
            [CallerMemberName] string propertyName = "",
            Action onChanged = null)
        {
            if (EqualityComparer<T>.Default.Equals(backingStore, value))
                return false;

            backingStore = value;
            onChanged?.Invoke();
            OnPropertyChanged(propertyName);
            return true;
        }

        protected static async Task ShowFeatureNotAvailableAsync()
        {
            await Application.Current.MainPage.DisplayAlert(
                "Under construction",
                ResStrings.OwnerTitle,
                ResStrings.BtnOk);
        }


    }
}
