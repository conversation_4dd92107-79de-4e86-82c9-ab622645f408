﻿using DrawnUi.Views;

namespace AppoMobi.Main
{


    /// <summary>
    /// Include drawn inside non-drawn wrapper
    /// </summary>
    public class ScreenCanvas : Canvas, IIncludedContent
    {
        public ScreenCanvas()
        {
            HorizontalOptions = LayoutOptions.Fill;
            Gestures = GesturesMode.Lock;
            //HardwareAcceleration = HardwareAccelerationMode.Enabled;
        }

        public IPageEnhancedNav Daddy { get; set; }

        //effects for cells

        //tuch highlighting below
        public bool IsPressed { get; set; }
        public bool IsPanned { get; set; } = false;

        public virtual void CommandFromPopupRecieved(string command, string param)
        {
        }

        public virtual void CommandToContentRecieved(string command, string param)
        {
        }

        /// <summary>
        ///     Calling base is not needed
        /// </summary>
        //-----------------------------------------------------------------
        public virtual void OnAppearing()
            //-----------------------------------------------------------------
        {
        }

        /// <summary>
        ///     Callinf base is not needed.
        /// </summary>
        //-----------------------------------------------------------------
        public virtual void OnDisappearing()
            //-----------------------------------------------------------------
        {
        }

        public void Init(IPageEnhancedNav daddy)
        {
            Daddy = daddy;
        }

        public bool CommandToPopup(string command, string param)
        {
            return Daddy.CommandToPopup(command, param);
        }

        public bool CommandToPage(string command, string param = null)
        {
            if (Daddy != null)
            {
                Daddy.CommandFromContent(command, param);
                return true;
            }

            return false;
        }

        public override void OnDisposing()
        {
            Daddy = null;

            base.OnDisposing();
        }

        public virtual void OnTabActivated()
        {
        }

        public virtual void OnTabDeactivated()
        {
            var c = 0;
            foreach (var child in Navigation.NavigationStack)
            {
                if (c == 0)
                    continue;
                ;
                if (child is IPageEnhancedNav) ((IPageEnhancedNav)child).TabDisactivated();

                c++;
            }
        }

        public virtual void OnRightIcon1Clicked()
        {
        }

        public virtual void OnRightIcon2Clicked()
        {
        }

        public async void HandlerGoBack(object sender, EventArgs e)
        {
            await Daddy.GoBack();
        }

        public async Task GoBack()
        {
            await Daddy.GoBack();
        }

        /// <summary>
        ///     Calling base is not needed
        /// </summary>
        public virtual void OnUpdatedControls()
        {
        }

        public virtual void AnimatingPage()
        {
        }

        public virtual void PreparingPageAnimations()
        {
        }

        public virtual void OnBeforeGoBack()
        {
        }
    }
}
