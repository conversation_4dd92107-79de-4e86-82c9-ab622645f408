﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Controls;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.UI;
using AppoMobi.Xam;
using System.Collections.Generic;


namespace AppoMobi.Main
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ContentSettings : IncludedContent
    {
 
        public static List<OptionsListItem> OptionList = new List<OptionsListItem>();

 
        private MainVModel Model { get; set; }

        public ContentSettings(IPageEnhancedNav daddy)
        {
            try
            {
                InitializeComponent();
                Init(daddy);

                Model = MainVModel.Instance;
                BindingContext = Model;

                //TOOLBAR ICONS
                //Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
                //Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

                //Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
                //Daddy.ToggleButtonVisibility(ButtonType.Right2, true);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }


        }



    }

}
