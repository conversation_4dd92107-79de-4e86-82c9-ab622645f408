﻿using System;
using Android.Content;
using Android.Graphics;
using Android.Renderscripts;
using Android.Util;
using Android.Views;
using AppoMobi.Droid.Camera.Models;
using AppoMobi.Forms.Content.Camera.Controls;
using Java.Lang;
using Math = System.Math;
using Boolean = Java.Lang.Boolean;
using Console = System.Console;
using Debug = System.Diagnostics.Debug;
using Element = Android.Renderscripts.Element;
using Exception = System.Exception;
using Image = Android.Media.Image;
using Point = Android.Graphics.Point;
using RelativeLayout = Android.Widget.RelativeLayout;
using Semaphore = Java.Util.Concurrent.Semaphore;
using Size = Android.Util.Size;
using SizeF = Android.Util.SizeF;
using Stream = Android.Media.Stream;
using StringBuilder = Java.Lang.StringBuilder;
using View = Android.Views.View;

namespace AppoMobi.Droid.Camera.Views
{
    public class AutoFitTextureView : TextureView
    {
        public override bool OnTouchEvent(MotionEvent e)
        {
            if (ScaleGestureDetector == null)
                return base.OnTouchEvent(e);

            //without this the whole thing just fails with ACTION_CANCEL
            this.Parent.RequestDisallowInterceptTouchEvent(true);
            
            //Debug.WriteLine($"[ZOOM] Passing: {e}");

            ScaleGestureDetector.OnTouchEvent(e);

            return true;
        }

        /// <summary>
        /// This is set manyally after the view is created by the fragment.
        /// </summary>
        public ScaleGestureDetector ScaleGestureDetector { get; set; }


        public AutoFitType FitMode { get; set; } = AutoFitType.Fill;

        private int mRotatedContentWidth = 0;
        private int mRotatedContentHeight = 0;

        public AutoFitTextureView(Context context, RenderScript rs) : base(context, null)
        {
            _rs = rs;
        }

        public AutoFitTextureView(Context context, IAttributeSet attrs) : base(context, attrs, 0)
        {

        }

        public AutoFitTextureView(Context context, IAttributeSet attrs, int defStyle) : base(context, attrs, defStyle)
        {
        }

        /// <summary>
        /// Sets the aspect ratio for this view. The size of the view will be measured based on the ratio
        /// calculated from the parameters.Note that the actual sizes of parameters don't matter, that
        /// is, calling setAspectRatio(2, 3) and setAspectRatio(4, 6) make the same result.
        /// </summary>
        /// <param name="width">Relative horizontal size</param>
        /// <param name="height">Relative vertical size</param>
        public Size SetRotatedContentSize(int width, int height, int cameraRotation, StretchModes mode) 
        {
            if (width < 0 || height < 0)
            {
                throw new IllegalArgumentException("Size cannot be negative.");
            }

            _displayMode = mode;

            mRotateTexture = cameraRotation;

            if (mRotateTexture != 0 && mRotateTexture != 270)
            {
                mRotatedContentWidth = height;
                mRotatedContentHeight = width;
            }
            else
            {
                mRotatedContentWidth = width;
                mRotatedContentHeight = height;
            }

            //mRotatedContentWidth = width;
            //mRotatedContentHeight = height;

            RequestLayout();
            Invalidate();

            return new Size(mRotatedContentWidth, mRotatedContentHeight);
        }

        protected int mRotateTexture;
        
        private StretchModes _displayMode;

        private RenderScript _rs;

        public void SetDisplayMode(StretchModes mode)
        {
            _displayMode = mode;
            RequestLayout();
            //Invalidate();
        }

        private float _RequestedZoomScale = 1.0f;

        private float _ViewportScale = 1.0f;
        public float ViewportScale
        {
            get { return _ViewportScale; }
            set
            {
                if (_ViewportScale != value && !Single.IsNaN(value))
                {
                    _ViewportScale = value;
                }
            }
        }


        public void SetZoomScale(float scale)
        {
            _RequestedZoomScale = scale;
            RequestLayout();
       //     Invalidate();
        }

        protected override void OnSizeChanged(int w, int h, int oldw, int oldh)
        {
            base.OnSizeChanged(w, h, oldw, oldh);
        }

        protected override void OnMeasure(int widthMeasureSpec, int heightMeasureSpec)
        {
            base.OnMeasure(widthMeasureSpec, heightMeasureSpec);

            if (0 == widthMeasureSpec || 0 == heightMeasureSpec)
            {
                return;
            }

            int measuredWidth = MeasureSpec.GetSize(widthMeasureSpec);
            int measuredHeight = MeasureSpec.GetSize(heightMeasureSpec);

            //=============================================
            //todo
            //can avoid zooming texture if we zoomed with camera lens
            var zoomScale = _RequestedZoomScale;
            //=============================================


            if (0 == mRotatedContentWidth || 0 == mRotatedContentHeight)
            {
                SetMeasuredDimension(measuredWidth, measuredHeight);
            }
            else
            {
                //var scaleParams = new ScriptField_ScaleSurfaceParams(_rs,1);
                
                //scaleParams.Set_fuulScreen(0, _fullScreen, false);
                //scaleParams.Set_width(0, width, false);
                //scaleParams.Set_height(0, height, false);
                //scaleParams.Set_mRotatedContentWidth(0, mRotatedContentWidth, false);
                //scaleParams.Set_mRotatedContentWidth(0,mRotatedContentWidth,false);
                //// Copy all values at once to the allocated memory of the struct.
                //scaleParams.CopyAll();
                //_rc.bind_tableau(scaleParams);

                // https://www.egeniq.com/blog/speed-android-computations-using-renderscript


                float imageAspectRatio = (float)mRotatedContentWidth/mRotatedContentHeight ;

                Matrix matrix = new Matrix();
                
                var fitW = measuredWidth;
                var fitH = (int)(measuredWidth / imageAspectRatio);

                // todo - just keep ratio, dont zoom viewport and use manual zoom only
                //if (_displayMode == StretchModes.Default)
                //{

                //}
                //else

                //FILL
                if ((fitH <= measuredHeight || fitW <= measuredWidth) && (_displayMode == StretchModes.Fill || _displayMode == StretchModes.Default))
                {
                    fitW = measuredWidth;
                    fitH = measuredHeight;
                    var centerX = fitW / 2;
                    var centerY = fitH / 2;

                    if (mRotateTexture == 90 || mRotateTexture == 270)
                    {
                        matrix.PreRotate(mRotateTexture, centerX, centerY);

                        //rotated dimensions
                        var scaleX = (float)fitW / (float)fitH;
                        var scaleY = (float)fitH / (float)fitW;

                        var savedXS = scaleX;
                        var savedYS = scaleY;

                        if (scaleX > imageAspectRatio)
                        {
                            //height need to be larger than screen
                            scaleY /= imageAspectRatio/scaleX;

                        }
                        else
                        if (scaleX < imageAspectRatio)
                        {
                            //width has to be larger than screen
                            scaleX *= imageAspectRatio/scaleX;
                        }

                        var sX = scaleY - savedYS;
                        var sY = scaleX - savedXS;
                        ViewportScale = 1 + Math.Max(sX , sY);//Math.Max(scaleX, scaleY);..(float)Math.Min(scaleX, scaleY); //

                        matrix.PostScale(scaleX* zoomScale, scaleY* zoomScale, centerX, centerY);
                    }
                    else //normal
                    {
                        var scaleX = 1.0f;
                        var scaleY = 1.0f;

                        var savedXS = scaleX;
                        var savedYS = scaleY;

                        if (scaleX > imageAspectRatio)
                        {
                            //height need to be larger than screen
                            scaleY /= imageAspectRatio / scaleX;

                        }
                        else
                        if (scaleX < imageAspectRatio)
                        {
                            //width has to be larger than screen
                            scaleX *= imageAspectRatio / scaleX;
                        }

                        var sX = scaleY - savedYS;
                        var sY = scaleX - savedXS;
                        ViewportScale = 1 + Math.Max(sX, sY);//Math.Max(scaleX, scaleY);..(float)Math.Min(scaleX, scaleY); //

                        matrix.PostScale(scaleX * zoomScale, scaleY * zoomScale, centerX, centerY);
                    }
                }
                else
                {
                    //FIT
                    ViewportScale = 1.0f;

                    //height not fitting
                    if (fitH > measuredHeight)
                    {
                        fitH = measuredHeight;
                        fitW = (int)(measuredHeight * imageAspectRatio);
                    }


                    float centerX = fitW / 2;
                    float centerY = fitH / 2;
            
                    if (mRotateTexture == 90 || mRotateTexture == 270)
                    {
                        matrix.PreRotate(mRotateTexture, centerX, centerY);

                        //rotated spawwed dimensions
                        var scaleX = (float)fitW / (float)fitH;
                        var scaleY = (float)fitH / (float)fitW;

                        var sX = scaleY - 1;
                        var sY = scaleX - 1;
                        ViewportScale = 1 + Math.Max(sX, sY);//Math.Max(scaleX, scaleY);..(float)Math.Min(scaleX, scaleY); //

                        matrix.PostScale(scaleX* zoomScale, scaleY* zoomScale, centerX, centerY);
                    }
                    else //normal{
                    {
                        //todo, im sleepy
                        matrix.PostScale(zoomScale, zoomScale, centerX, centerY);
                    }
                }

                SetTransform(matrix);
                SetMeasuredDimension(fitW, fitH);

            }
        }
    }
}