﻿using System;

namespace AppoMobi.Common.Dto
{
    //****************************************************
    public class CustomerInfoFromClient // from client-side
    //****************************************************
    {
        //can change on client side
        public string FirstName { get; set; } //can change
        public string LastName { get; set; } //can change
        public DateTime? BirthDate { get; set; } //can change

        public string Id { get; set; }
    }

    //****************************************************
    public class CustomerInfoFromServer : DbCustomerData //must be stored secure
    //****************************************************
    {
        //can change on client side
        public string FirstName { get; set; } //can change
        public string LastName { get; set; } //can change
        public DateTime? BirthDate { get; set; } //can change

        //server side
        public string Id { get; set; }
        public string Username { get; set; }
        public string PhoneNumber { get; set; } //think about how to change it in the future
    }

    //****************************************************
    public class DbCustomerData //can be stored unsecure
    //****************************************************
    {
        public DateTime? LastPlayedTime { get; set; }

        //public string Daylies { get; set; } //keys ids of played this day

        //public List<QuizzPrizeDto> Prizes { get; set; }
        //[JsonIgnore]
        //public int PrizesTotal
        //{
        //    get { return Prizes.Count; }
        //}
    }

}
