﻿using System;
using System.Diagnostics;
using FFImageLoading.Maui;


namespace AppoMobi
{

    /// <summary>
    /// Set WidthRequest to change size
    /// otherwise will take whole space in container
    /// </summary>
    //================================================
    public class ImageResponsive:CachedImage
    //================================================
    {
        
        public ImageResponsive()
        
        {
            FadeAnimationEnabled = false;
            Aspect = Aspect.Fill;
            HorizontalOptions=LayoutOptions.FillAndExpand;
            VerticalOptions=LayoutOptions.FillAndExpand;
            Success += Img_OnSuccess;
            BitmapOptimizations = false;
            SizeChanged += Img_OnSizeChanged;
        }

        private bool Initialized { get; set; }
        public double Ratio { get; set; }

        
        private void Img_OnSizeChanged(object sender, EventArgs e)
        
        {
            if (Ratio > 0)
            {
                HeightRequest = Ratio * Width;
            }
        }

        
        private void Img_OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        
        {
            double dynamicHeight = 0;
            if (e.ImageInformation.OriginalWidth < 1.0)
            {
                dynamicHeight = 0;
            }
            else
            {
                Ratio = (double)e.ImageInformation.OriginalHeight / (double)e.ImageInformation.OriginalWidth;
                HeightRequest = Ratio * Width;
            }
        }
    }



    //***********************************************
    public class CImageView : ContentView
    //***********************************************
    {
        private CImage Image { get; set; }

        
        // Source
        
        private const string nameSource = "Source";
        public static readonly BindableProperty SourceProperty = BindableProperty.Create(nameSource, typeof(string), typeof(CImageView), string.Empty); //, BindingMode.TwoWay
        public string Source
        {
            get { return (string)GetValue(SourceProperty); }
            set { SetValue(SourceProperty, value); }
        }
        
        private bool CreateImage(string source=null)
        
        {
            //delete old
            if (Image != null)
            {
                Image.Error -= OnError;
                Image.Success -= OnSuccess;
                Image.Source = null;                
            }

            Image = new CImage();
            Image.Error += OnError;
            Image.Success += OnSuccess;

            Image.Aspect = Aspect.Fill;
            Image.DownsampleToViewSize = false;
            Image.IsOpaque = true;
            Image.HorizontalOptions=LayoutOptions.FillAndExpand;
            Image.VerticalOptions=LayoutOptions.FillAndExpand;

            this.Content = Image;
            //if(Core.IsAndroid) 
            //    GC.Collect();

            Image.Source = source;

            return true;
        }
        
        private void OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        
        {
           

        }
        private bool once { get; set; }
        
        private void OnError(object sender, CachedImageEvents.ErrorEventArgs e)
        
        {
            if (once) return;
            once = true;

        //    CreateImage("resource://AppoMobi.Mobile.Images.Brand.wallpaper_low.jpg");
            CreateImage("fxc.png");
        }

        
        public CImageView()
        
        {
            CreateImage();

        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                //property changed
                case nameSource:
                    Image.Source = Source;
                    break;


            }

        }

	


    }

  

    //***********************************************
    public class CImage : CachedImage
    //***********************************************
    {
        public string Tag { get; set; }


        
        // SourceOnError
        
        private const string nameSourceOnError = "SourceOnError";
        public static readonly BindableProperty SourceOnErrorProperty = BindableProperty.Create(nameSourceOnError, typeof(string), typeof(CImage), string.Empty); //, BindingMode.TwoWay
        public string SourceOnError
        {
            get { return (string)GetValue(SourceOnErrorProperty); }
            set { SetValue(SourceOnErrorProperty, value); }
        }

        private bool HadError { get; set; }

        public CImage()
        {
            ErrorPlaceholder = "placeholder.jpg";
        //    LoadingPlaceholder = "resource://AppoMobi.Mobile.Images.UI.loading.gif";
            RetryCount = 10;
            RetryDelay = 2000;
            
            // Error += OnError;
            // Success += OnSuccess;
        }

        private void OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        {

            //if (HadError && Source.ToString().Contains("fxc.png"))
            //{
            //var newShit = new CImage();
            //    newShit.Tag = "Testing shit";
            //    newShit.Source = "resource://AppoMobi.Mobile.Images.Brand.wallpaper_low.jpg";
            //}

        }

        private bool once { get; set; }

        private async void OnError(object sender, CachedImageEvents.ErrorEventArgs e)
        {
            if (once) return;
            Debug.WriteLine("** FAILED loading image: "+Source);
            once = true;


            //try load low quality

            //string source = Source.ToString();
            ////source = source.ToLower();

            ////local:
            //if (source.Contains("EmbeddedResource:"))
            //{
            //    if (source.Contains(".png"))
            //        source = source.Replace(".png", "_low.png");

            //    if (source.Contains(".jpg"))
            //        source = source.Replace(".jpg", "_low.jpg");


            //    //cut name
            //    source = source.TrimStart("EmbeddedResource:".ToCharArray());
            //    var rs = source.LastIndexOf("/?assembly=");
            //    source = source.Substring(0,rs).Trim();
               
            //}
            //else
            //if (source.ToLower().StartsWith("http"))
            //{


            //}
            ////
            ////else do not process
            //// ...

            //reload
            Source = null;
            if (string.IsNullOrEmpty(SourceOnError))
            {
                Source = "fxc.png";
                HadError = true;
            }
            else
            {
                Source = SourceOnError;
            }
        }
    }
}
