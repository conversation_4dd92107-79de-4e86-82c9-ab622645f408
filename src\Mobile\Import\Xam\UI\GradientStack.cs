﻿namespace AppoMobi.Xam
{

    /*

    //****************************************************
    public class GradientStack : StackLayout
    //****************************************************
    {


        public EventHandler RendererNeedUpdate { get; set; }

        
        protected override void OnSizeAllocated(double width, double height)
            
        {
            base.OnSizeAllocated(width, height);
            if (width > 0) RendererNeedUpdate?.Invoke(this, null);
        }

        //-------------------------------------------------------------
        // StartColor
        //-------------------------------------------------------------
        private const string nameStartColor = "StartColor";
        public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(GradientStack), Colors.Transparent); //, BindingMode.TwoWay
        public Color StartColor
        {
            get { return (Color)GetValue(StartColorProperty); }
            set { SetValue(StartColorProperty, value); }
        }


        //-------------------------------------------------------------
        // EndColor
        //-------------------------------------------------------------
        private const string nameEndColor = "EndColor";
        public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(GradientStack), Colors.Transparent); //, BindingMode.TwoWay
        public Color EndColor
        {
            get { return (Color)GetValue(EndColorProperty); }
            set { SetValue(EndColorProperty, value); }
        }


        
        public StackOrientation GradientOrientation
        
        {
            get;
            set;
        }
    }
    */
}
