﻿using Android.Webkit;
using AppoMobi.Xam;
using Java.Interop;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using WebView = Android.Webkit.WebView;

namespace AppoMobi.Droid.Renderers
{
	public class CustomFbWebViewRenderer : WebViewHandler
	{
		const string JavascriptFunction = "function invokeCSharpAction(data){jsBridge.invokeAction(data);}";

        WebView Control => PlatformView as WebView;

        protected override void ConnectHandler(WebView platformView)
        {
            base.ConnectHandler(platformView);

            this.FormsControl = (XamWebView)this.VirtualView;

            Control.SetWebViewClient(new CustomWkWebViewRenderer.MyWebViewClient( $"javascript: {JavascriptFunction}"));
            Control.AddJavascriptInterface(new JSBridge(this), "jsBridge");

            if (Android.OS.Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.Lollipop)
            {
                var check = CookieManager.Instance.AcceptCookie();
                check = CookieManager.Instance.AcceptThirdPartyCookies(Control);

                CookieManager.Instance.SetAcceptCookie(true);
                CookieManager.Instance.SetAcceptThirdPartyCookies(Control, true);

                string cookieString = "datr=eTiwX31TzpjZSn3hHJKJJZ2H; domain=.facebook.com; path=/";
                CookieManager.Instance.SetCookie("https://facebook.com", cookieString);
            }

            FormsControl.RendererCommand += OnRendererCommand;

            Control.SetBackgroundColor(FormsControl.WebViewColor.ToPlatform());

            if (FormsControl.ZoomEnabled)
            {
                //If you want to support zoom buttons
                //Control.Settings.BuiltInZoomControls = true;
                Control.Settings.SetSupportZoom(true);

                Control.SetInitialScale(100);
                Control.Settings.UseWideViewPort = true;
                Control.Settings.LoadWithOverviewMode = false;
            }

            Control.OverScrollMode = Android.Views.OverScrollMode.IfContentScrolls;

        }

        protected override void DisconnectHandler(WebView platformView)
        {
            FormsControl.RendererCommand -= OnRendererCommand;
            FormsControl = null;

            Control.RemoveJavascriptInterface("jsBridge");

            base.DisconnectHandler(platformView);
        }

     

		public XamWebView FormsControl { get; set; }

		public void StopPlayback()
		{
			// Control.PauseTimers();;
			Control.OnPause();
		}

		public void ResumePlayback()
		{
			Control.OnResume();
		}


 

		
		private void OnRendererCommand(object sender, EventArgs e)
		
		{
			if (sender is string)
			{
				var command = (string)sender;

				if (command == "PausePlay")
				{
					StopPlayback();
				}
				else
				if (command == "ResumePlay")
				{
					ResumePlayback();
				}
				else
				if (command == "EnableZoom")
				{
					//If you want to support zoom buttons
					Control.Settings.BuiltInZoomControls = true;
					Control.Settings.SetSupportZoom(true);
					Control.Settings.UseWideViewPort = true;
					Control.Settings.LoadWithOverviewMode = true;
				}
				else
				if (command == "DisableZoom")
				{
					//If you want to support zoom buttons
					Control.Settings.BuiltInZoomControls = false;
					Control.Settings.SetSupportZoom(false);
					Control.Settings.UseWideViewPort = false;
					Control.Settings.LoadWithOverviewMode = false;
				}

			}


		}
 

		public class JSBridge : Java.Lang.Object
		{
			readonly WeakReference<CustomFbWebViewRenderer> hybridWebViewRenderer;

			public JSBridge(CustomFbWebViewRenderer hybridRenderer)
			{
				hybridWebViewRenderer = new WeakReference<CustomFbWebViewRenderer>(hybridRenderer);
			}

			[JavascriptInterface]
			[Export("invokeAction")]
			public void InvokeAction(string data)
			{
				CustomFbWebViewRenderer hybridRenderer;

				if (hybridWebViewRenderer != null && hybridWebViewRenderer.TryGetTarget(out hybridRenderer))
				{
					// todo https://docs.microsoft.com/en-us/xamarin/xamarin-forms/app-fundamentals/custom-renderer/hybridwebview
					//    ((WebView)hybridRenderer.Element).InvokeAction(data);
				}
			}
		}

 
	}




}
