﻿using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;


namespace AppoMobi.Droid.Renderers
{
    public class MyScrollViewRenderer : ScrollViewHandler
    {
        protected override void ConnectHandler(MauiScrollView platformView)
        {
            base.ConnectHandler(platformView);

            Update();
        }

        public override void PlatformArrange(Rect frame)
        {
            Update();

            base.PlatformArrange(frame);
        }

        void Update()
        {
            Control.HorizontalScrollBarEnabled = false;
            Control.VerticalScrollBarEnabled = false;
        }

        MauiScrollView Control => PlatformView as MauiScrollView;

    }
}

