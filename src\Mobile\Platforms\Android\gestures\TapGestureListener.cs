using Android.Runtime;
using Android.Views;
using Java.Lang;
using AppoMobi.Touch;
using AppoMobi.Touch.Droid.EventArgs;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Xamarin.Forms;
using Microsoft.Maui.Platform;
using Animation = Android.Views.Animations.Animation;
using Boolean = Java.Lang.Boolean;
using Color = Android.Graphics.Color;
using Console = System.Console;
using Debug = System.Diagnostics.Debug;
using Element = Android.Renderscripts.Element;
using Exception = System.Exception;
using Image = Android.Media.Image;
using Math = System.Math;
 
using RelativeLayout = Android.Widget.RelativeLayout;
using Semaphore = Java.Util.Concurrent.Semaphore;
 
using SizeF = Android.Util.SizeF;
using Stream = Android.Media.Stream;
using StringBuilder = Java.Lang.StringBuilder;



namespace AppoMobi.Touch.Droid
    {
        internal class TapGestureListener : Java.Lang.Object, ITapGestureListener
        {
            private IWithTouch element;

            private Android.Views.View view;

            private IGestureListener listener;

            private int numberOfTaps;

            private CancellationTokenSource cancelTappedRaiser;

            
            internal TapGestureListener(IWithTouch element, Android.Views.View view, IGestureListener listener)
            
            {
                this.element = element;
                this.view = view;
                this.listener = listener;
            }

            internal TapGestureListener(IntPtr handle, JniHandleOwnership ownership) : base(handle, ownership)
            {
            }


 

        
        public bool OnLongPressed(MotionEvent e, bool cancel)
            
            {
                //TapGestureListener.u003cu003ec__DisplayClass10_0 variable = null;
                if (this.element.GestureHandler.HandlesLongPressed)
                {
                    AndroidLongPressEventArgs androidLongPressEventArg = new AndroidLongPressEventArgs(e, this.view, cancel);
                   // Task.Run<bool>(new Func<bool>(variable, () => variable.u003cu003e4__this.listener.OnLongPressed(variable.args)));

                Task.Run(() =>
                {
                    listener.OnLongPressed(androidLongPressEventArg);
                });

            }
                return false;
            }

            
            public bool OnLongPressing(MotionEvent e)
            
            {
                //TapGestureListener.u003cu003ec__DisplayClass8_0 variable = null;
                if (this.element.GestureHandler.HandlesLongPressing)
                {
                    AndroidLongPressEventArgs androidLongPressEventArg = new AndroidLongPressEventArgs(e, this.view, false);
                    //Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnLongPressing(this.args)));
                    Task.Run(() =>
                    {
                        listener.OnLongPressing(androidLongPressEventArg);
                    });
                }
                return false;
            }

 

            
            public bool OnLongPressing(IEnumerable<Point> touches, long duration)
            
            {
                //TapGestureListener.u003cu003ec__DisplayClass9_0 variable = null;
                if (this.element.GestureHandler.HandlesLongPressing)
                {
                    AndroidLongPressEventArgs androidLongPressEventArg 
                        = new AndroidLongPressEventArgs(touches, duration, this.view);
                    //Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnLongPressing(this.args)));
                    Task.Run(() =>
                    {
                        listener.OnLongPressing(androidLongPressEventArg);
                    });
                }
                return false;
            }

            
            public bool OnTapping(MotionEvent e)
            
            {
                if (this.cancelTappedRaiser != null)
                {
                    try
                    {
                        this.cancelTappedRaiser.Cancel(); //cancel previous task
                    }
                    catch
                    {
                    }
                }
                
                this.numberOfTaps++;  // ++++

                AndroidTapEventArgs androidTapEventArg = new AndroidTapEventArgs(e, this.view, this.numberOfTaps);
                if (this.element.GestureHandler.HandlesTapping)
                {
                    Task.Run(() =>
                    {
                        listener.OnTapping(androidTapEventArg);
                    });
                //Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnTapping(this.args)));
                }

            //new task
            cancelTappedRaiser = new CancellationTokenSource();
            Task.Run(async () =>
                {
                    await Task.Delay(AppoMobi.Touch.Settings.MsUntilTapped, cancelTappedRaiser.Token);
                    cancelTappedRaiser = null;
                    numberOfTaps = 0;
                    if (androidTapEventArg.NumberOfTaps == 1 && element.GestureHandler.HandlesTapped)
                    {
                        listener.OnTapped(androidTapEventArg);
                    }
                    else
                    if (androidTapEventArg.NumberOfTaps == 2 && element.GestureHandler.HandlesDoubleTapped)
                    {
                        listener.OnDoubleTapped(androidTapEventArg);
                    }
                }, cancelTappedRaiser.Token);

                return false;
        }



   
        }

       

    }