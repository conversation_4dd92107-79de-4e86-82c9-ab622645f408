﻿using System;
using System.Globalization;
using AppoMobi.Common.Dto;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Tenant;



namespace AppoMobi.Converters
{
    
    public class ThumbnailMediumConverter : IValueConverter
    
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var myThumbnail = BaseMobileDto.GetThumbnailUrl(value?.ToString(), "medium", TenantOptions.TenantKey);
            return myThumbnail;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }


    
    public class BooleanNegationConverter : IValueConverter
    
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return !(bool)value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return !(bool)value;
        }
    }
    
    public class UpperCaseConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value?.ToString().ToUpper();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }


    public class TitleCaseConverter : IValueConverter  
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return ToTitleCase(value?.ToString());
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }


        private string ToTitleCase(string str)
        {
            if (str == null) return null;
            string auxStr = str.ToLower();
            string[] auxArr = auxStr.Split(' ');
            string result = "";
            bool firstWord = true;
            foreach (string word in auxArr)
            {
                if (!firstWord)
                    result += " ";
                else
                    firstWord = false;

                if (string.IsNullOrEmpty(word)) continue;

                result += word.Substring(0, 1).ToUpper() + word.Substring(1, word.Length - 1);
            }

            return result;

        }
    }





    //public class ProductNameConverter : IValueConverter
    //{
    //    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        return " " + value.ToString() + " ";
    //    }

    //    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        return value;
    //    }
    //}
    
    public class ProdDescConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                return new FormattedString
                {
                    Spans =
                    {
                        new Span { Text = value.ToString().Substring(0, 210).Trim() + "...  " },//, ForegroundColor=AppColors.BwDarkest
                        new Span { Text = ResStrings.TapToRead, TextColor = AppColors.BwGreyLight, FontSize = 11},
                    }
                };
                //return value.ToString().Substring(0, 200) + "..  "+ResStrings.TapToRead;
            }
            catch
            {
                return new FormattedString
                {
                    Spans =
                    {
                        new Span { Text = value + "...  " },//, ForegroundColor=AppColors.BwDarkest
                        new Span { Text = ResStrings.TapToRead, TextColor=AppColors.BwGreyLight, FontSize = 11},
                    }
                };
            }
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
    

    
    public class ProdSmallDescConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                return new FormattedString
                {
                    Spans =
                    {
                        new Span { Text = value.ToString().Substring(0, 65).Trim() + "...  " },//, ForegroundColor=AppColors.BwDarkest
                        new Span { Text = ResStrings.TapToRead, TextColor=AppColors.BwGreyLight, FontSize = 9},
                    }
                };
                //return value.ToString().Substring(0, 200) + "..  "+ResStrings.TapToRead;
            }
            catch
            {
                return new FormattedString
                {
                    Spans =
                    {
                        new Span { Text = value + "...  " },//, ForegroundColor=AppColors.BwDarkest
                        new Span { Text = ResStrings.TapToRead, TextColor=AppColors.BwGreyLight, FontSize = 9},
                    }
                };
            }
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
    


    public class ProdPackagingConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var item = (ProductDTO)value;
                string ret="";
                if (!string.IsNullOrEmpty(item.Units))
                    ret = item.Volume + " " + item.Units;
                return ret.ToString();
            }
            catch
            {
                return value;
            }
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}

