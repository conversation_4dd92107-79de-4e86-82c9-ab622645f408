﻿using System;

namespace AppoMobi.Framework.Attributes
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class IndexAttribute : Attribute
    {
        private bool? _isUnique;
        //
        // Summary:
        //     /// Set this property to true to define a unique index. Set this property to
        //     false to define a /// non-unique index. ///
        //
        // Remarks:
        //     /// The value of this property is only relevant if System.ComponentModel.DataAnnotations.Schema.IndexAttribute.IsUniqueConfigured
        //     returns true. /// If System.ComponentModel.DataAnnotations.Schema.IndexAttribute.IsUniqueConfigured
        //     returns false, then the value of this property is meaningless. ///
        public virtual bool IsUnique
        {
            get
            {
                if (_isUnique.HasValue)
                {
                    return _isUnique.Value;
                }
                return false;
            }
            set
            {
                _isUnique = value;
            }
        }
    }
}