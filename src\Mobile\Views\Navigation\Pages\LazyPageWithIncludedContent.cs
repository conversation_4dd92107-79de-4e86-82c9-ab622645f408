﻿using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Pages;
using AppoMobi.Xam;

namespace AppoMobi.Mobile.Views;

public class LazyWrapContentType : LazyView<WrapTabIncludedContent>
{
    private readonly Type _type;
    private readonly bool _fullscreen;
    private readonly string _title;
    private readonly bool _hideNavigation;

    public LazyWrapContentType(System.Type contentType, string title, bool isFullScreen, bool hideNavigation)
    {
        _type = contentType;
        _title = title;
        _fullscreen = isFullScreen;
        _hideNavigation = hideNavigation;
    }

    public override View CreateInnerView()
    {
        var view = base.CreateInnerView() as WrapTabIncludedContent;
        view.BackgroundColor = BackColors.Page;
        view.UseNavGradient = true;
        view.IsFullScreen = _fullscreen;
        view.NavigationVisible = !_hideNavigation;

        view.Title = _title;
        //view.AlwaysShowMenu = true;
        //view.ContentType = _type;
        //view.Title = title;

        try
        {
            view.ReplaceContent(_type);
        }
        catch (Exception e)
        {
            var wrap = new ContentView();
            Super.DisplayException(wrap, e);
            view.InsertContent = wrap;
        }

        return view;
    }

}

public class LazyPageWithIncludedContent<T> : LazyView<ViewEnhancedNav> where T : View
{
    public override View CreateInnerView()
    {
        var view = base.CreateInnerView() as ViewEnhancedNav;

        try
        {
            var includedContent = (IView)Activator.CreateInstance(typeof(T), view);
            view.InsertContent = includedContent;
        }
        catch (Exception e)
        {
            var wrap = new ContentView();
            Super.DisplayException(wrap, e);
            view.InsertContent = wrap;
        }

        return view;
    }

}
