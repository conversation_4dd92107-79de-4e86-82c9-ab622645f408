﻿using System;
using AppoMobi.Maui.Navigation;
using AppoMobi.Xam;
using AppoMobi.Touch;



namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OverlayNavBarIconed
    {
        public OverlayNavBarIconed()
        {
            InitializeComponent();
            Initialize();
        }
        /// <summary>
        /// You have to call it manually when device orientation changes
        /// </summary>
        public void Update(DeviceRotation Orientation)
        {

            float cornerRadious = 6;

            cBackgroundFrame.CornerRadius = cornerRadious;

            double marginMain = 0.25;
//            Margin =new Thickness(, 0.25, 0.25, 0.25);


            if (Settings.Current.iModel == "iPhone X")//iphoneX
            {
                if (Orientation == DeviceRotation.Landscape)
                {
                    double marginX = 0;
                    Margin = new Thickness(3 + marginMain, 0 + marginMain, marginMain, marginMain);
                }
                else
                {
                    double marginX = 12.0;
                    Margin = new Thickness(3 + marginMain, 20 + marginX + marginMain*2, marginMain, marginMain);
                }
            }
            else
            {
                //HeightRequest = AppUI.NavBarHeight;
                Margin = new Thickness(5 +marginMain, Super.StatusBarHeight + 5, marginMain, marginMain);
                //cTitleBarMain.HeightRequest = AppUI.NavBarHeight;
            }


        }

        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
           

        }

        public FontIconLabel MainIcon
        {
            get
            {
                return LeftIcon1Symbol;
            }
            protected set
            {
                //nean
            }
        }


        

        private double _ScreenWidth;
        public double ScreenWidth
        {
            get
            {
                return Width;
                //return _ScreenWidth;
            }
            set
            {
                if (_ScreenWidth != value)
                {
                    _ScreenWidth = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _IsPopupSearchVisible;
        public bool IsPopupSearchVisible
        {
            get { return _IsPopupSearchVisible; }
            set
            {
                if (_IsPopupSearchVisible != value)
                {
                    _IsPopupSearchVisible = value;
                    OnPropertyChanged();
                }
            }
        }

        private const double popupSearchOffset = 16 + 23 + 16;
        public uint PopupOptionsTimeIn = 300;
        public uint PopupOptionsTimeOut = 300;
        public uint PopupShadowTime = 100;


        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {

                //property changed
             
                case nameLeftIcon1Source:
                    IconSort.Source = LeftIcon1Source;
                    break;

                case nameLeftIcon2Source:
                    LeftIcon2.Source = LeftIcon2Source;
                    break;


            }

        }
        #region Properties

        
        // LeftIcon2Source
        
        private const string nameLeftIcon2Source = "LeftIcon2Source";
        public static readonly BindableProperty LeftIcon2SourceProperty = BindableProperty.Create(nameLeftIcon2Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon2Source
        {
            get { return (ImageSource)GetValue(LeftIcon2SourceProperty); }
            set { SetValue(LeftIcon2SourceProperty, value); }
        }


        
        // LeftIcon1Source
        
        private const string nameLeftIcon1Source = "LeftIcon1Source";
        public static readonly BindableProperty LeftIcon1SourceProperty = BindableProperty.Create(nameLeftIcon1Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon1Source
        {
            get { return (ImageSource)GetValue(LeftIcon1SourceProperty); }
            set { SetValue(LeftIcon1SourceProperty, value); }
        }

        //-------------------------------------------------------------
        // LeftIcon1Symbol
        //-------------------------------------------------------------
        private const string nameLeftIcon1Symbol = "LeftIcon1Symbol";
        public static readonly BindableProperty LeftIcon1SymbolProperty = BindableProperty.Create(nameLeftIcon1Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel LeftIcon1Symbol
        {
            get { return LeftIcon1txt; }
        }

        #endregion

        #region TouchHandlers
        public event EventHandler OnDoubleTapped = null;

        public event EventHandler OnDownLeftIcon1 = null;
        private void OnDown_LeftIcon1(object sender, DownUpEventArgs e)
        {
            OnDownLeftIcon1?.Invoke(this, e);
        }
        public event EventHandler OnDownLeftIcon2 = null;
        private void OnDown_LeftIcon2(object sender, DownUpEventArgs e)
        {
            OnDownLeftIcon2?.Invoke(this, e);
        }

        #endregion

    }



}