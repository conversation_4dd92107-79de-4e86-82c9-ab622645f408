﻿<?xml version="1.0" encoding="UTF-8" ?>
<touch:LegacyGesturesStackLayout
    x:Class="AppoMobi.UI.OverlayNavBarIconed"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:maui="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:touch="clr-namespace:AppoMobi.Touch"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:transformations1="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HeightRequest="50"
    HorizontalOptions="StartAndExpand"
    Spacing="0"
    VerticalOptions="Start"
    WidthRequest="50">

    <!--  MAIN NON-SCROLING SWIP CONTAINER  -->
    <StackLayout
        HorizontalOptions="Start"
        Orientation="Horizontal"
        Spacing="0"
        VerticalOptions="FillAndExpand">

        <!--  main layout - title and icons  -->
        <Grid
            x:Name="cTitleBarMain"
            Grid.Column="0"
            Padding="0"
            IsVisible="True">

            <Frame
                x:Name="cBackgroundFrame"
                Padding="0"
                BackgroundColor="{x:Static appoMobi:AppColors.OverlayBtns}"
                BorderColor="Transparent"
                CornerRadius="6"
                HasShadow="False"
                HeightRequest="47"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                WidthRequest="47" />


            <!--  icon LEFT 1  -->
            <maui:CachedImage
                x:Name="IconSort"
                Margin="9.5,0,8,0"
                HeightRequest="28"
                HorizontalOptions="Center"
                LoadingPriority="Highest"
                VerticalOptions="Center"
                WidthRequest="28">
                <maui:CachedImage.Transformations>
                    <transformations1:TintTransformation EnableSolidColor="True" HexColor="{x:Static appoMobi:AppColors.icons}" />
                </maui:CachedImage.Transformations>
            </maui:CachedImage>

            <!--  icon with font  -->
            <xam:FontIconLabel
                x:Name="LeftIcon1txt"
                FontSize="{x:Static xam:FontSizes.NavbarIcon}"
                HorizontalOptions="Center"
                TextColor="{x:Static appoMobi:AppColors.Icons}"
                VerticalOptions="Center" />

            <!--  icon LEFT 1 hotspot  -->
            <touch:LegacyGesturesBoxView
                BackgroundColor="Transparent"
                Down="OnDown_LeftIcon1"
                HorizontalOptions="Center"
                VerticalOptions="Fill"
                WidthRequest="55" />

        </Grid>


        <Grid Padding="0" IsVisible="False">

            <!--  icon LEFT 2  -->
            <maui:CachedImage
                x:Name="LeftIcon2"
                Margin="48,0,8,0"
                HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                HorizontalOptions="Start"
                IsVisible="False"
                VerticalOptions="Center"
                WidthRequest="{x:Static appoMobi:AppUI.NavbarIconSize}">
                <maui:CachedImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{x:Static appoMobi:AppColors.icons}" />
                </maui:CachedImage.Transformations>
            </maui:CachedImage>

            <!--  icon LEFT 2 hotspot  -->
            <touch:LegacyGesturesBoxView
                Margin="45,0,0,0"
                BackgroundColor="Transparent"
                Down="OnDown_LeftIcon2"
                HorizontalOptions="Start"
                IsVisible="False"
                VerticalOptions="Fill"
                WidthRequest="30" />





        </Grid>

    </StackLayout>


</touch:LegacyGesturesStackLayout>



