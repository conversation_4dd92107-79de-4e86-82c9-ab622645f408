﻿using System;
using System.Globalization;


namespace AppoMobi.Forms.Framework.Xaml
{
    public class ValueOverZeroConverter : ConverterBase, IValueConverter
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            const bool success = true;

            if (value is int)
            {
                if ((int)value > 0) return success;
            }
            if (value is long)
            {
                if ((long)value > 0) return success;
            }
            if (value is double)
            {
                if ((double)value > 0) return success;
            }

            return !success;
        }

    }



}