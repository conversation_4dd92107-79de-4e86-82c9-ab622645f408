﻿using System.Collections.Generic;
using System.IO;

//#if BACKEND
//using Microsoft.Azure.Mobile.Server;
//#elif MOBILE
//
//#endif

namespace AppoMobi.Common.Dto
{
    public class ReportJsonDto
    {
        public string Context { get; set; }
        public string Json { get; set; }
    }


    //===================================================================
    public class JsonExportList
    //===================================================================
    {
        public IEnumerable<object> list { get; set; }
        public string status { get; set; }
        public string code { get; set; }
        public int count { get; set; }
        public double version { get; set; }
        public double export { get; set; }
        public string key { get; set; }
    }

    //===================================================================
    public class JsonExportA
    //===================================================================
    {
        public IEnumerable<object> list { get; set; }

        public string status { get; set; }
        public string code { get; set; }
        public int count { get; set; }
        public double version { get; set; }
        public double export { get; set; }
    }


    //***************************************************************************
    public class BaseMobileDto
    //***************************************************************************
    {
        public string Id { get; set; } //hello UID = tag
        public string Name { get; set; }
        public string Description { get; set; }

        
        public static string GetThumbnailUrl(string imageId, string sizeType, string tenantKey)
        
        {
            if (string.IsNullOrEmpty(imageId)) return null;
            var basePath = Constants.Constants.ServerApp + string.Format(Constants.Constants.ThumbnailsFolderMask, tenantKey) + Constants.Constants.ThumbsSubPath + @"/";
            var ext= Path.GetExtension(imageId);
            var fileName = Path.GetFileNameWithoutExtension(imageId) + "-" + sizeType + ext;
            return basePath + fileName;
        }     

    }
    //================================================
    public static class DtoExtensions
    //================================================
    {
        
        public static string GetThumbnailForDTO(this string imageId, string sizeType, string tenantKey)
        
        {
            if (string.IsNullOrEmpty(imageId)) return null;
            var basePath = Constants.Constants.ServerApp + string.Format(Constants.Constants.ThumbnailsFolderMask, tenantKey) + Constants.Constants.ThumbsSubPath + @"/";
            var ext = Path.GetExtension(imageId);
            var fileName = Path.GetFileNameWithoutExtension(imageId) + "-" + sizeType + ext;
            return basePath + fileName;
        }
    }

    //    public interface IBaseDataObject
    //    {
    //        string Id { get; set; }
    //    }
    //#if BACKEND
    //        public class BaseDataObject : EntityData
    //        {
    //            public BaseDataObject ()
    //            {
    //                Id = Guid.NewGuid().ToString();
    //            }

    //            public string RemoteId { get; set; }
    //        }
    //#else
    //    public class BaseDataObject : ObservableObject, IBaseDataObject
    //    {
    //        public BaseDataObject()
    //        {
    //            Id = Guid.NewGuid().ToString();
    //        }

    //        public string RemoteId { get; set; }

    //        [Newtonsoft.Json.JsonProperty("Id")]
    //        public string Id { get; set; }

    //        [Microsoft.WindowsAzure.MobileServices.Version]
    //        public string AzureVersion { get; set; }
    //    }
    //#endif


}

