﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
 

namespace Appomobi.Animations
{
    using System.Threading.Tasks;
    

    //***************************************************************
    public class AnimationBlock
    //***************************************************************
    {
        public static readonly double _sideCoeff = 0.55;
        public readonly Easing _easing = new Easing((x) => (x - 1) * (x - 1) * ((_sideCoeff + 1) * (x - 1) + _sideCoeff) + 1);
        private readonly Random r = new Random();

        public  int XTranslation = 300;
        public int YTranslation = 50;
        public int AnimationDurationMs = 350;
        public float FirstAnimationOffsetS = .025f;
        public float DelayPerRowS = .05f;

        public int _itemCounter;
        public DateTimeOffset? _firstAnimationTime;
        public ItemEntranceKind _entranceKind;
    }

    public class CollectionViewIndex
    {
        public static CollectionViewIndex For(int itemIndex, int span)
            => new CollectionViewIndex
            {
                ItemIndex = itemIndex,
                RowIndex = itemIndex / span,
                ColIndex = (itemIndex) % span
            };

        public int ItemIndex { get; set; }
        public int RowIndex { get; set; }
        public int ColIndex { get; set; }
    }

    //***************************************************************
    public static class AnimationExtension
    //***************************************************************
    {

        // hack city 2.0

        public static async Task Animate(this View view, ItemEntranceKind animation)
        {

            var index = new CollectionViewIndex();
            var block = new AnimationBlock();
            block._entranceKind = animation;

            try
            {
                var now = DateTimeOffset.Now;
                block._firstAnimationTime = block._firstAnimationTime ?? now.AddSeconds(block.FirstAnimationOffsetS);

                if (block._entranceKind == ItemEntranceKind.None)
                {
                    view.Opacity = 1;
                    return;
                }

                var translateY = true;
                var timeOffsetForItem = TimeSpan.FromSeconds(index.RowIndex * block.DelayPerRowS);
                var expectedTimeForItem = block._firstAnimationTime.Value.Add(timeOffsetForItem);
                var timeToWait = expectedTimeForItem - now;

                if (timeToWait < TimeSpan.Zero)
                {
                    timeToWait = TimeSpan.FromSeconds(block.FirstAnimationOffsetS);
                    translateY = false; // essentially, 'dont ytranslate things that werent on the screen when we started'
                }

                var translationX = index.ColIndex == 1 ? block.XTranslation : -block.XTranslation;
                var translationY = (index.RowIndex + 1) * block.YTranslation;

                switch (block._entranceKind)
                {
                    case ItemEntranceKind.StaggeredFadeUp:
                        view.TranslationY = translateY ? translationY : 0;
                        break;

                    case ItemEntranceKind.StaggeredFadeDown:
                        view.TranslationY = translateY ? -translationY : 0;
                        break;

                    case ItemEntranceKind.XTranslate:
                        view.TranslationX = translationX;
                        break;

                    case ItemEntranceKind.CrissCross:
                        view.TranslationX = -translationX;
                        break;
                }

                Task.Delay(timeToWait)
                    .ContinueWith(_ =>
                    {
                        if (block._entranceKind.IsOneOf(ItemEntranceKind.CrissCross, ItemEntranceKind.XTranslate))
                            view.Opacity = 1; // dont fade these ones;

                        view.TranslateTo(0, 0, (uint)block.AnimationDurationMs, block._easing);
                        view.FadeTo(1, (uint)block.AnimationDurationMs);
                    });
            }
            catch
            {
                return;
            }
        }

        public static async Task Animate(this View view, CollectionViewIndex index, AnimationBlock block)
        {
            try
            {
                var now = DateTimeOffset.Now;
                block._firstAnimationTime = block._firstAnimationTime ?? now.AddSeconds(block.FirstAnimationOffsetS);

                if (block._entranceKind == ItemEntranceKind.None)
                {
                    view.Opacity = 1;
                    return;
                }

                var translateY = true;
                var timeOffsetForItem = TimeSpan.FromSeconds(index.RowIndex * block.DelayPerRowS);
                var expectedTimeForItem = block._firstAnimationTime.Value.Add(timeOffsetForItem);
                var timeToWait = expectedTimeForItem - now;

                if (timeToWait < TimeSpan.Zero)
                {
                    timeToWait = TimeSpan.FromSeconds(block.FirstAnimationOffsetS);
                    translateY = false; // essentially, 'dont ytranslate things that werent on the screen when we started'
                }

                var translationX = index.ColIndex == 1 ? block.XTranslation : -block.XTranslation;
                var translationY = (index.RowIndex + 1) * block.YTranslation;

                switch (block._entranceKind)
                {
                    case ItemEntranceKind.StaggeredFadeUp:
                        view.TranslationY = translateY ? translationY : 0;
                        break;

                    case ItemEntranceKind.StaggeredFadeDown:
                        view.TranslationY = translateY ? -translationY : 0;
                        break;

                    case ItemEntranceKind.XTranslate:
                        view.TranslationX = translationX;
                        break;

                    case ItemEntranceKind.CrissCross:
                        view.TranslationX = -translationX;
                        break;
                }

                Task.Delay(timeToWait)
                    .ContinueWith(_ =>
                    {
                        if (block._entranceKind.IsOneOf(ItemEntranceKind.CrissCross, ItemEntranceKind.XTranslate))
                            view.Opacity = 1; // dont fade these ones;

                    view.TranslateTo(0, 0, (uint)block.AnimationDurationMs, block._easing);
                        view.FadeTo(1, (uint)block.AnimationDurationMs);
                    });
            }
            catch
            {
                return;
            }
        }
    }


    //***************************************************************
    public static class MiscExtensions
    //***************************************************************
    {
        private static readonly string[] IllegalAssetCharacters = { " ", "-", "&" };

        public static string ToSafeAssetName(this string s)
        {
            foreach (var c in IllegalAssetCharacters)
                s = s.Replace(c, "");

            return s;
        }

        public static ObservableCollection<T> ToObservableCollection<T>(this IEnumerable<T> items)
            => new ObservableCollection<T>(items);

        public static HashSet<T> ToSet<T>(this IEnumerable<T> items)
            => new HashSet<T>(items);

        public static bool IsOneOf<TEnum>(this TEnum item, params TEnum[] comparison)
            where TEnum : Enum
        {
            foreach (var x in comparison)
                if (Equals(item, x))
                    return true;

            return false;
        }
    }


}