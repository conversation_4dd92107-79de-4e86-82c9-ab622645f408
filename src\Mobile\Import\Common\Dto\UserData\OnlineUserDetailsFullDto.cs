﻿using System.Collections.Generic;
using AppoMobi.Common.Dto.Finance;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class OnlineUserDetailsFullDto : OnlineUserDetailsDto
    {
        public OnlineUserDetailsFullDto()
        {
            Wallet = new CustomerFinanceDto();
            Friends=new List<OnlineUserDetailsDto>();
        }


        public CustomerFinanceDto Wallet { get; set; }

        public FriendshipDirection FriendDirection { get; set; }

        public FriendshipStatus Friendship { get; set; }

        public string FriendshipKey { get; set; }

        public string LastName { get; set; }

        public string AboutMe { get; set; }

        public string CityKey { get; set; }

        public string CountryCode { get; set; }

        public string CountryDesc { get; set; }

        public string PhoneNumber { get; set; }
        

        //public string Email { get; set; }

        //manual
        public List<OnlineUserDetailsDto> Friends { get; set; }

        //for friends
        public string Tel { get; set; }
        

        public List<OnlineUserDetailsDto> Contacts { get; set; }
        
        public string PhoneCountry { get; set; }
    }
}