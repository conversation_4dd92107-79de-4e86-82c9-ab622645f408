﻿using System;
using Android.Graphics;
using Android.Media;
using Java.Nio;
using Image = Android.Media.Image;
using ImageFormat = Android.Graphics.ImageFormat;
using Rect = Android.Graphics.Rect;

namespace AppoMobi.Droid.Camera.Legacy
{
    public static class CameraTools
    {

        public static byte[] YUV_420_888toNV21(Image image)
        {

            int width = image.Width;
            int height = image.Height;
            int ySize = width * height;
            int uvSize = width * height / 4;

            byte[] nv21 = new byte[ySize + uvSize * 2];

            ByteBuffer yBuffer = image.GetPlanes()[0].Buffer; // Y
            ByteBuffer uBuffer = image.GetPlanes()[1].Buffer; // U
            ByteBuffer vBuffer = image.GetPlanes()[2].Buffer; // V

            int rowStride = image.GetPlanes()[0].RowStride;

            //1 pixel must take 1 byte
            if (image.GetPlanes()[0].PixelStride != 1)
                return null;

            int pos = 0;

            // ** Y **
            if (rowStride == width)
            { 
                // likely
                yBuffer.Get(nv21, 0, ySize);
                pos += ySize;
            }
            else
            {
                //why the f this hapened
                long yBufferPos = width - rowStride; // not an actual position
                for (; pos < ySize; pos += width)
                {
                    yBufferPos += rowStride - width;
                    yBuffer.Position((int)yBufferPos);
                    yBuffer.Get(nv21, pos, width);
                }
            }

            // ** U **
            rowStride = image.GetPlanes()[2].RowStride;
            int pixelStride = image.GetPlanes()[2].PixelStride;

            //assert(rowStride == image.getPlanes()[1].getRowStride());

            //assert(pixelStride == image.getPlanes()[1].getPixelStride());

            if (pixelStride == 2 && rowStride == width && uBuffer.Get(0) == vBuffer.Get(1))
            {
                // maybe V an U planes overlap as per NV21, which means vBuffer[1] is alias of uBuffer[0]
                var savePixel = vBuffer.Get(1);
                try
                {
                    vBuffer.Put(1, savePixel);
                    if (uBuffer.Get(0) == savePixel)
                    {
                        vBuffer.Put(1, savePixel);
                        vBuffer.Get(nv21, ySize, uvSize);

                        return nv21; // shortcut
                    }
                }
                catch (Exception ex)
                {
                    // unfortunately, we cannot check if vBuffer and uBuffer overlap
                }

                // unfortunately, the check failed. We must save U and V pixel by pixel
                vBuffer.Put(1, savePixel);
            }

            // other optimizations could check if (pixelStride == 1) or (pixelStride == 2), 
            // but performance gain would be less significant

            //THIS IS SLOW in c#
            for (int row = 0; row < height / 2; row++)
            {
                for (int col = 0; col < width / 2; col++)
                {
                    int vuPos = col * pixelStride + row * rowStride;
                    nv21[pos++] = (byte)vBuffer.Get(vuPos);
                    nv21[pos++] = (byte)uBuffer.Get(vuPos);
                }
            }

            return nv21;
        }

        public static byte[] ToByteArray(this ByteBuffer buffer)
        {
            buffer.Rewind();
            var length = buffer.Remaining();
            byte[] data = new byte[length];
            buffer.Get(data);
            return data;
        }

        public static byte[] generateNV21Data(Image image)
        {
            Rect crop = image.CropRect;
            int format = (int)image.Format;
            int width = crop.Width();
            int height = crop.Height();
            Image.Plane[] planes = image.GetPlanes();
            byte[] data = new byte[width * height * ImageFormat.GetBitsPerPixel(image.Format) / 8];
            byte[] rowData = new byte[planes[0].RowStride];
            int channelOffset = 0;
            int outputStride = 1;
            for (int i = 0; i < planes.Length; i++)
            {
                switch (i)
                {
                    case 0:
                        channelOffset = 0;
                        outputStride = 1;
                        break;
                    case 1:
                        channelOffset = width * height + 1;
                        outputStride = 2;
                        break;
                    case 2:
                        channelOffset = width * height;
                        outputStride = 2;
                        break;
                }
                ByteBuffer buffer = planes[i].Buffer;
                int rowStride = planes[i].RowStride;
                int pixelStride = planes[i].PixelStride;
                int shift = i == 0 ? 0 : 1;
                int w = width >> shift;
                int h = height >> shift;
                buffer.Position(rowStride * (crop.Top >> shift) + pixelStride * (crop.Left >> shift));
                for (int row = 0; row < h; row++)
                {
                    int length;
                    if (pixelStride == 1 && outputStride == 1)
                    {
                        length = w;
                        buffer.Get(data, channelOffset, length);
                        channelOffset += length;
                    }
                    else
                    {
                        length = (w - 1) * pixelStride + 1;
                        buffer.Get(rowData, 0, length);
                        for (int col = 0; col < w; col++)
                        {
                            data[channelOffset] = rowData[col * pixelStride];
                            channelOffset += outputStride;
                        }
                    }
                    if (row < h - 1)
                    {
                        buffer.Position(buffer.Position() + rowStride - length);
                    }
                }
            }
            return data;
        }
    }

}