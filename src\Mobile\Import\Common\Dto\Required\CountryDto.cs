﻿using AppoMobi.Framework.Api;

namespace AppoMobi.Common.Dto.Required
{
    public class CountryDto : BaseFrameworkDto
    {

        public string Title { get; set; }

        public string TitleNative { get; set; }

        public string ImageColor { get; set; }

        public int Priority { get; set; }

        //public bool IsDefault { get; set; }

        //public string DefaultCityId { get; set; }

        //public string DefaultCityDesc { get; set; }

        public string TelCode { get; set; }

        public bool LoginEnabled { get; set; }

        public string Banner { get; set; }

        public string TelDisplayFormat { get; set; }

        //public string TelLimit { get; set; }

    }
}