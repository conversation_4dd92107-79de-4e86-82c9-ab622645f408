using Android.Views;
using AppoMobi.Touch;
using System;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidTapEventArgs : TapEventArgs
	{
		public AndroidTapEventArgs(MotionEvent tap, Android.Views.View view, int numberOfTaps)
		{
			this.Cancelled = tap.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(tap);
			this.NumberOfTaps = numberOfTaps;
		}
	}
}