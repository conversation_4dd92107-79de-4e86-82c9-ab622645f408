﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class CleanTextConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                if (!string.IsNullOrEmpty((string)value))
                    return ((string)value).Trim().TrimEnd('\r', '\n');
            }
            else
            {
                return value;
            }
            return value;
        }
    }
}