﻿using AppoMobi.Framework.Api;
using AppoMobi.Common.Enums.System;
using Newtonsoft.Json;

namespace AppoMobi.Common.Dto.System
{
    public class ServerNotificationDto : BaseFrameworkDto
    {
        public string Group { get; set; }

        public string Sub { get; set; }

        [JsonProperty("item")]
        public string ItemKey { get; set; }

        public RemoteContentChangedMode Mode { get; set; }
    }

    //public class ServerNotificationFullDto : ServerNotificationDto
    //{

    //    public string Title { get; set; }

    //    public string Avatar { get; set; }

    //    public object Dto { get; set; }
    //}
}