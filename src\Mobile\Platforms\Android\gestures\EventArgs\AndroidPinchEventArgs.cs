using Android.Views;
using AppoMobi.Touch;
using System;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidPinchEventArgs : PinchEventArgs
	{
		public AndroidPinchEventArgs(MotionEvent current, PinchEventArgs previous, Android.Views.View view)
		{
			this.Cancelled = current.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(current, 2, previous);
			base.CalculateScales(previous);
		}
	}
}