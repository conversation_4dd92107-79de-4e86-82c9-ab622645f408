﻿<?xml version="1.0" encoding="UTF-8"?>
<ScrollView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xam="clr-namespace:AppoMobi.Xam"

             VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"

            x:Name="MainScroll" 

            Scrolled="OnScrolled_ScrollView"

             x:Class="AppoMobi.Xam.ScrollingDataStack">

    <StackLayout 
        x:Name="ControlStackLayout"
        Spacing="0" VerticalOptions="FillAndExpand">

        <!--todo list header-->

        <xam:NiftyDataStack
            FinishedDrawing="OnFinishedDrawing_DataStack"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        x:Name="ControlDataStack"/>

        <!--todo list footer-->

    </StackLayout>

</ScrollView>