﻿using Android.Content;
using Android.Graphics;
using AppoMobi.Xam;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;
using View = Android.Views.View;

namespace AppoMobi.Droid
{
    public class NiftyCellRenderer : VisualElementRenderer<ContentView>
    {
        
        public NiftyCellRenderer(Context context) : base(context)
        {

        }

        protected NiftyCell FormsControl { get; private set; }
        
        protected void Init()
        {

        }
        
        protected override void Dispose(bool disposing)
        
        {
            try
            {
                FormsControl.RendererCommand -= OnRendererCommand;
            }
            catch (Exception e)
            {
            }

            base.Dispose(disposing);
        }

        
        private void OnRendererCommand(object sender, EventArgs e)
        
        {
            


        }

        public bool Rendered { get; private set; }
        //protected override void OnDraw(Canvas canvas)
        //{
        //    base.OnDraw(canvas);
        //    FormsControl.OnRendered();
        //    //if (!Rendered)
        //    //{
        //    //    Rendered = true;
        //    //    FormsControl.OnRendered();
        //    //}
        //}
        /// <summary>
        /// We try not to render the cell when the binding context is not set. Can override this with ShouldRenderCheck. This is also calling parent OnRendered event after rendering.
        /// </summary>
        /// <param name="canvas"></param>
        /// <param name="child"></param>
        /// <param name="drawingTime"></param>
        /// <returns></returns>
        
        protected override bool DrawChild(Canvas canvas, View? child, long drawingTime)
        
        {            
            if (FormsControl != null)
            {
                if (!FormsControl.ShouldRender) return false;
               // if (NativeView?.Height < 1) return true;

                var ret = base.DrawChild(canvas, child, drawingTime);
                FormsControl?.OnRendered();
                return ret;
            }
            return base.DrawChild(canvas, child, drawingTime);
        }


        
        protected override void OnDraw(Canvas canvas)
        
        {
            if (FormsControl != null)
            {
                if (!FormsControl.ShouldRender) return;
            }
            base.OnDraw(canvas);
        }

        //protected View NativeView { get; set; }
        /*
        protected override void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "Height")
            {
                NativeView = FormsControl.GetNativeView();
                if (NativeView != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[NIFTYCEL] Height: {FormsControl.Height} / {NativeView.Height}");
                }
                else
                    System.Diagnostics.Debug.WriteLine($"[NIFTYCEL] Height: {FormsControl.Height}");
            }
            //else if (e.PropertyName == "Aspect")
            //{
            //    UpdateAspect();
            //}
            //else
                base.OnElementPropertyChanged(sender, e);
        }
        */

        
        protected override void OnElementChanged(ElementChangedEventArgs<ContentView> e)
        
        {
            base.OnElementChanged(e);

            if (Element == null) return;
            if (e.OldElement != null)
            {
                var OldControl = (NiftyCell)e.OldElement;
                OldControl.RendererCommand -= OnRendererCommand;
                return;
            }
            this.FormsControl = (NiftyCell)this.Element;
            FormsControl.RendererCommand += OnRendererCommand;

            Init();
        }
    }
}
