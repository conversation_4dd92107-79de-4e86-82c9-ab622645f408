﻿using System;
using System.Collections.Generic;
using AppoMobi.Common.Dto.UserData;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class CustomerInfoFromClient // from client-side
    {
        //can change on client side
        public string FirstName { get; set; } //can change
        
        public string MiddleName { get; set; } //can change
        
        public string LastName { get; set; } //can change

        public DateTime? BirthDate { get; set; } //can change
        
        public string Email { get; set; } //can change
        
        public string ImageUrl { get; set; } //can change
        
        public string CountryCode { get; set; }
        
        public string MosqueCode { get; set; }

        public GenderKind? Gender { get; set; }

        /// <summary>
        /// Unused actually
        /// </summary>
        public string WishRole { get; set; }

        //public string AboutMe { get; set; }

        public string Id { get; set; }
 
        public string City { get; set; }

        //ID docs
        public string AttachUploads { get; set; }

        //avatar
        public string AttachBanner { get; set; }

        public List<DocumentUpdateDto> AttachDocuments { get; set; }

        /// <summary>
        /// Native spoken languages
        /// </summary>
        public string Languages { get; set; }

    }

   

}