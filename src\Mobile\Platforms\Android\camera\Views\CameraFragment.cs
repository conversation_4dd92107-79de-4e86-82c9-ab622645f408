﻿using Android.App;
using Android.Content;
using Android.Content.Res;
using Android.Graphics;
using Android.Hardware;
using Android.Hardware.Camera2;
using Android.Hardware.Camera2.Params;
using Android.Media;
using Android.OS;
using Android.Provider;
using Android.Renderscripts;
using Android.Util;
using Android.Views;
using Android.Widget;
using AppoMobi.Droid.Camera.Models;
using AppoMobi.Droid.Camera.Models.Splines;
using AppoMobi.Droid.Camera.Services;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Content.Camera.Controls;
using AppoMobi.Forms.Content.Camera.Models;
using Java.IO;
using Java.Lang;
using Java.Util;
using Java.Util.Concurrent;
using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Timers;
using AppoMobi.Maui.Native.Droid.Graphics;
using Microsoft.Maui.Controls.Compatibility;
using Boolean = Java.Lang.Boolean;
using Console = System.Console;
using Debug = System.Diagnostics.Debug;
using Element = Android.Renderscripts.Element;
using Exception = System.Exception;
using Image = Android.Media.Image;
using Math = System.Math;
using Point = Android.Graphics.Point;
using RelativeLayout = Android.Widget.RelativeLayout;
using Semaphore = Java.Util.Concurrent.Semaphore;
using Size = Android.Util.Size;
using SizeF = Android.Util.SizeF;
using Stream = Android.Media.Stream;
using StringBuilder = Java.Lang.StringBuilder;
using View = Android.Views.View;


namespace AppoMobi.Droid.Camera.Views
{


    public partial class CameraFragment : AndroidX.Fragment.App.Fragment,
        TextureView.ISurfaceTextureListener, ImageReader.IOnImageAvailableListener, INotifyPropertyChanged
    {
        public CameraFragment()
        {

        }

        public void SetVirtualControl(CameraPreview formsControl)
        {
            FormsControl = formsControl;
        }

        protected CameraPreview FormsControl { get; set; }


        private static readonly SparseIntArray ORIENTATIONS = new SparseIntArray();
        public static readonly int REQUEST_CAMERA_PERMISSION = 1;
        private static readonly string FRAGMENT_DIALOG = "dialog";

        // Tag for the {@link Log}.
        private static readonly string TAG = "Camera2BasicFragment";

        // Camera state: Showing camera preview.
        public const int STATE_PREVIEW = 0;

        // Camera state: Waiting for the focus to be locked.
        public const int STATE_WAITING_LOCK = 1;

        // Camera state: Waiting for the exposure to be precapture state.
        public const int STATE_WAITING_PRECAPTURE = 2;

        //Camera state: Waiting for the exposure state to be something other than precapture.
        public const int STATE_WAITING_NON_PRECAPTURE = 3;

        // Camera state: Picture was taken.
        public const int STATE_PICTURE_TAKEN = 4;

        // Max preview width that is guaranteed by Camera2 API
        private static readonly int MAX_PREVIEW_WIDTH = 2000;

        // Max preview height that is guaranteed by Camera2 API
        private static readonly int MAX_PREVIEW_HEIGHT = 2000;


        // ID of the current {@link CameraDevice}.
        private string mCameraId;

        // An AutoFitTextureView for camera preview
        private AutoFitTextureView mTextureView;

        // A {@link CameraCaptureSession } for camera preview.
        public CameraCaptureSession CaptureSession;

        // A reference to the opened CameraDevice
        public CameraDevice mCameraDevice;

        // The size of the camera preview
        private Size mPreviewSize;

        private Size mRotatedPreviewSize;

        // CameraDevice.StateListener is called when a CameraDevice changes its state
        private CameraStateListener mStateCallback;

        // An additional thread for running tasks that shouldn't block the UI.
        private HandlerThread mBackgroundThread;

        // A {@link Handler} for running tasks in the background.
        public Handler mBackgroundHandler;

        // An {@link ImageReader} that handles still image capture.
        private ImageReader mImageReaderPreview;

        private ImageReader mImageReaderPhoto;

        //{@link CaptureRequest.Builder} for the camera preview
        public CaptureRequest.Builder mPreviewRequestBuilder;

        // {@link CaptureRequest} generated by {@link #mPreviewRequestBuilder}
        public CaptureRequest mPreviewRequest;

        // The current state of camera state for taking pictures.
        public int mState = STATE_PREVIEW;

        // A {@link Semaphore} to prevent the app from exiting before closing the camera.
        public Semaphore mCameraOpenCloseLock = new Semaphore(1);

        // Whether the current camera device supports Flash or not.
        private bool mFlashSupported;

        // Orientation of the camera sensor
        public int SensorOrientation { get; set; }


        // A {@link CameraCaptureSession.CaptureCallback} that handles events related to JPEG capture.
        public CameraCaptureListener mCaptureCallback;

        // Shows a {@link Toast} on the UI thread.
        public void ShowToast(string text)
        {
            if (Activity != null)
            {
                Activity.RunOnUiThread(new ShowToastRunnable(Activity.ApplicationContext, text));
            }
        }

        /// <summary>
        /// Given choices of Sizes supported by a camera, choose the smallest one that
        /// is at least as large as the respective texture view size, and that is at most as large as the
        /// respective max size, and whose aspect ratio matches with the specified value.If such size
        /// doesn't exist, choose the largest one that is at most as large as the respective max size,
        /// and whose aspect ratio matches with the specified value.
        /// </summary>
        /// <param name="choices">The list of sizes that the camera supports for the intended output class</param>
        /// <param name="textureViewWidth">The width of the texture view relative to sensor coordinate</param>
        /// <param name="textureViewHeight">The height of the texture view relative to sensor coordinate</param>
        /// <param name="maxWidth">The maximum width that can be chosen</param>
        /// <param name="maxHeight">The maximum height that can be chosen</param>
        /// <param name="aspectRatio">The aspect ratio</param>
        /// <returns>The optimal Size, or an arbitrary one if none were big enough</returns>
        private static Size ChooseOptimalSize(Size[] choices, int textureViewWidth,
            int textureViewHeight, int maxWidth, int maxHeight, Size aspectRatio)
        {
            // Collect the supported resolutions that are at least as big as the preview Surface
            List<Size> bigEnough = new List<Size>();
            // Collect the supported resolutions that are smaller than the preview Surface
            List<Size> notBigEnough = new List<Size>();
            int w = aspectRatio.Width;
            int h = aspectRatio.Height;
            foreach (Size option in choices)
            {
                if (option.Width <= maxWidth && option.Height <= maxHeight &&
                        option.Height == option.Width * h / w)
                {
                    if (option.Width >= textureViewWidth &&
                        option.Height >= textureViewHeight)
                    {
                        bigEnough.Add(option);
                    }
                    else
                    {
                        notBigEnough.Add(option);
                    }
                }
            }

            // Pick the smallest of those big enough. If there is no one big enough, pick the
            // largest of those not big enough.
            if (bigEnough.Count > 0)
            {
                return (Size)Collections.Min(bigEnough, new CompareSizesByArea());
            }
            else if (notBigEnough.Count > 0)
            {
                return (Size)Collections.Max(notBigEnough, new CompareSizesByArea());
            }
            else
            {
                Log.Error(TAG, "Couldn't find any suitable preview size");
                return choices[0];
            }
        }



        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            //todo get from forms control
            //PresetViewport.Init(App.ViewFinderData.GetPresetData("35"));

            Olistener = new MyOrientationListener(this, SensorDelay.Normal);
            if (Olistener.CanDetectOrientation())
                Olistener.Enable();

            // fill ORIENTATIONS list
            ORIENTATIONS.Append((int)SurfaceOrientation.Rotation0, 90);
            ORIENTATIONS.Append((int)SurfaceOrientation.Rotation90, 0);
            ORIENTATIONS.Append((int)SurfaceOrientation.Rotation180, 270);
            ORIENTATIONS.Append((int)SurfaceOrientation.Rotation270, 180);

            //SetupOutputGalleryFolder();
        }

        private ScaleListener mScaleListener;

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            mTextureView = new AutoFitTextureView(Context, rs);

            //return inflater.Inflate(Resource.Layout.fragment_camera2_basic, container, false);            
            var parameters = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WrapContent,
                RelativeLayout.LayoutParams.WrapContent);

            parameters.AddRule(LayoutRules.CenterInParent, 1);

            mTextureView.LayoutParameters = parameters;

            mTextureView.SurfaceTextureListener = this;

            mScaleListener = new ScaleListener();

            mScaleListener.OnScaleEvent += OnScaleChanged;

            mTextureView.ScaleGestureDetector = new ScaleGestureDetector(Context, mScaleListener);

            return mTextureView;
        }

        public bool ManualZoomEnabled = true;

        private void OnScaleChanged(object sender, EventArgsScale e)
        {
            if (ManualZoomEnabled)
            {
                SetZoom(e.Scale);
            }
        }




        public override void OnViewCreated(View view, Bundle savedInstanceState)
        {
            //mTextureView =  new RTextureView(Context);
            ////view.FindViewById(Resource.Id.picture).SetOnClickListener(this);
            //view.FindViewById(Resource.Id.info).SetOnClickListener(this);
        }

        //protected ScriptIntrinsicLUT _lut;

        public override void OnActivityCreated(Bundle savedInstanceState)
        {
            base.OnActivityCreated(savedInstanceState);

            rs = RenderScript.Create(Context);

            Splines.Initialize(rs);


        }


        /// <summary>
        /// called manually by renderer and forms control
        /// </summary>
        public void Resume()
        {
            //resume fragment..
            //     OnResume();

            if (State == CameraProcessorState.Enabled)
                return;

            if (mTextureView == null)
                return;

            StartBackgroundThread();

            // When the screen is turned off and turned back on, the SurfaceTexture is already
            // available, and "onSurfaceTextureAvailable" will not be called. In that case, we can open
            // a camera and start preview from here (otherwise, we wait until the surface is ready in
            // the SurfaceTextureListener).
            if (mTextureView.IsAvailable)
            {
                OpenCamera(mTextureView.Width, mTextureView.Height);
            }
            else
            {
                mTextureView.SurfaceTextureListener = this;
            }
        }

        //public override void OnResume()
        //{
        //    base.OnResume();


        //}

        /// <summary>
        /// Stop and dispose 
        /// </summary>
        public void Destroy()
        {
            Stop();
            Dispose();
        }

        /// <summary>
        /// Call when inactive to free resources
        /// </summary>
        public void Stop()
        {
            CloseCamera();
        }

        [EditorBrowsable(EditorBrowsableState.Never)]
        public override void OnPause()
        {
            Stop();
            base.OnPause();
        }

        //private void RequestCameraPermission()
        //{
        //    if (FragmentCompat.ShouldShowRequestPermissionRationale(this, Manifest.Permission.Camera))
        //    {
        //        new ConfirmationDialog().Show(ChildFragmentManager, FRAGMENT_DIALOG);
        //    }
        //    else
        //    {
        //        FragmentCompat.RequestPermissions(this, new string[] { Manifest.Permission.Camera },
        //                REQUEST_CAMERA_PERMISSION);
        //    }
        //}

        //public void OnRequestPermissionsResult(int requestCode, string[] permissions, int[] grantResults)
        //{
        //    if (requestCode != REQUEST_CAMERA_PERMISSION)
        //        return;

        //    if (grantResults.Length != 1 || grantResults[0] != (int)Permission.Granted)
        //    {
        //        ErrorDialog.NewInstance(GetString(Resource.String.request_permission))
        //                .Show(ChildFragmentManager, FRAGMENT_DIALOG);
        //    }
        //}




        /*
         EMULATOR

        {
        "Id": "0",
        "Focal": [
          {
            "FocalLength": 5.0,
            "FocalLengthEq": 54.0875
          }SINGLE X
            ],
            "MinDistance": 0.0,
            "SensorWidth": 3.2,
            "SensorHeight": 2.4
          }

        BLACKVIEW

         {
        "Id": "0",
        "Focal": [
          {
            "FocalLength": 3.5,
            "FocalLengthEq": 25.8346043
          }
            ],
            "MinDistance": 20.0,
            "SensorWidth": 4.71,
            "SensorHeight": 3.49
          }

         */

        // Sets up member variables related to camera.
        private void SetupHardware(int width, int height)
        {
            var activity = Activity;
            var manager = (CameraManager)activity.GetSystemService(Context.CameraService);
            try
            {
                //get avalable camreas info
                var BackCameras = new List<CameraUnit>();

                for (var i = 0; i < manager.GetCameraIdList().Length; i++)
                {
                    var cameraId = manager.GetCameraIdList()[i];
                    CameraCharacteristics characteristics = manager.GetCameraCharacteristics(cameraId);

                    #region compatible camera

                    // We don't use a front facing camera
                    var facing = (Integer)characteristics.Get(CameraCharacteristics.LensFacing);
                    if (facing != null && facing == (Integer.ValueOf((int)LensFacing.Front)))
                    {
                        continue;
                    }

                    var map = (StreamConfigurationMap)characteristics.Get(CameraCharacteristics
                        .ScalerStreamConfigurationMap);
                    if (map == null)
                    {
                        continue;
                    }


                    #endregion

                    var focalList = (float[])characteristics.Get(CameraCharacteristics.LensInfoAvailableFocalLengths);
                    var sensorSize = (SizeF)characteristics.Get(CameraCharacteristics.SensorInfoPhysicalSize);

                    var unit = new CameraUnit
                    {
                        Id = cameraId,
                        MinFocalDistance = (float)characteristics.Get(CameraCharacteristics.LensInfoMinimumFocusDistance),
                        //LensDistortion = (???)characteristics.Get(CameraCharacteristics.LensDistortion),
                        SensorHeight = sensorSize.Height,
                        SensorWidth = sensorSize.Width,
                        FocalLengths = new List<float>()
                    };



                    //float w = 0.5f * sensorSize.Width;
                    //float h = 0.5f * sensorSize.Height;

                    foreach (var focalLength in focalList)
                    {
                        unit.FocalLengths.Add(focalLength);

                        //float horizonalAngle = (float)Math.ToDegrees(2 * Math.Atan(w / focalLength));
                        //float verticalAngle = (float)Math.ToDegrees(2 * Math.Atan(h / focalLength));
                        //Debug.WriteLine($"  * horizonalAngle == {horizonalAngle}");
                        //Debug.WriteLine($"  * verticalAngle == {verticalAngle}");
                    }

                    //todo get more info about htis shit
                    unit.FocalLength = unit.FocalLengths[0];

                    BackCameras.Add(unit);

                }


                if (!BackCameras.Any())
                    return;

                //todo choose camera.

                //atm just take nb 1

                var selectedCamera = BackCameras[0];

                bool SetupCamera(CameraUnit cameraUnit)
                {
                    CameraCharacteristics characteristics = manager.GetCameraCharacteristics(cameraUnit.Id);

                    var map = (StreamConfigurationMap)characteristics.Get(CameraCharacteristics.ScalerStreamConfigurationMap);
                    if (map == null)
                    {
                        return false;
                    }

                    SensorOrientation = (int)(Integer)characteristics.Get(CameraCharacteristics.SensorOrientation);

                    Point displaySize = new Point();
                    activity.WindowManager.DefaultDisplay.GetSize(displaySize);

                    //camera width

                    var rotatedPreviewWidth = width;
                    var rotatedPreviewHeight = height;
                    var maxPreviewWidth = displaySize.X;
                    var maxPreviewHeight = displaySize.Y;

                    bool rotated = false;

                    if (SensorOrientation != 0 && SensorOrientation != 270)
                    {
                        rotated = true;
                        rotatedPreviewWidth = height;
                        rotatedPreviewHeight = width;
                        maxPreviewWidth = displaySize.Y;
                        maxPreviewHeight = displaySize.X;
                    }

                    if (maxPreviewWidth > MAX_PREVIEW_WIDTH)
                    {
                        maxPreviewWidth = MAX_PREVIEW_WIDTH;
                    }

                    if (maxPreviewHeight > MAX_PREVIEW_HEIGHT)
                    {
                        maxPreviewHeight = MAX_PREVIEW_HEIGHT;
                    }

                    // For still image captures, we use the largest available size.
                    var stillSizes = map.GetOutputSizes((int)ImageFormatType.Yuv420888).ToList();

                    //                    Size largest = (Size)Collections.Max(stillSizes, new CompareSizesByArea());

                    Size largestW = stillSizes.FirstOrDefault(x => x.Width == stillSizes.Max(m => m.Width));

                    Size largestH = stillSizes.FirstOrDefault(x => x.Height == stillSizes.Max(m => m.Height));


                    //Size largest = (Size)Collections.Max(stillSizes, new CompareSizesByArea());

                    Size largest;

                    //portrait
                    if (rotated)
                    {
                        largest = (Size)Collections.Max(stillSizes.Where(x => x.Width > x.Height).ToList(), new CompareSizesByArea());
                    }
                    else
                    //landscape
                    {
                        largest = (Size)Collections.Max(stillSizes.Where(x => x.Width >= x.Height).ToList(), new CompareSizesByArea()); //todo: untested!!!!
                    }


                    //todo use rotated size


                    // Danger, W.R.! Attempting to use too large a preview size could  exceed the camera
                    // bus' bandwidth limitation, resulting in gorgeous previews but the storage of
                    // garbage capture data.
                    mPreviewSize = ChooseOptimalSize(map.GetOutputSizes(Class.FromType(typeof(SurfaceTexture))),
                        rotatedPreviewWidth, rotatedPreviewHeight, maxPreviewWidth,
                        maxPreviewHeight, largest);


                    //todo set STILL size with looking same ratio

                    //var newStillSize = stillSizes.FirstOrDefault(x => x.Width / x.Height == ratio);
                    //if (newStillSize != null)
                    //    largest = newStillSize;

                    CaptureWidth = largest.Width;
                    CaptureHeight = largest.Height;

                    PreviewWidth = mPreviewSize.Width;
                    PreviewHeight = mPreviewSize.Height;

                    //var captureRatio = CaptureWidth / CaptureHeight;
                    //var ratio = mPreviewSize.Width / mPreviewSize.Height;


                    mImageReaderPhoto = ImageReader.NewInstance(CaptureWidth, CaptureHeight, ImageFormatType.Yuv420888, /*maxImages*/1);
                    mImageReaderPhoto.SetOnImageAvailableListener(mCaptureCallback, mBackgroundHandler);

                    var rotatedSize = mTextureView.SetRotatedContentSize(PreviewWidth, PreviewHeight, SensorOrientation, DisplayMode);


                    //non rotated
                    mRotatedPreviewSize = new Size(PreviewWidth, PreviewHeight);

                    mImageReaderPreview = ImageReader.NewInstance(PreviewWidth, PreviewHeight, ImageFormatType.Yuv420888, 3);

                    mImageReaderPreview.SetOnImageAvailableListener(this, mBackgroundHandler);

                    AllocateOutSurface();

                    // Check if the flash is supported.
                    var available = (Boolean)characteristics.Get(CameraCharacteristics.FlashInfoAvailable);
                    if (available == null)
                    {
                        mFlashSupported = false;
                    }
                    else
                    {
                        mFlashSupported = (bool)available;
                    }

                    mCameraId = cameraUnit.Id;

                    Camera = cameraUnit;

                    FocalLength = (float)(Camera.FocalLength * Camera.SensorCropFactor);

                    //                    ConsoleColor.Green.WriteLineForDebug(ViewFinderData.PrettyJson(PresetViewport));

                    Debug.WriteLine($"[CameraFragment] Cameras:\n {ViewFinderData.PrettyJson(BackCameras)}");

                    return true;
                }

                if (SetupCamera(selectedCamera))
                    return;

                Debug.WriteLine($"[CameraFragment] No outputs!");


            }
            catch (CameraAccessException e)
            {
                e.PrintStackTrace();
            }
            catch (NullPointerException e)
            {
                // Currently an NPE is thrown when the Camera2API is used but not supported on the
                // device this code runs.
                //ErrorDialog.NewInstance(GetString(Resource.String.camera_error)).Show(ChildFragmentManager, FRAGMENT_DIALOG);
            }
        }

        public void SetZoom(float zoom, bool manual = false)
        {
            if (manual)
                mScaleListener.ScaleFactor = zoom;

            ZoomScale = zoom;
        }

        private CameraUnit _camera;
        public CameraUnit Camera
        {
            get { return _camera; }
            set
            {
                if (_camera != value)
                {
                    _camera = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _PreviewWidth;
        public int PreviewWidth
        {
            get { return _PreviewWidth; }
            set
            {
                if (_PreviewWidth != value)
                {
                    _PreviewWidth = value;
                    OnPropertyChanged("PreviewWidth");
                }
            }
        }

        private int _PreviewHeight;
        public int PreviewHeight
        {
            get { return _PreviewHeight; }
            set
            {
                if (_PreviewHeight != value)
                {
                    _PreviewHeight = value;
                    OnPropertyChanged("PreviewHeight");
                }
            }
        }

        private int _CaptureWidth;
        public int CaptureWidth
        {
            get { return _CaptureWidth; }
            set
            {
                if (_CaptureWidth != value)
                {
                    _CaptureWidth = value;
                    OnPropertyChanged("CaptureWidth");
                }
            }
        }

        private int _CaptureHeight;
        public int CaptureHeight
        {
            get { return _CaptureHeight; }
            set
            {
                if (_CaptureHeight != value)
                {
                    _CaptureHeight = value;
                    OnPropertyChanged("CaptureHeight");
                }
            }
        }


        public enum CameraProcessorState
        {
            None,
            Enabled,
            Paused,
            Error
        }

        private CameraProcessorState _state;
        public CameraProcessorState State
        {
            get { return _state; }
            set
            {
                if (_state != value)
                {
                    _state = value;
                    OnPropertyChanged();
                }
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;
        //        public event EventHandler<PropertyChangedEventArgs> PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            var changed = PropertyChanged;
            if (changed == null)
                return;

            changed.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion


        // Opens the camera specified by {@link Camera2BasicFragment#mCameraId}.
        public bool OpenCamera(int width, int height)
        {

            if (State == CameraProcessorState.Enabled)
                return true; //avoid spam

            //if (ContextCompat.CheckSelfPermission(Activity, Manifest.Permission.Camera) != Permission.Granted)
            //{
            //    RequestCameraPermission();
            //    return;
            //}


            //ConfigureTransform(width, height);

            var activity = Activity;
            var manager = (CameraManager)activity.GetSystemService(Context.CameraService);


            try
            {
                if (!mCameraOpenCloseLock.TryAcquire(2500, TimeUnit.Milliseconds))
                {
                    throw new RuntimeException("Time out waiting to lock camera opening.");
                }


                if (mCaptureCallback == null)
                    mCaptureCallback = new CameraCaptureListener(this);

                SetupHardware(width, height);

                if (mStateCallback == null)
                    mStateCallback = new CameraStateListener(this);

                manager.OpenCamera(mCameraId, mStateCallback, mBackgroundHandler);

                _timer = new System.Timers.Timer();
                _timer.Interval = 1000;
                _timer.Elapsed += OnTimerEverySecond;
                _timer.Start();

                State = CameraProcessorState.Enabled;
                Debug.WriteLine($"[CAMERA] {Id} Run");

                return true;
            }
            catch (Exception e)
            {
                //todo something - log errors!
                Console.WriteLine(e);
                //                throw new RuntimeException("Interrupted while trying to lock camera opening.", e);
                return false;
            }
            finally
            {
                mCameraOpenCloseLock.Release();
            }
        }

        public event EventHandler OnImageTaken;
        public event EventHandler OnImageTakingFailed;

        public event EventHandler OnUpdateFPS;

        public event EventHandler OnUpdateOrientation;

        private int _FPS;
        public int FPS
        {
            get { return _FPS; }
            set
            {
                if (_FPS != value)
                {
                    _FPS = value;
                }
            }
        }

        private void OnTimerEverySecond(object sender, ElapsedEventArgs e)
        {
            FPS = countFrames;
            countFrames = 0;
            OnUpdateFPS?.Invoke(null, new EventArgs());
        }

        // Closes the current {@link CameraDevice}.
        public void CloseCamera(bool force = false)
        {
            if (State == CameraProcessorState.None && !force)
                return;

            if (State != CameraProcessorState.Enabled && !force)
                return; //avoid spam

            try
            {
                mCameraOpenCloseLock.Acquire();

                if (null != CaptureSession)
                {
                    CaptureSession.Close();
                    CaptureSession = null;
                }

                if (null != mCameraDevice)
                {
                    mCameraDevice.Close();
                    mCameraDevice = null;
                }

                mStateCallback = null;
                mCaptureCallback = null;

                if (null != mImageReaderPreview)
                {
                    mImageReaderPreview.Close();
                    mImageReaderPreview = null;
                }

                if (null != mImageReaderPhoto)
                {
                    mImageReaderPhoto.Close();
                    mImageReaderPhoto = null;
                }

                if (_timer != null)
                {
                    _timer.Stop();
                    _timer.Elapsed -= OnTimerEverySecond;
                    _timer = null;
                }
                FPS = 0;

                State = CameraProcessorState.None;

                Debug.WriteLine($"[CAMERA] {Id} Stopped");

                StopBackgroundThread();
            }
            catch (Exception e)
            {
                //throw new RuntimeException("Interrupted while trying to lock camera closing.", e);
                Console.WriteLine(e);
            }
            finally
            {
                mCameraOpenCloseLock.Release();
                GC.Collect();
            }
        }

        // Starts a background thread and its {@link Handler}.
        private void StartBackgroundThread()
        {
            mBackgroundThread = new HandlerThread("CameraBackground");
            mBackgroundThread.Start();
            mBackgroundHandler = new Handler(mBackgroundThread.Looper);
        }

        // Stops the background thread and its {@link Handler}.
        private void StopBackgroundThread()
        {
            try
            {
                mBackgroundThread.QuitSafely();
                mBackgroundThread.Join();
                mBackgroundThread = null;
                mBackgroundHandler = null;
            }
            catch (Exception e)
            {
                //e.PrintStackTrace();
            }
        }

        // Creates a new {@link CameraCaptureSession} for camera preview.
        public void CreateCameraPreviewSession()
        {
            try
            {



                // We configure the size of default buffer to be the size of camera preview we want.
                // texture.SetDefaultBufferSize(mPreviewSize.Width, mPreviewSize.Height);

                // This is the output Surface we need to start preview.
                //Surface surface = new Surface(texture);

                // We set up a CaptureRequest.Builder with the output Surface.
                mPreviewRequestBuilder = mCameraDevice.CreateCaptureRequest(CameraTemplate.Preview);
                //mPreviewRequestBuilder.AddTarget(surface);

                //mPreviewRequestBuilder.Set(CaptureRequest.ControlEffectMode, (int)ControlEffectMode.Sepia);



                // Here, we create a CameraCaptureSession for camera preview.
                //List<Surface> surfaces = new List<Surface>();
                ////surfaces.Add(surface);
                //surfaces.Add(mImageReader.Surface);
                //mCameraDevice.CreateCaptureSession(surfaces, new CameraCaptureSessionCallback(this), null);


                mCameraDevice.CreateCaptureSession(
                    new List<Surface> { mImageReaderPreview.Surface, mImageReaderPhoto.Surface },

                    new CameraCaptureSessionCallback(this),
                    mBackgroundHandler);

            }
            catch (CameraAccessException e)
            {
                Console.WriteLine(e);
                Console.WriteLine($"[CAMERA] {Id} Failed to start camera session");

                State = CameraProcessorState.Error;
            }
        }

        public static T Cast<T>(Java.Lang.Object obj) where T : class
        {
            var propertyInfo = obj.GetType().GetProperty("Instance");
            return propertyInfo == null ? null : propertyInfo.GetValue(obj, null) as T;
        }



        public int DeviceOrientation { get; set; }

        public double Orientation { get; set; }

        // Initiate a still image capture.
        public void TakePicture()
        {
            LockFocus();
            CaptureStillPicture();
        }



        // Lock the focus as the first step for a still image capture.
        private void LockFocus()
        {
            //  if (mState == STATE_WAITING_LOCK)
            //      return;

            try
            {
                // This is how to tell the camera to lock focus.

                mPreviewRequestBuilder.Set(CaptureRequest.ControlAfTrigger, (int)ControlAFTrigger.Start);
                // Tell #mCaptureCallback to wait for the lock.
                mState = STATE_WAITING_LOCK;
                CaptureSession.Capture(mPreviewRequestBuilder.Build(), mCaptureCallback,
                        mBackgroundHandler);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        // Run the precapture sequence for capturing a still image. This method should be called when
        // we get a response in {@link #mCaptureCallback} from {@link #lockFocus()}.
        public void RunPrecaptureSequence()
        {
            try
            {
                // This is how to tell the camera to trigger.
                mPreviewRequestBuilder.Set(CaptureRequest.ControlAePrecaptureTrigger, (int)ControlAEPrecaptureTrigger.Start);
                // Tell #mCaptureCallback to wait for the precapture sequence to be set.
                mState = STATE_WAITING_PRECAPTURE;
                CaptureSession.Capture(mPreviewRequestBuilder.Build(), mCaptureCallback, null);
            }
            catch (CameraAccessException e)
            {
                e.PrintStackTrace();
            }
        }

        //private CaptureRequest.Builder stillCaptureBuilder;

        public MediaPlayer MediaPlayer;

        public void PlaySound()
        {
            if (Silent)
                return;

            try
            {
                if (MediaPlayer != null && MediaPlayer.IsPlaying)
                {
                    MediaPlayer.Stop();
                }

                if (MediaPlayer != null)
                {
                    MediaPlayer.Release();
                    MediaPlayer = null;
                }

                if (MediaPlayer == null)
                {
                    MediaPlayer = new MediaPlayer();
                }

                AssetFileDescriptor descriptor = Context.Assets.OpenFd("canond30.mp3");
                MediaPlayer.SetDataSource(descriptor.FileDescriptor, descriptor.StartOffset, descriptor.Length);
                descriptor.Close();
                MediaPlayer.Prepare();
                MediaPlayer.SetVolume(1f, 1f);
                MediaPlayer.Looping = false;
                MediaPlayer.Start();
            }
            catch (Exception e)
            {
                //e.printStackTrace();
            }
        }

        public Stream CapturedStream;

        // Capture a still picture. This method should be called when we get a response in
        // {@link #mCaptureCallback} from both {@link #lockFocus()}.
        public void CaptureStillPicture()
        {
            if (CapturingStill)
                return;

            PlaySound();


            try
            {

                CapturingStill = true;

                var activity = Activity;
                if (null == activity || null == mCameraDevice)
                {
                    OnCaptureError(new ("Cannot attach to activity"));
                    return;
                }

                // This is the CaptureRequest.Builder that we use to take a picture.
                //if (stillCaptureBuilder == null)
                var stillCaptureBuilder = mCameraDevice.CreateCaptureRequest(CameraTemplate.StillCapture);
                stillCaptureBuilder.AddTarget(mImageReaderPhoto.Surface);

                // Use the same AE and AF modes as the preview.
                stillCaptureBuilder.Set(CaptureRequest.ControlAfMode, (int)ControlAFMode.ContinuousPicture);
                SetAutoFlash(stillCaptureBuilder);

                // Orientation
                int rotation = 0; //int)activity.WindowManager.DefaultDisplay.Rotation;
                stillCaptureBuilder.Set(CaptureRequest.JpegOrientation, GetOrientation(rotation));

                CaptureSession.StopRepeating();

                CaptureSession
                    .Capture(stillCaptureBuilder.Build(), new CameraCaptureStillPictureSessionCallback(this),
                        mBackgroundHandler);


            }
            catch (Exception e)
            {
                OnCaptureError(e);
            }
            finally
            {
                //CapturingStill = false;
            }
        }

        public void OnCaptureError(Exception e)
        {
            System.Diagnostics.Trace.WriteLine(e);
            OnImageTakingFailed?.Invoke(this, null);
        }

        public void OnCaptureSuccess()
        {
            OnImageTaken?.Invoke(this, null);
        }

        /*
        private int GetJpegOrientation()
        {
            int sensorOrientation = mRotateTexture;

            var deviceOrientation = 0;

            // Round device orientation to a multiple of 90
            deviceOrientation = (deviceOrientation + 45) / 90 * 90;

            // Reverse device orientation for front-facing cameras
            boolean facingFront = c.get(CameraCharacteristics.LENS_FACING) == CameraCharacteristics.LENS_FACING_FRONT;
            if (facingFront) deviceOrientation = -deviceOrientation;

            // Calculate desired JPEG orientation relative to camera orientation to make
            // the image upright relative to the device orientation
            int jpegOrientation = (sensorOrientation + deviceOrientation + 360) % 360;

            return jpegOrientation;
        }
        */

        // Retrieves the JPEG orientation from the specified screen rotation.
        private int GetOrientation(int rotation)
        {
            // Sensor orientation is 90 for most devices, or 270 for some devices (eg. Nexus 5X)
            // We have to take that into account and rotate JPEG properly.
            // For devices with orientation of 90, we simply return our mapping from ORIENTATIONS.
            // For devices with orientation of 270, we need to rotate the JPEG 180 degrees.
            return (ORIENTATIONS.Get(rotation) + 270) % 360;
        }

        // Unlock the focus. This method should be called when still image capture sequence is
        // finished.
        public void UnlockFocus()
        {
            try
            {
                // Reset the auto-focus trigger
                mPreviewRequestBuilder.Set(CaptureRequest.ControlAfTrigger, (int)ControlAFTrigger.Cancel);
                SetAutoFlash(mPreviewRequestBuilder);
                CaptureSession.Capture(mPreviewRequestBuilder.Build(), mCaptureCallback,
                        mBackgroundHandler);
                // After this, the camera will go back to the normal state of preview.
                mState = STATE_PREVIEW;
                CaptureSession.SetRepeatingRequest(
                    mPreviewRequest,
                    mCaptureCallback,
                     mBackgroundHandler);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        /*
        public void OnClick(View v)
        {
            if (v.Id == Resource.Id.picture)
            {
                TakePicture();
            }
            else if (v.Id == Resource.Id.info)
            {

                EventHandler<DialogClickEventArgs> nullHandler = null;
                Activity activity = Activity;
                if (activity != null)
                {
                    new AlertDialog.Builder(activity)
                        .SetMessage("This sample demonstrates the basic use of the Camera2 API. ...")
                        .SetPositiveButton(Android.Resource.String.Ok, nullHandler)
                        .Show();
                }
            }
        }
        */

        public void SetAutoFlash(CaptureRequest.Builder requestBuilder)
        {
            if (mFlashSupported)
            {
                requestBuilder.Set(CaptureRequest.ControlAeMode, (int)ControlAEMode.OnAutoFlash);
            }
        }

        public RenderScript rs;

        protected Allocation Output { get; set; }

        protected void AllocateOutSurface(bool reset = false)
        {
            if (Output != null && !reset)
                return;

            Debug.WriteLine($"[CAMERA] reallocating surface {mRotatedPreviewSize.Width}x{mRotatedPreviewSize.Height}");

            var oldOutput = Output;

            var output = Allocation.CreateTyped(rs,
                         new Android.Renderscripts.Type.Builder(rs,
                                 Element.RGBA_8888(rs))
                             .SetX(mRotatedPreviewSize.Width)
                             .SetY(mRotatedPreviewSize.Height).Create(),
                         AllocationUsage.IoOutput | AllocationUsage.Script);

            output.Surface = new Surface(mTextureView.SurfaceTexture);

            Output = output;

            if (oldOutput != null)
            {
                oldOutput.Destroy();
                oldOutput.Dispose();
            }

        }

        protected MyOrientationListener Olistener;

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                CloseCamera();

                if (Olistener != null)
                    Olistener.Disable();

                mScaleListener.OnScaleEvent -= OnScaleChanged;

                mScaleListener = null;

                mTextureView.Dispose();
            }

            base.Dispose(disposing);

        }

        public void OnSurfaceTextureAvailable(Android.Graphics.SurfaceTexture surface, int width, int height)
        {
            OpenCamera(width, height);


        }

        public bool OnSurfaceTextureDestroyed(Android.Graphics.SurfaceTexture surface)
        {
            return true;
        }

        public void OnSurfaceTextureSizeChanged(Android.Graphics.SurfaceTexture surface, int width, int height)
        {
            ViewportScale = mTextureView.ViewportScale;
        }


        public void OnSurfaceTextureUpdated(Android.Graphics.SurfaceTexture surface)
        {
            ViewportScale = mTextureView.ViewportScale;
        }


        public SplinesHelper Splines { get; set; } = new SplinesHelper();


        protected int countFrames = 0;


        public bool CapturingStill
        {
            get
            {
                return _capturingStill;
            }

            set
            {
                if (_capturingStill != value)
                {
                    _capturingStill = value;
                    OnPropertyChanged();
                }
            }
        }
        bool _capturingStill;

        private int lockProcessing;

        private List<Image> processing = new List<Image>();

        public void OnImageAvailable(ImageReader reader)
        {
            if (lockProcessing >= reader.MaxImages - 1)
                return;

            Task.Run(async () =>
            {

                Image image = null;

                try
                {
                    if (Output != null || !CapturingStill && mTextureView?.Height > 1)
                    {
                        lockProcessing++;
                        //Console.WriteLine($"Processing UP to {lockProcessing}");

                        image = reader.AcquireLatestImage();

                        if (image != null && !processing.Contains(image))
                        {
                            processing.Add(image);

                            ProcessImage(image, Output, 0);
                            countFrames++;
                        }
                    }

                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
                finally
                {
                    if (image != null)
                    {
                        image.Close();
                        if (processing.Contains(image))
                            processing.Remove(image);
                    }

                    if (lockProcessing > 0)
                    {
                        lockProcessing--;
                        //Console.WriteLine($"Processing down to {lockProcessing}");
                    }
                }

            }).ConfigureAwait(false);




        }



        public void ProcessImage(Image image, Allocation output, int rotation)
        {
            if (Effect == CameraEffect.ColorNegativeAuto)
            {

                if (Splines.Current != null)
                    Rendering.BlitWithLUT(rs, Splines.Renderer, Splines.Current.RendererLUT, image, output, rotation, Gamma);
                else
                    Rendering.TestOutput(rs, output);
            }
            else
            {
                if (Effect == CameraEffect.ColorNegativeManual)
                {
                    Rendering.BlitAdjust(rs, Splines.Renderer, image, output, rotation, Gamma, false, true);
                }
                else
                if (Effect == CameraEffect.GrayscaleNegative)
                {
                    Rendering.BlitAdjust(rs, Splines.Renderer, image, output, rotation, Gamma, true, true);
                }
                else
                if (Effect == CameraEffect.Grayscale)
                {
                    Rendering.BlitAdjust(rs, Splines.Renderer, image, output, rotation, Gamma, true, false);
                }
                else
                {
                    Rendering.BlitAdjust(rs, Splines.Renderer, image, output, rotation, Gamma, false, false);
                    //Rendering.TestOutput(rs, output);
                }
            }

        }

        public bool Silent { get; set; }


        public string CaptureCustomFolder { get; set; }

        public CaptureLocationType CaptureLocation { get; set; }

        public void InsertImageIntoMediaStore(Context context, string imagePath, string imageName)
        {
            ContentValues values = new ContentValues();
            values.Put(MediaStore.Images.Media.InterfaceConsts.Title, imageName);
            values.Put(MediaStore.Images.Media.InterfaceConsts.DisplayName, imageName);
            values.Put(MediaStore.Images.Media.InterfaceConsts.MimeType, "image/jpeg");
            values.Put(MediaStore.Images.Media.InterfaceConsts.Data, imagePath);

            context.ContentResolver.Insert(MediaStore.Images.Media.ExternalContentUri, values);
        }

        public Android.Net.Uri GetMediaStore(Context context, string imagePath, string imageName)
        {
            ContentValues values = new ContentValues();
            values.Put(MediaStore.Images.Media.InterfaceConsts.Title, imageName);
            values.Put(MediaStore.Images.Media.InterfaceConsts.DisplayName, imageName);
            values.Put(MediaStore.Images.Media.InterfaceConsts.MimeType, "image/jpeg");
            values.Put(MediaStore.Images.Media.InterfaceConsts.RelativePath, "ArtOfFoto");
            //values.Put(MediaStore.Images.Media.InterfaceConsts.Data, imagePath);
            return context.ContentResolver.Insert(MediaStore.Images.Media.ExternalContentUri, values);
        }

        public Java.IO.File GetOutputGalleryFolder()
        {
            Java.IO.File jFolder = null;

            var sdk = (int)Android.OS.Build.VERSION.SdkInt;

            if (FormsControl != null)
            {
                if (!string.IsNullOrEmpty(FormsControl.CustomAlbum))
                {
                    jFolder = new Java.IO.File(Android.OS.Environment.ExternalStorageDirectory, FormsControl.CustomAlbum);
                }
            }

            if (jFolder == null)
            {
                jFolder = new Java.IO.File(Android.OS.Environment.GetExternalStoragePublicDirectory(Android.OS.Environment.DirectoryDcim), "Camera");
            }

            if (!jFolder.Exists())
                jFolder.Mkdirs();

            return jFolder;
        }



        private double _SavedRotation;
        public double SavedRotation
        {
            get { return _SavedRotation; }
            set
            {
                if (_SavedRotation != value)
                {
                    _SavedRotation = value;
                    OnPropertyChanged("SavedRotation");
                }
            }
        }


        public string SavedFilename
        {
            get { return _SavedFilename; }
            set
            {
                if (_SavedFilename != value)
                {
                    _SavedFilename = value;
                    OnPropertyChanged("SavedFilename");
                }
            }
        }
        private string _SavedFilename;

        public Action<Bitmap> CapturedImage;

        public bool SaveImageFromYUV(Image image)
        {

            var width = image.Width;
            var height = image.Height;

            if (SensorOrientation == 90 || SensorOrientation == 270)
            {
                height = image.Width;
                width = image.Height;
            }

            var outputBitmap = Bitmap.CreateBitmap(width, height, Bitmap.Config.Argb8888);

            Android.Renderscripts.Allocation outputAllocation = Allocation.CreateFromBitmap(rs,
                outputBitmap,
                Allocation.MipmapControl.MipmapNone,
                AllocationUsage.Script);

            ProcessImage(image, outputAllocation, SensorOrientation);

            outputAllocation.CopyTo(outputBitmap);

            outputAllocation.Destroy();

            //SetupOutputFileFolder();

            CapturedImage?.Invoke(outputBitmap);

            if (CaptureLocation == CaptureLocationType.Bitmap)
            {
                SavedRotation = Orientation;//DeviceOrientation - mSensorOrientation;
                return true;
            }


            Java.IO.File jFolder = GetOutputGalleryFolder();

            if (CaptureLocation == CaptureLocationType.Manual)
            {
                try
                {
                    jFolder = new Java.IO.File(CaptureCustomFolder);
                    if (!jFolder.Exists())
                        jFolder.Mkdirs();
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    ShowToast(e.Message);
                }
            }

            var filename = GenerateJpgFileName();
            var jFile = new Java.IO.File(jFolder, filename);
            var fullFilename = jFile.AbsoluteFile.ToString();

            //using (var output = new System.IO.FileStream(fullFilename, System.IO.FileMode.Create))
            //{
            //    outputBitmap.Compress(Bitmap.CompressFormat.Jpeg, 90, output);
            //    output.Close();
            //}

            try
            {
                using (var output = new System.IO.FileStream(fullFilename, System.IO.FileMode.Create))
                {
                    outputBitmap.Compress(Bitmap.CompressFormat.Jpeg, 90, output);
                    output.Close();
                }

                SavedRotation = Orientation;//DeviceOrientation - mSensorOrientation;
                SavedFilename = fullFilename;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                ShowToast(e.Message);
            }

            switch (DeviceOrientation)
            {
            case 90:
            Camera.Meta.Orientation = 6;
            //newexif.SetAttribute(ExifInterface.TagOrientation, "6");
            break;
            case 270:
            Camera.Meta.Orientation = 8;
            //newexif.SetAttribute(ExifInterface.TagOrientation, "8");
            break;
            case 180:
            Camera.Meta.Orientation = 3;
            //newexif.SetAttribute(ExifInterface.TagOrientation, "3");
            break;
            default:
            this.Camera.Meta.Orientation = 1;
            //newexif.SetAttribute(ExifInterface.TagOrientation, "1");
            break;
            }

            //refresh gallery
            PublishFile(fullFilename);

            return true;
        }

        public static string ConvertCoords(double coord)
        {
            coord = Math.Abs(coord);
            int degree = (int)coord;
            coord *= 60;
            coord -= (degree * 60.0d);
            int minute = (int)coord;
            coord *= 60;
            coord -= (minute * 60.0d);
            int second = (int)(coord * 1000.0d);

            StringBuilder sb = new StringBuilder();
            sb.Append(degree);
            sb.Append("/1,");
            sb.Append(minute);
            sb.Append("/1,");
            sb.Append(second);
            sb.Append("/1000");
            return sb.ToString();
        }

        public void PublishFile(string filename)
        {
            var meta = Camera.Meta;
            if (meta != null)
            {
                var newexif = new ExifInterface(filename);

                newexif.SetAttribute(ExifInterface.TagSoftware, $"{ResStrings.OwnerTitle} Android {App.BuildDesc}");
                newexif.SetAttribute(ExifInterface.TagMake, $"{Android.OS.Build.Manufacturer} {Android.OS.Build.Model}");
                newexif.SetAttribute(ExifInterface.TagModel, $"{ResStrings.OwnerTitle} {ResStrings.Camera}");
                newexif.SetAttribute(ExifInterface.TagOrientation, meta.Orientation.ToString());

                if (FormsControl != null)
                {
                    var latitude = FormsControl.LocationLat;
                    var longitude = FormsControl.LocationLon;

                    if (latitude != 0 && longitude != 0)
                    {
                        newexif.SetAttribute(ExifInterface.TagGpsLatitude, ConvertCoords(latitude));
                        newexif.SetAttribute(ExifInterface.TagGpsLongitude, ConvertCoords(longitude));
                        newexif.SetAttribute(ExifInterface.TagGpsLatitudeRef, latitude > 0 ? "N" : "S");
                        newexif.SetAttribute(ExifInterface.TagGpsLongitudeRef, longitude > 0 ? "E" : "W");
                    }

                    //string latitudeRef = (latitude >= 0) ? "N" : "S";
                    //string longitudeRef = (longitude >= 0) ? "E" : "W";

                    //string exifString = string.Format("{0} {1},{2},{3}/{4},{5},{6} {7}",
                    //	Math.Abs(latitude),
                    //	Math.Abs(latitude - Math.Truncate(latitude)) * 60,
                    //	(Math.Abs(latitude * 3600) % 60).ToString("0.##"),
                    //	latitudeRef,
                    //	Math.Abs(longitude),
                    //	Math.Abs(longitude - Math.Truncate(longitude)) * 60,
                    //	(Math.Abs(longitude * 3600) % 60).ToString("0.##"),
                    //	longitudeRef);

                }

                newexif.SetAttribute(ExifInterface.TagDatetime, DateTime.UtcNow.ToString("o", CultureInfo.InvariantCulture));

                newexif.SaveAttributes();
            }

            Java.IO.File file = new Java.IO.File(filename);
            Android.Net.Uri uri = Android.Net.Uri.FromFile(file);
            Context.SendBroadcast(new Intent(Intent.ActionMediaScannerScanFile, uri));
        }

        private static int filenamesCounter = 0;

        public string GenerateJpgFileName()
        {
            var add = $"{DateTime.Now:MM/dd/yyyy HH:mm:ss}{filenamesCounter}";
            var filename = $"artoffoto-{add.Replace("/", "").Replace(":", "").Replace(" ", "").Replace(",", "").Replace(".", "").Replace("-", "")}.jpg";

            filenamesCounter++;

            return filename;
        }

        public float Gamma { get; set; } = 1.0f;

        private StretchModes _displayMode;
        public StretchModes DisplayMode
        {
            get
            {
                return _displayMode;
            }
            set
            {
                _displayMode = value;
                //todo update!
                mTextureView?.SetDisplayMode(value);
            }
        }

        public float _manualZoom = 1.0f;
        public float _manualZoomCamera = 1.0f;
        public float _minZoom = 0.1f;

        private float _ZoomScale = 1.0f;
        public float ZoomScale
        {
            get { return _ZoomScale; }
            set
            {
                _ZoomScale = value;
                mTextureView?.SetZoomScale(value);

                ZoomScaleTexture = value; //todo use camera zoom

                OnPropertyChanged();
            }
        }


        private float _ZoomScaleTexture = 1.0f;
        public float ZoomScaleTexture
        {
            get { return _ZoomScaleTexture; }
            set
            {
                _ZoomScaleTexture = value;
                OnPropertyChanged();
            }
        }

        private float _ViewportScale = 1.0f;
        public float ViewportScale
        {
            get { return _ViewportScale; }
            set
            {
                _ViewportScale = value;
                OnPropertyChanged();
            }
        }

        private float _focalLength = 0.0f;
        public float FocalLength
        {
            get { return _focalLength; }
            set
            {
                _focalLength = value;
                OnPropertyChanged();
            }
        }



        private System.Timers.Timer _timer;

        private CameraEffect _effect;


        public CameraEffect Effect
        {
            get
            {
                return _effect;
            }
            set
            {
                _effect = value;
                //todo update!
            }
        }







    }
}

