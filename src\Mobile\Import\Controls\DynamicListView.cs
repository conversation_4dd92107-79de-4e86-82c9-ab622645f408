﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace AppoMobi
{
 


    public class DynamicListView : ListView
    {
        public DynamicListView() : base(ListViewCachingStrategy.RecycleElement)
        {
            WatchContent();

        }

        public List<View> Cells = new List<View>();
        public double TrueHeight { get; set; }  = -1;

        private bool _stop { get; set; } = false;


        public async Task Redraw()
        {

            await WatchContent();
        }

        bool _event_busy=false;
        
        public async Task EventOnAppeared()
        
        {
            if (_event_busy) return;
            _event_busy = true;

           {
                await Task.Delay(10);

            }

            while (Cells.Count == 0)
            {
                await Task.Delay(10);
            }


            var hehe = Cells[0];

            while (hehe.Height < 1)
            {
                await Task.Delay(10);
            }

            double MyHeight = 0;
            for (int a = 0; a < Cells.Count; a++)
            {
                MyHeight += Math.Round(Cells[a].Height,0, MidpointRounding.AwayFromZero);
            }

            TrueHeight = MyHeight + 2;
            HeightRequest = TrueHeight;

            _event_busy = false;
        }

        private bool _watch { get; set; } = false;
        
        private async Task WatchContent()
        
        {
            if (_watch) return; //busy
            _watch = true;
            await EventOnAppeared();
            _watch = false;
        }

        
        protected override void SetupContent(Cell content, int index)
        
        {
            base.SetupContent(content, index);

            var currentViewCell = content as ViewCell;

            if (currentViewCell != null && !_stop)
            {
                Cells.Add(currentViewCell.View);
            }

        }



        private bool _measured { get; set; } = false;
        private bool _self { get; set; } = false;
        
        protected override SizeRequest OnMeasure(double widthConstraint, double heightConstraint)
            
        {
            //if (!_self)
            _measured = true;
            return base.OnMeasure(widthConstraint, heightConstraint);
        }

        

        
        protected override void OnSizeAllocated(double width, double height)
            
        {
            base.OnSizeAllocated(width, height);

 

            if (_measured)
            {
                _measured = false;
                _self = true;
                //Cells.Clear(); 
                _stop = true;
                WatchContent();

                //WidthRequest = width + int.Parse(PaddingWidth) * 2;

            }
            else
            {
                _self = false;
            }
        }
    }

    public class AlwaysScrollView : ScrollView
    {
        public AlwaysScrollView()
        {
        }

    }

}
