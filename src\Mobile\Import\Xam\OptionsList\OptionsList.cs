﻿using System.Collections.Generic;
using System.Threading.Tasks;


namespace AppoMobi.Xam
{
    public class OptionsList
    {
        private readonly bool _closeOnChildTapped;

        
        public OptionsList(string title, List<OptionsListItem> list, string cancel = "Cancel", bool closeOnChildTapped=false)
        
        {
            sMessage = title;
            sNo = cancel;
            Items = list;
            _closeOnChildTapped = closeOnChildTapped;
        }



        protected List<OptionsListItem> Items { get; set; }

        private bool bDisableBackgroundClick { get; set; }
        private bool bQuitOnBackPressed { get; set; }

        public void DisableBackgroundClick(bool value = true)
        {
            bDisableBackgroundClick = value;
        }

        public void QuitOnBackPressed(bool value = true)
        {
            bQuitOnBackPressed = value;
        }


        
        private void CallbackAfter(int ret)
        
        {
            bFinished = true;
        }

        public string sNo { get; set; }
        public string sMessage { get; set; }
        public bool bFinished { get; set; } = false;

        
        public async Task ShowAsync(bool showNo = true)
        
        {
            var popup = new PopupOptionsListView(CallbackAfter, sMessage, Items, sNo, bDisableBackgroundClick,
                bQuitOnBackPressed);
            popup.CloseOnChildTapped = _closeOnChildTapped;
            popup.Open();
            while (!bFinished)
            {
                await Task.Delay(100);
            }
            return;
        }



    }
 
}
