﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyIconedText"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:appoMobi="clr-namespace:AppoMobi">

    <ContentView.Content>
        <Grid
            x:Name="ControlContainer"
            Padding="{StaticResource ContactPadding}"
            ColumnSpacing="16"
            HorizontalOptions="FillAndExpand">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <!--  column 0 - TEXT  -->
            <ContentView Grid.Column="0" Padding="0,9,0,9">
                <Label x:Name="ControlLabelText" 
                       TextColor="{x:Static appoMobi:AppColors.BwGrey}"          FontSize="14.5"     />
                <ContentView.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnTapped" />
                </ContentView.GestureRecognizers>
            </ContentView>
            <!--  column 1 - SHARE ICON  -->
            <appoMobi:CImage
                Grid.Column="1"
                DownsampleToViewSize="False"
                HeightRequest="25"
                Source="sharew"
                VerticalOptions="Center"
                WidthRequest="25">
                <appoMobi:CImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_LinkIcon}" />
                </appoMobi:CImage.Transformations>
                <appoMobi:CImage.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnShared" />
                </appoMobi:CImage.GestureRecognizers>
            </appoMobi:CImage>
        </Grid>
    </ContentView.Content>

</ContentView>