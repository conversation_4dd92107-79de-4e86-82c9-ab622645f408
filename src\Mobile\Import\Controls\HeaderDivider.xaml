﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:appoMobi="clr-namespace:AppoMobi"
             x:Class="AppoMobi.HeaderDivider">

    <ContentView.Content>
        <BoxView 
            HorizontalOptions="FillAndExpand" 
            HeightRequest="0.75"
            BackgroundColor="{x:Static appoMobi:AppColors.Divider}">
            <!--<BoxView.HeightRequest>
                <OnPlatform x:TypeArguments="x:Double" iOS=".5" Android="0.75" WinPhone="1"/>
            </BoxView.HeightRequest>-->
        </BoxView>
    </ContentView.Content>

</ContentView>