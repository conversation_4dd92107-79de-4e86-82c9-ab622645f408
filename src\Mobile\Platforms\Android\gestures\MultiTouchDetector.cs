using Android.Content;
using Android.Views;
using System;

namespace AppoMobi.Touch.Droid
{
    //***************************************************************
	internal class MultiTouchDetector
    //***************************************************************
	{

		protected readonly Android.Content.Context Context;

		protected readonly IMultiTouchGestureListener Listener;

		protected readonly int touchSlopSquare;

		private bool gestureInProgress;

		private MotionEvent start;

		public bool IsInProgress
		{
			get
			{
				return this.gestureInProgress;
			}
		}

		private MotionEvent Start
		{
			get
			{
				return this.start;
			}
			set
			{
				MotionEvent motionEvent;
				if (this.start != null)
				{
					this.start.Recycle();
				}
				if (value != null)
				{
					motionEvent = MotionEvent.Obtain(value);
				}
				else
				{
					motionEvent = null;
				}
				this.start = motionEvent;
			}
		}

        
		internal MultiTouchDetector(Android.Content.Context context, IMultiTouchGestureListener listener)
        
		{
			this.Context = context;
			this.Listener = listener;
			this.touchSlopSquare = ViewConfiguration.Get(this.Context).ScaledTouchSlop;
			this.touchSlopSquare *= this.touchSlopSquare;
		}

		private bool EndGesture(MotionEvent e)
		{
			bool flag = false;
			if (this.gestureInProgress)
			{
				flag = this.Listener.OnMoved(e);
			}
			this.Start = null;
			this.gestureInProgress = false;
			return flag;
		}

		public bool OnTouchEvent(MotionEvent e)
		{
			bool flag = false;
			if (e.PointerCount < 2)
			{
				flag = this.EndGesture(e);
			}
			else
			{
				switch (e.ActionMasked)
				{
					case (MotionEventActions)2:
					{
						if (this.Start == null)
						{
							this.Start = e;
						}
						if (!this.gestureInProgress)
						{
							for (int i = 0; i < e.PointerCount; i++)
							{
								int num = this.Start.FindPointerIndex(e.GetPointerId(i));
								if (num >= 0)
								{
									float x = this.Start.GetX(num) - e.GetX(i);
									float y = this.Start.GetY(num) - e.GetY(i);
									if (x * x + y * y > (float)this.touchSlopSquare)
									{
										this.gestureInProgress = true;
									}
								}
							}
						}
						if (!this.gestureInProgress)
						{
							break;
						}
						flag = this.Listener.OnMoving(e);
						break;
					}
					case (MotionEventActions)3:
					case (MotionEventActions)6:
					{
						flag = this.EndGesture(e);
						break;
					}
					case (MotionEventActions)5:
					{
						if (e.PointerCount != 2)
						{
							break;
						}
						this.Start = e;
						break;
					}
				}
			}
			return flag;
		}
	}
}