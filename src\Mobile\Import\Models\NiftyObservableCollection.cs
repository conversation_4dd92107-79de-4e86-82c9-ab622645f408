﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using Newtonsoft.Json;

namespace AppoMobi.Xam
{
    public class NiftyObservableCollection<T> : ObservableRangeCollection<T>
    {

        
        public NiftyObservableCollection()
        
        {
            Tag = "NiftyObservableCollection";
        }
        
        public void SafeClear()
        
        {
            try
            {
                Clear();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);

                int temp = this.Count;
                for (int i = 0; i < temp; i++)
                {
                    this.RemoveAt(0);
                }
            }
        }



        
        public NiftyObservableCollection(IEnumerable<T> items, string tag = null) : this()
        
        {
            this.AddRange(items);
            if (tag == null)
                Tag = "NiftyObservableCollection";
            else
                Tag = tag;
        }


        private string _Tag;
        public string Tag
        {
            get { return _Tag; }
            set
            {
                if (_Tag != value)
                {
                    _Tag = value;
                    OnPropertyChanged(new PropertyChangedEventArgs("Tag"));
                }
            }
        }


        public void NotifySelectedUpdated()
        {
            OnPropertyChanged(new PropertyChangedEventArgs("Selected"));
        }

        private T _Selected;
        public T Selected
        {
            get { return _Selected; }
            set
            {
                if (!EqualityComparer<T>.Default.Equals(_Selected, value))
                {
                    _Selected = value;
                    OnPropertyChanged(new PropertyChangedEventArgs("Selected"));
                }
            }
        }

        public bool SelectionLocked { get; set; }

        private string _Description;
        public string Description
        {
            get { return _Description; }
            set
            {
                if (_Description != value)
                {
                    _Description = value;
                    OnPropertyChanged(new PropertyChangedEventArgs("Description"));
                }
            }
        }

        private string _SelectedDescription;
        public string SelectedDescription
        {
            get { return _SelectedDescription; }
            set
            {
                if (_SelectedDescription != value)
                {
                    _SelectedDescription = value;
                    OnPropertyChanged(new PropertyChangedEventArgs("SelectedDescription"));
                }
            }
        }

        
        public void Reset()
        
        {
            Clear();
            Selected = default(T);
            Refreshed = false;
        }

        /// <summary>
        /// Indicates data need to be visually rebuilt.
        /// </summary>
        public bool Refreshed { get; set; }

        /// <summary>
        /// set this to TRUE after the initial load.
        /// </summary>
        public bool Loaded { get; set; }

        
        public void Set(IEnumerable<T> items)
        
        {
            Clear();
            AddRange(items);
        }
        
        public void ReportItemChange(T item)
        
        {
            NotifyCollectionChangedEventArgs args =
                new NotifyCollectionChangedEventArgs(
                    NotifyCollectionChangedAction.Replace,
                    item,
                    item,
                    IndexOf(item));
            OnCollectionChanged(args);
        }

        
        public void ReportItemChangeAt(int index)
        
        {
            var item = this.ElementAt(index);
            NotifyCollectionChangedEventArgs args =
                new NotifyCollectionChangedEventArgs(
                    NotifyCollectionChangedAction.Replace,
                    item,
                    item,
                    index);
            OnCollectionChanged(args);
        }

        
        public void ForceUpdate()
        
        {

            Items.Clear();

            //            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            //OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Replace, changedItems, changedItems, index));
        }

        
        public void RefreshRange(int index, int quantity)
        
        {
            CheckReentrancy();
            var changedItems = new List<T>();
            for (var currentIndex = index; currentIndex < quantity + index; currentIndex++)
            {
                try
                {
                    changedItems.Add(Items[currentIndex]);
                }
                catch
                {
                    break;
                }
            }
            // OnPropertyChanged(new PropertyChangedEventArgs("Count"));
            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Replace, changedItems, changedItems, index));
        }

        
        public void RefreshRange(int index, IEnumerable<T> collection)
        
        {
            CheckReentrancy();
            var changedItems = collection is List<T> ? (List<T>)collection : new List<T>(collection);

            for (var currentIndex = index; currentIndex < changedItems.Count + index; currentIndex++)
            {
                try
                {
                    var i = changedItems[currentIndex];
                    Items.RemoveAt(currentIndex); //remove
                    Items.Insert(currentIndex, i); //insert new 
                }
                catch
                {
                    break;
                }
            }

            // OnPropertyChanged(new PropertyChangedEventArgs("Count"));
            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Replace, changedItems, changedItems, index));
        }

        
        public void InsertRange(int index, IEnumerable<T> collection)
        
        {
            //NotifyCollectionChangedAction notificationMode = NotifyCollectionChangedAction.Add;

            CheckReentrancy();

            int currentIndex = index;
            var changedItems = collection is List<T> ? (List<T>)collection : new List<T>(collection);
            foreach (var i in changedItems)
            {
                Items.Insert(currentIndex, i);
                currentIndex++;
            }
            OnPropertyChanged(new PropertyChangedEventArgs("Count"));
            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Add, changedItems, index));
        }

        //-------------------------------------------------------------------------------
        public IEnumerable<T> Clone<T>()
        //-------------------------------------------------------------------------------
        {
            var source = this;

            // In the PCL we do not have the BinaryFormatter
            var json = JsonConvert.SerializeObject(source);
            return JsonConvert.DeserializeObject<IEnumerable<T>>(json);
        }
    }

    /// <summary>
    /// Grouping of items by key into ObservableRange
    /// </summary>
    public class Grouping<TKey, TItem> : ObservableRangeCollection<TItem>
    {
        /// <summary>
        /// Gets the key.
        /// </summary>
        /// <value>The key.</value>
        public TKey Key { get; }

        /// <summary>
        /// Returns list of items in the grouping.
        /// </summary>
        public new IList<TItem> Items => base.Items;

        /// <summary>
        /// Initializes a new instance of the Grouping class.
        /// </summary>
        /// <param name="key">Key.</param>
        /// <param name="items">Items.</param>
        public Grouping(TKey key, IEnumerable<TItem> items)
        {
            Key = key;
            AddRange(items);
        }

    }

    /// <summary>
    /// Grouping of items by key into ObservableRange
    /// </summary>
    public class Grouping<TKey, TSubKey, TItem> : ObservableRangeCollection<TItem>
    {
        /// <summary>
        /// Gets the key.
        /// </summary>
        /// <value>The key.</value>
        public TKey Key { get; }

        /// <summary>
        /// Gets the subkey of the grouping
        /// </summary>
        public TSubKey SubKey { get; }

        /// <summary>
        /// Returns list of items in the grouping.
        /// </summary>
        public new IList<TItem> Items => base.Items;

        /// <summary>
        /// Initializes a new instance of the Grouping class.
        /// </summary>
        /// <param name="key">Key.</param>
        /// <param name="subkey">Subkey</param>
        /// <param name="items">Items.</param>
        public Grouping(TKey key, TSubKey subkey, IEnumerable<TItem> items)
        {
            Key = key;
            SubKey = subkey;
            AddRange(items);
        }

    }

 /*
    /// <summary> 
    /// Represents a dynamic data collection that provides notifications when items get added, removed, or when the whole list is refreshed. 
    /// </summary> 
    /// <typeparam name="T"></typeparam> 
    public class ObservableRangeCollection<T> : ObservableCollection<T>
    {

        /// <summary> 
        /// Initializes a new instance of the System.Collections.ObjectModel.ObservableCollection(Of T) class. 
        /// </summary> 
        public ObservableRangeCollection()
            : base()
        {
        }

        /// <summary> 
        /// Initializes a new instance of the System.Collections.ObjectModel.ObservableCollection(Of T) class that contains elements copied from the specified collection. 
        /// </summary> 
        /// <param name="collection">collection: The collection from which the elements are copied.</param> 
        /// <exception cref="System.ArgumentNullException">The collection parameter cannot be null.</exception> 
        public ObservableRangeCollection(IEnumerable<T> collection)
            : base(collection)
        {
        }

        /// <summary> 
        /// Adds the elements of the specified collection to the end of the ObservableCollection(Of T). 
        /// </summary> 
        public void AddRange(IEnumerable<T> collection, NotifyCollectionChangedAction notificationMode = NotifyCollectionChangedAction.Add)
        {
            if (notificationMode != NotifyCollectionChangedAction.Add && notificationMode != NotifyCollectionChangedAction.Reset)
                throw new ArgumentException("Mode must be either Add or Reset for AddRange.", nameof(notificationMode));
            if (collection == null)
                throw new ArgumentNullException(nameof(collection));

            CheckReentrancy();

            if (notificationMode == NotifyCollectionChangedAction.Reset)
            {
                foreach (var i in collection)
                    Items.Add(i);

                OnPropertyChanged(new PropertyChangedEventArgs("Count"));
                OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));

                return;
            }

            int startIndex = Count;
            var changedItems = collection is List<T> ? (List<T>)collection : new List<T>(collection);
            foreach (var i in changedItems)
                Items.Add(i);

            OnPropertyChanged(new PropertyChangedEventArgs("Count"));
            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Add, changedItems, startIndex));
        }

        /// <summary> 
        /// Removes the first occurence of each item in the specified collection from ObservableCollection(Of T). NOTE: with notificationMode = Remove, removed items starting index is not set because items are not guaranteed to be consecutive.
        /// </summary> 
        public void RemoveRange(IEnumerable<T> collection, NotifyCollectionChangedAction notificationMode = NotifyCollectionChangedAction.Reset)
        {
            if (notificationMode != NotifyCollectionChangedAction.Remove && notificationMode != NotifyCollectionChangedAction.Reset)
                throw new ArgumentException("Mode must be either Remove or Reset for RemoveRange.", nameof(notificationMode));
            if (collection == null)
                throw new ArgumentNullException(nameof(collection));

            CheckReentrancy();

            if (notificationMode == NotifyCollectionChangedAction.Reset)
            {

                foreach (var i in collection)
                    Items.Remove(i);

                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));

                return;
            }

            var changedItems = collection is List<T> ? (List<T>)collection : new List<T>(collection);
            for (int i = 0; i < changedItems.Count; i++)
            {
                if (!Items.Remove(changedItems[i]))
                {
                    changedItems.RemoveAt(i); //Can't use a foreach because changedItems is intended to be (carefully) modified
                    i--;
                }
            }

            OnPropertyChanged(new PropertyChangedEventArgs("Count"));
            OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Remove, changedItems, -1));
        }



        /// <summary> 
        /// Clears the current collection and replaces it with the specified item. 
        /// </summary> 
        public void Replace(T item) => ReplaceRange(new T[] { item });

        /// <summary> 
        /// Clears the current collection and replaces it with the specified collection. 
        /// </summary> 
        public void ReplaceRange(IEnumerable<T> collection)
        {
            if (collection == null)
                throw new ArgumentNullException("collection");

            Items.Clear();
            AddRange(collection, NotifyCollectionChangedAction.Reset);
        }

    }
 */
}

