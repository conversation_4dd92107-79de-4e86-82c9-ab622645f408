﻿using System;

namespace AppoMobi.Common.Extensions
{
    public static class DateTimeExtensions
    {
        
        public static DateTime ToTimeZoneTime(this DateTime time, TimeZoneInfo tzi)
        
        {
            return TimeZoneInfo.ConvertTimeFromUtc(time, tzi);
        }
        
        
        public static DateTime ToTimeZoneTime(this DateTime time, string timeZoneId)
        
        {
            TimeZoneInfo tzi;
            try
            {
                tzi = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            }
            catch (TimeZoneNotFoundException)
            {
                tzi = TimeZoneInfo.FindSystemTimeZoneById("Europe/Moscow");
            }
            //TimeZoneInfo tzi = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            return time.ToTimeZoneTime(tzi);
        }

        
        public static DateTime GetLocalTimeNow(string timeZoneId = "Russian Standard Time")
         
        {
            var now = DateTime.UtcNow;
            return now.ToTimeZoneTime(timeZoneId);
        }
        
        public static DateTime GetLocalToday(string timeZoneId = "Russian Standard Time")
        
        {
            var now = DateTimeExtensions.GetLocalTimeNow().Date;
            return now;
        }
    }
}
