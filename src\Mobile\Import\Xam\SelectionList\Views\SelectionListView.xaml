﻿<?xml version="1.0" encoding="utf-8" ?>
<xam:PopupDialogBase
    x:Class="AppoMobi.Xam.SelectionListView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="End"
    Color="{x:Static xam:BackColors.PopupPageBackgroundLight}">

    <!--<pages:PopupPage.Animation>
        <animations:MoveAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"

            PositionIn="Right"
            PositionOut="Right" />
    </pages:PopupPage.Animation>-->

    <ContentPage.Content>

        <!--  POPUP  -->
        <gestures:LegacyGesturesGrid Down="Grid_OnDown" VerticalOptions="FillAndExpand">


            <StackLayout
                Margin="50,20,0,0"
                Spacing="0"
                VerticalOptions="StartAndExpand">
                <!--  HEADER  -->
                <gestures:LegacyGesturesStackLayout
                    BackgroundColor="{x:Static xam:BackColors.PopupHeaderBackground}"
                    InputTransparent="True"
                    Orientation="Horizontal"
                    Spacing="0"
                    VerticalOptions="StartAndExpand">
                    <!--  header text  -->
                    <Label
                        x:Name="cTitle"
                        Margin="20,8,8,8"
                        FontSize="15"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Message}"
                        TextColor="{x:Static xam:BackColors.PopupHeaderText}"
                        VerticalTextAlignment="Center" />
                </gestures:LegacyGesturesStackLayout>

                <xam:NiftyDataStack
                    x:Name="DataStack"
                    Margin="0,0,0,0"
                    BackgroundColor="{x:Static xam:BackColors.PopupListBackground}"
                    ItemsSource="{Binding MenuList}"
                    Spacing="0.5"
                    Tag="Popup"
                    VerticalOptions="Start">
                    <xam:NiftyDataStack.ItemTemplate>
                        <DataTemplate>

                            <gestures:LegacyGesturesStackLayout
                                HorizontalOptions="FillAndExpand"
                                Spacing="0"
                                Tapped="OnTapped_Item"
                                TappedCommandParameter="{Binding .}"
                                VerticalOptions="Start">

                                <!--  List Item  -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <Label
                                        Grid.Column="0"
                                        Margin="12,0,0,0"
                                        FontSize="17"
                                        IsVisible="{Binding Selected}"
                                        Text="&#x2713;"
                                        TextColor="{x:Static xam:BackColors.PopupListText}"
                                        VerticalOptions="Center" />

                                    <Label
                                        Grid.Column="1"
                                        Margin="8,15,4,15"
                                        FontSize="15"
                                        Text="{Binding Title}"
                                        TextColor="{x:Static xam:BackColors.PopupListText}"
                                        VerticalOptions="Center" />
                                </Grid>

                                <!--  divider  -->
                                <BoxView
                                    BackgroundColor="{x:Static xam:BackColors.PopupListDivider}"
                                    HeightRequest="0.5"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="End" />



                            </gestures:LegacyGesturesStackLayout>



                        </DataTemplate>
                    </xam:NiftyDataStack.ItemTemplate>


                </xam:NiftyDataStack>

            </StackLayout>


        </gestures:LegacyGesturesGrid>


    </ContentPage.Content>

</xam:PopupDialogBase>