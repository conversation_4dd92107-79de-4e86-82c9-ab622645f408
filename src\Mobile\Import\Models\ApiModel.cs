﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Tenant;
using Newtonsoft.Json;


namespace AppoMobi.Models
{

    public class InstancedApiBaseModel<T> : ApiModel where T : new()
    {

        #region SELF_CREATE

        //self create
        private static T _self;

        public static T Instance
        {
            get
            {
                if (_self == null)
                {
                    _self = new T();
                    //       App.Instance.Messager.Doctor(typeof(T).Name);
                }
                return _self;
            }
        }
        #endregion



        #region RemoteData



        
        public bool _DataCheckLoaded(Action<bool, object[]> callback, bool mayLoad = true, params object[] parameters)
        
        {
            if (!_DataLoaded)
            {
                //try load
                if (mayLoad)
                    _DataLoad(callback, parameters); //not awaited
                //System.Threading.Tasks.Task.Run( async ()=> await _DataLoad(callback, parameters) ).ConfigureAwait(false);
                return false;
            }
            return true;
        }


        
        public async Task<bool> _DataCheckLoadedAsync(Action<bool, object[]> callback)
        
        {
            if (!_DataLoaded)
            {
                //try load

                return await _DataLoad(callback, null); //not awaited
            }
            return true;
        }
        //-------------------------------------------------------------------
        protected virtual bool OnDataLoadedCheck()
        //-------------------------------------------------------------------
        {
            return true;
        }

        public bool _DataLoading { get; set; }

        
        private bool __DataLoaded;
        public bool _DataLoaded
        {
            get
            {
                if (OnDataLoadedCheck())
                    return __DataLoaded;
                return false;
            }
            protected set
            {
                __DataLoaded = value;
            }
        }
        
        public async Task<bool> _DataLoad(Action<bool, object[]> callback = null, params object[] parameters)
        
        {
            Debug.WriteLine($"[DATA] Called Data for {this.GetType().FullName}");

            if (_DataLoading)
            {
                Debug.WriteLine($"[DATA] *WAITING* loading data for  {this.GetType().FullName}");
                while (_DataLoading)
                {
                    await Task.Delay(100);
                }
                Debug.WriteLine($"[DATA] Loaded, calling callback for  {this.GetType().FullName}");
                callback?.Invoke(_DataLoaded, parameters);
                return _DataLoaded;
            }
            _DataLoading = true;

            Debug.WriteLine($"[DATA] *LOADING* data for  {this.GetType().FullName}");
            var ret = await LoadModelData(parameters);

            _DataLoaded = ret;
            _DataLoading = false;
            Debug.WriteLine($"[DATA] Loaded2, calling callback for  {this.GetType().FullName}");
            callback?.Invoke(ret, parameters);
            return ret;
        }

        /// <summary>
        /// Override this and do not call base to load model data
        /// </summary>
        /// <returns></returns>
        protected virtual async Task<bool> LoadModelData(params object[] parameters)
        {
            return false;
        }


        #endregion

    }


    public class ApiModel : NiftyBaseModel  
    {
        private static int _retries;

        
        protected static HttpClient CreateClient(double timeout=60, int retries=2, bool withCompression=true)
        
        {
            _retries = retries;

            var client = new HttpClient(new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip,
            })
            {
                //BaseAddress = new Uri(baseAddress),
                Timeout = TimeSpan.FromSeconds(timeout),
                DefaultRequestHeaders = { { "User-Agent", "AppoMobi" } },
            };
            if (withCompression)
            {
                client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("gzip"));
                client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("deflate"));
            }
            return client;
        }


        
        protected string BearerToken
        
        {
            get
            {
                string auth = "";
                try
                {
                    auth += "Bearer " + Preferences.Default.Get("id_token", "");
                }
                catch (Exception e)
                {

                }

                return auth;
            }
        }

        
        protected string Lang => Settings.Current.SelectedLang.ToLower();
        

        
        protected string TenantKey => TenantOptions.TenantKey;
        

        
        protected async Task<TResult> Request<TResult>(HttpMethod method, string url, object fromBody, params KeyValuePair<string, string>[] headers) where TResult : new()
         
        {
            TResult ret = default(TResult);
            
            await Task.Delay(10); //unblock UI thread

            var myClient = CreateClient();

            foreach (var header in headers)
            {
                if (string.IsNullOrEmpty(header.Value)) continue;

                myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
                myClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            var retries = _retries;

            while (retries>0)
            {
                try
                {
                    var uri = new Uri(url);
                    var escape = Uri.EscapeDataString(uri.Query);
                    var escape2 = Uri.EscapeUriString(url);
                    var newUri = uri.Scheme + uri.Authority + uri.LocalPath + escape;

                    HttpResponseMessage response;
                    string content;

                    if (method == HttpMethod.Post)
                    {
                        StringContent encodedContent = null;
                        if (fromBody != null)
                        {
                            var send = JsonConvert.SerializeObject(fromBody);
                            encodedContent = new StringContent(send, Encoding.UTF8, "application/json");
                        }
                        response = await myClient.PostAsync(uri, encodedContent);
                        content = response.Content.ReadAsStringAsync().Result;
                    }
                    else
                    if (method == HttpMethod.Get)
                    {
                        response = await myClient.GetAsync(uri);
                        content = await response.Content.ReadAsStringAsync();
                    }
                    else
                    {
                        throw new ApiException($"Unsupported method {method}", method, url, HttpStatusCode.OK);
                    }


                    if (typeof(TResult) == typeof(ApiResponse))
                    {
                        var responseRet = new ApiResponse()
                        {
                            StatusCode = response.StatusCode,
                            Content = content,
                            Url = url,
                            Method = method
                        };
                        return (TResult)(object)responseRet;
                    }

                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        ret = JsonConvert.DeserializeObject<TResult>(content);
                        retries = 0;
                    }
                    else
                    {
                        throw new ApiException("Status code doesn't indicate success", method, url, response.StatusCode);
                    }

                    //return new ModelAuth.HttpResponce
                    //{
                    //    Code = response.StatusCode,
                    //    Headers = response.ToString(),
                    //    Body = contents.ToString(),
                    //    RequestUrl = url,
                    //};
                }
                catch (Exception e)
                {
                    await Core.Current.ProcessApiException(e);
                }

                retries--;
                if (retries > 0) 
                    await Task.Delay(2000); //todo customize timeout
            }


            return ret;


        }


    }
}
