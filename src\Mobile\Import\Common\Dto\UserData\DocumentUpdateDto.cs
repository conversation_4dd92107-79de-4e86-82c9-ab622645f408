﻿using System;

namespace AppoMobi.Common.Dto.UserData
{
    /// <summary>
    /// Send to update or create
    /// </summary>
    public class DocumentUpdateDto
    {
        public string Id { get; set; }

        public string PlayerKey { get; set; }

        public int Type { get; set; }

        public string TypeDesc { get; set; }

        public string Number { get; set; }

        public DateTime? Issued { get; set; }

        public string IssuedBy { get; set; }

        public DateTime? Expires { get; set; }

        /// <summary>
        /// for stats, 0 manual, 1 scan, 2 nfc..
        /// </summary>
        public int InputType { get; set; }

        public string AttachUploads { get; set; }

        public string UserComments { get; set; }

//        public string AdminComments { get; set; }

        public bool Modify { get; set; }
    }
}