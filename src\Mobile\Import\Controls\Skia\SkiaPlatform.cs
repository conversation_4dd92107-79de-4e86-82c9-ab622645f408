﻿using AppoMobi.Forms.Framework;

namespace AppoMobi.Forms.Controls.Skia
{
    public static class SkiaPlatform
    {
        public static float ToDeviceUnits(this float value)
        {
            return value / Core.DisplayDensity;
        }

        public static double ToDeviceUnits(this double value)
        {
            return value / Core.DisplayDensity;
        }

        public static float ToPixels(this float value)
        {
            return value * Core.DisplayDensity;
        }

        public static double ToPixels(this double value)
        {
            return value * Core.DisplayDensity;
        }

    }
}