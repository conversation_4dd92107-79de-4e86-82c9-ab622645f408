﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="AppoMobi.PageColors"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui">
    <ContentPage.Content>
        <StackLayout Padding="16" Spacing="16">

            <Grid ColumnSpacing="0" RowSpacing="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <appoMobi:CImage
                    x:Name="imgClose"
                    Grid.Column="1"
                    Aspect="AspectFit"
                    HeightRequest="32"
                    HorizontalOptions="End"
                    Source="cancel">
                    <appoMobi:CImage.Transformations>
                        <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_MenuIconSelected}" />
                    </appoMobi:CImage.Transformations>
                    <appoMobi:CImage.GestureRecognizers>
                        <TapGestureRecognizer Tapped="Image_OnTapped" />
                    </appoMobi:CImage.GestureRecognizers>
                </appoMobi:CImage>
                <Label
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    HorizontalOptions="Center"
                    Text="AppColors"
                    VerticalOptions="Center" />
            </Grid>
            <ScrollView>
                <Grid x:Name="MyGrid" RowSpacing="4" />
            </ScrollView>
        </StackLayout>
    </ContentPage.Content>
</ContentPage>