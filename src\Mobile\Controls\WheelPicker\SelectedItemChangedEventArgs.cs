﻿namespace DrawnUi.Draw
{
    /// <summary>
    /// Event arguments for selected item changed events
    /// </summary>
    //public class SelectedItemChangedEventArgs : EventArgs
    //{
    //    public object SelectedItem { get; }
    //    public int SelectedIndex { get; }

    //    public SelectedItemChangedEventArgs(object selectedItem, int selectedIndex)
    //    {
    //        SelectedItem = selectedItem;
    //        SelectedIndex = selectedIndex;
    //    }
    //}
}
