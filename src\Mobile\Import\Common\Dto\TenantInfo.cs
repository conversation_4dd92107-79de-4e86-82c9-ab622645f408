﻿using System.Collections.Generic;

namespace AppoMobi.Common.Dto
{
    public class Pair
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class AppStrings
    {
        public string AppTitle { get; set; }
        
        public string PricesMask { get; set; }


        public string AboutCompanyDesc { get; set; }

        public string AppoSelectQuestion1 { get; set; }
        public string AppoSelectQuestion2 { get; set; }
        public string AppoSelectQuestion3 { get; set; }
        public string AppoSelectTitle1 { get; set; }
        public string AppoSelectTitle2 { get; set; }
        public string AppoSelectTitle3 { get; set; }
        public string AppoBookedTitle { get; set; }

        public string AppoSelectingObject { get; set; }
        public string AppoSelectingService { get; set; }



        public string AppoActionTitle { get; set; } //Book Online
        public string AppoActionTitleShort { get; set; } //Book Online
       
        //buttons
        public string AppoActionButton { get; set; } //Book Now!
        public string AppoCancelButton { get; set; } //Cancel Booking

        public string AppoCancelWarning { get; set; } //Cancel are you sure?

        public string NewsTitle { get; set; } //Cancel are you sure?
        public string NewsTitleShort { get; set; } //Cancel are you sure?


        public string MenuKnowledgeBaseTitle { get; set; }
        public string MenuKnowledgeBaseLink { get; set; }
        public string PrivacyLink { get; set; }

        public string RegisterPrivacyWarning { get; set; }

        public string ServicesDesc { get; set; }
        public string ServicesTitle { get; set; }
        public string ServicesTitleShort { get; set; }

        public string GalleryTitle { get; set; }
        public string GalleryTitleShort { get; set; }
        public string GalleryTitleCompany { get; set; } 


    }

    public class TenantInfoDTO
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Subtitle { get; set; }
        public IEnumerable<string> Modules { get; set; }
        public IEnumerable<string> Languages { get; set; }
        public IEnumerable<string> Skins { get; set; }
        public bool ExplicitBookable { get; set; }

        public IEnumerable<CompanyInfoDTO> Companies { get; set; }
        
        public AppStrings Strings { get; set; }

        public string Logo { get; set; }
        public string LogoCoUrl { get; set; }

        //public string PriceMask { get; set; }

        public string Pattern { get; set; }
        public double FadePattern { get; set; }

        public string Wallpaper { get; set; }
        public double FadeWallpaper { get; set; }

        //new
        public string ColorPrimary { get; set; }
        public string ColorAccent { get; set; }
        public string ColorSplash { get; set; }
        public string ColorProgress { get; set; }

        public string FontIcons { get; set; }

        public string FontTitleApple { get; set; }
        public double FontTitleSizeApple { get; set; }
        public string FontTitleGoogle { get; set; }
        public double FontTitleSizeGoogle { get; set; }
    }

    public class TenantSplashDTO
    {
        public string Logo { get; set; }
        public string ColorPrimary { get; set; }
        public string ColorAccent { get; set; }
        public string ColorSplash { get; set; }
        public string ColorProgress { get; set; }
        public IEnumerable<string> Languages { get; set; }
    }
    /*
    1.
    .slim,
.slim img 
        ADD
    max-height: 350px;

    2.
      .slim-result img
    DELETE 
    (in 1 place)
        position: absolute;
        left: 0;
        top: 0; 
    (in second place)
    width: 100%;
        ADD
        display: block;
        margin-left: auto;
        margin-right: auto;




     */

}
