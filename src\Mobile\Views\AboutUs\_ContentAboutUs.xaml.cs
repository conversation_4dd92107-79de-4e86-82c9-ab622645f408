﻿using System;
using System.Collections.Generic;
using AppoMobi.Common.Constants;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Common.Dto;

using AppoMobi.Nifty;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;


namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ContentAboutUs
    {
 

        public List<CMyListItem> ContactList = new List<CMyListItem>();
        public List<CMyListItem> ContactListMap = new List<CMyListItem>();
        public List<CMyListItem> ContactListRes = new List<CMyListItem>();

        public string AppVersion { get; set; }

        private CompanyInfoDTO Model => Core.Current.MyCompany;

        public ContentAboutUs(IPageEnhancedNav daddy)
        {
            InitializeComponent();

            var test =Core.Current.Info;

           BindingContext = Core.Current;

            string prms = null;

            //txtDescription.Text = Core.Current.MyCompany.Description;
            //txtSubLogo.Text = Core.Current.MyCompany.SubLogoText;

            //company logo ?
            //imgLogo.Source = Core.Current.MyCompany.LogoUrl.GetThumbnailForDTO("large", TenantOptions.TenantKey);
            imgLogo.Source = "logoabout.jpg";//resource://AppoMobi.Mobile.Images.Brand.
            // imgSubLogo.Source = Core.Current.MyCompany.SubLogoUrl.GetThumbnailForDTO("normal", TenantOptions.TenantKey);


            prms = Core.Current.MyCompany.ContactUsTel;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "tel",
                    FontIcon = FontIcons.fa_phone_square,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsWebsite;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "url",
                    FontIcon = FontIcons.fa_globe,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsEmail;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "mail",
                    FontIcon = FontIcons.fa_envelope,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsFacebook;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "facebook",
                    FontIcon = FontIcons.fa_facebook, FontOverride = "FaBrands",
                    Desc = ResStrings.Facebook,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsVk;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "vk",
                    FontIcon = FontIcons.fa_vk, FontOverride = "FaBrands",
                    Desc = ResStrings.VK,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsInstagram;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "instagram",
                    FontIcon = FontIcons.fa_instagram, FontOverride = "FaBrands",
                    Desc = ResStrings.Instagram,
                    Parameters = prms,
                    Selected = false
                });
            }


                ContactListMap.AddRange(new[]
                {
                    new CMyListItem { Tag = "map",
                        FontIcon = FontIcons.fa_map_marked_alt,
    //                    FontOverride = "fa",
                        Parameters =Core.Current.MyCompany.ContactUsAddress, Image = "geo",         
                        Desc = Core.Current.MyCompany.ContactUsAddress, Selected = false},
                });

            if (Core.Current.MyCompany.MapX>0)
            {
                ContactListMap.AddRange(new[]
                {
                    new CMyListItem { Tag = "navi",
                        FontIcon = FontIcons.fa_route,
                        //                    FontOverride = "fa",
                        Parameters =Core.Current.MyCompany.ContactUsAddress, 
                        Desc = ResStrings.HowToGet, Selected = false},
                });
            }
            else
            {
                cardMap.IsVisible = false;
            }



            Daddy = daddy;

            //TOOLBAR ICONS

            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsTel))
            {
                Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_phone);
                Daddy.RightIcon1Symbol.RotationY = 180;
                Daddy.ToggleButtonVisibility(ButtonType.Right1, true);
            }
            else
            {
                Daddy.ToggleButtonVisibility(ButtonType.Right1, false);
            }
            /*
            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsAddress))
            {
                Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_location_arrow);
                Daddy.ToggleButtonVisibility(ButtonType.Right2, true);
            }
            else
            {
                Daddy.ToggleButtonVisibility(ButtonType.Right2, false);
            }
            */



            //Daddy.RightIcon1Source = "resource://AppoMobi.Mobile.Images.Navbar.phones.png";
            //Daddy.RightIcon2Source = "resource://AppoMobi.Mobile.Images.Navbar.car.png";

            IconsList.List = ContactList;
            NiftyList.List = ContactList;
            NiftyListMap.List = ContactListMap;
            //NiftyListRes.List = ContactListRes;

            AppVersion = ResStrings.PageSettings_PageSettings_Version + " " +  Core.Native.GetAppVersion();
        }

        
        public override void OnRightIcon1Clicked()
        
        {
            CWorld.Dial(Core.Current.MyCompany.ContactUsTel);

            base.OnRightIcon1Clicked();
        }

        //---------------------------------------------------------
        private async void NiftyList_OnTapped(object sender, EventArgs e)
        //---------------------------------------------------------
        {
            View daddy = null;
            string Params = null;
            string Tag = null;

            if (sender is IconsRow row)
            {
                daddy = (IconsRow) sender;
                Params = row.SelectedParams;
                Tag = row.Tag;
            }
            else
            if (sender is NiftyIconedList list)
            {
                daddy = (NiftyIconedList)sender;
                Params = list.SelectedParams;
                Tag = list.Tag;
            }
            else
            {
                return;
            }


            switch (Tag)
            {
                case "tel":
                    CWorld.Dial(Params);
                    break;

                case "www":
                    await Core.PushInstance(Navigation, typeof(WebpageCustomized), Params, Core.Current.MyCompany.Name, Core.Current.MyCompany.ContactUsTel);
                    break;

                case "url":
                     Core.Native.OpenUrl(Params);
                    break;

                case "facebook": 
                    if (! Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebookNumeric))
                    {
                         Core.Native.OpenUrl(@"https://www.facebook.com/" + Core.Current.MyCompany.ContactUsFacebook);
                    }
                    break;

                case "vk": //todo
                    //if (! Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebook))
                    //{
                         Core.Native.OpenUrl(@"https://www.vk.com/" + Core.Current.MyCompany.ContactUsVk);
                    //}
                    break;

                case "instagram": 
                    if (! Core.Native.OpenInAppInstagram(Core.Current.MyCompany.ContactUsInstagram))
                    {
                        //else open in our browser:
                        Core.Native.OpenUrl(@"https://www.instagram.com/" + Core.Current.MyCompany.ContactUsInstagram);
                       // await Core.PushInstance(Navigation, typeof(Webpage), @"https://www.instagram.com/" + Core.Current.MyCompany.ContactUsInstagram, Core.Current.MyCompany.Name + " " + ResStrings.Instagram, "");
                    }
                    break;

                case "mail":
                    
                     Core.Native.OpenUrl($"mailto:{Params}");
//                    Core.Mail(Params);
                    break;

                case "map":
                    //await Core.PushInstance(Daddy.Navigation, typeof(PageWeOnMap));
                    //                    await Core.PushInstance(Navigation, typeof(PageAboutUsMap));
                    break;


                case "navi":
                    //await NiftyNative.Navigate(Core.Current.MyCompany.Name, Core.Current.MyCompany.MapX, Core.Current.MyCompany.MapY);
                    break;

            }


        }



         private async void ShowMapTapped(object sender, EventArgs e)
        {

           // await Core.PushInstance(Daddy.Navigation, typeof(PageWeOnMap));
            //    await Core.PushInstance(Navigation, typeof(PageAboutUsMap));
        }

        private void On_PartnersLogin(object sender, EventArgs e)
        {
            //  throw new NotImplementedException();
        }

        private bool locklm { get; set; }
        private async void OnBtnLearnMore(object sender, EventArgs e)
        {
            if (locklm) return;
            locklm = true;

            await Core.PushInstance(Navigation, typeof(WebpageCustomized), string.Format(Constants.ServerRedirectLinkMask, TenantOptions.TenantKey, "learnmore"), Model.Name, "");


            locklm = false;
        }


 
    }
}
