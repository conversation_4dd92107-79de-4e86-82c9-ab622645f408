﻿using System;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NiftySimpleButton : ContentView
    {
        public NiftySimpleButton()
        {
            InitializeComponent();
            PaddingWidth = "0";
        }


        
        // Pressed
        
        public event EventHandler Pressed = null;
        private void BtnNifty_OnClicked(object sender, EventArgs e)
        {
            Pressed?.Invoke(this, EventArgs.Empty);
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);
            switch (propertyName)
            {
                case nameCaption:
                    btnNifty.Text = Caption;//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;
                case nameIsOn:
                    if (IsOn)
                    {
                        btnNifty.TextColor = Color.Parse("fafafa");
                    }
                    else
                    {
                        btnNifty.TextColor = Color.Parse("598285");
                    }
                    InputTransparent = !IsOn;
                    break;

            }

        }

   
        private bool _measured { get; set; } = false;
        private bool _self { get; set; } = false;


        
        protected override SizeRequest OnMeasure(double widthConstraint, double heightConstraint)
        
        {
            if (!_self) _measured = true;
            return base.OnMeasure(widthConstraint, heightConstraint);
        }

        
        protected override void OnSizeAllocated(double width, double height)
        
        {
            base.OnSizeAllocated(width, height);

      

            if (_measured)
            {
                _measured = false;
                _self = true;
                var i = Core.Native.GetButtonWidth(Caption);
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    WidthRequest = i + int.Parse(PaddingWidth)*6;
                else
                    WidthRequest = i + int.Parse(PaddingWidth) * 4;

            }
            else
            {
               _self = false;
            }
        }

  
        
        // Caption
        
        private const string nameCaption = "Caption";
        public static readonly BindableProperty CaptionProperty = BindableProperty.Create(nameCaption, typeof(string), typeof(NiftySimpleButton), string.Empty);
        public string Caption
        {
            get { return (string)GetValue(CaptionProperty); }
            set { SetValue(CaptionProperty, value); }
        }

        
        // PaddingWidth
        
        private const string namePaddingWidth = "PaddingWidth";
        public static readonly BindableProperty PaddingWidthProperty = BindableProperty.Create(nameCaption, typeof(string), typeof(NiftySimpleButton), string.Empty);
        public string PaddingWidth
        {
            get { return (string)GetValue(PaddingWidthProperty); }
            set { SetValue(PaddingWidthProperty, value); }
        }
        
        // IsOn
        
        private const string nameIsOn = "IsOn";
        public static readonly BindableProperty IsOnProperty = BindableProperty.Create(nameIsOn, typeof(bool), typeof(NiftySimpleButton), true);
        public bool IsOn
        {
            get { return (bool)GetValue(PaddingWidthProperty); }
            set { SetValue(PaddingWidthProperty, value); }
        }
    }
}