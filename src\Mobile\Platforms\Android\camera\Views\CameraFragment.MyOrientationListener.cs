﻿using System;
using Android.Content;
using Android.Hardware;
using Android.Runtime;
using Android.Views;

namespace AppoMobi.Droid.Camera.Views
{
    public partial class CameraFragment
    {
        public class MyOrientationListener : OrientationEventListener
        {
            private CameraFragment _owner;

            public MyOrientationListener(IntPtr javaReference, JniHandleOwnership transfer) : base(javaReference, transfer)
            {
            }

            public MyOrientationListener(Context context) : base(context)
            {
            }

            public MyOrientationListener(CameraFragment fragment, SensorDelay rate) : base(fragment.Context, rate)
            {
                _owner = fragment;
            }

            public override void OnOrientationChanged(int orientation)
            {

                var rotation = ((orientation + 45) / 90) % 4;

                _owner.DeviceOrientation = rotation * 90;

                _owner.OnUpdateOrientation?.Invoke(null, new EventArgs());

            }
        }
    }
}