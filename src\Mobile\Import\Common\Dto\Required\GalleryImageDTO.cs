﻿using System;
using AppoMobi.Framework.Api;

namespace AppoMobi.Common.Dto.Required
{
    public class UploadedFileDto : BaseFrameworkDto
    {
        public string Filename { get; set; }
    }

    public class UploadedImageDto : UploadedFileDto
    {
        public string Title { get; set; } 

        public string Desc { get; set; }

        public string ImageColor { get; set; }

        public string PlayerKey { get; set; }

        public GalleryImageType Type { get; set; }
    }

public class GalleryImageDTO
        //===================================================================
    {
        public string Id { get; set; } //hello UID
        public string Keywords { get; set; } //NEW
        public string Title { get; set; } //todo
        public string Desc { get; set; }

        /// <summary>
        /// Who uploaded etc
        /// </summary>
        public string SystemDesc { get; set; }
        public string PlayerKey { get; set; }
        public DateTime? CreatedTime { get; set; }

        public bool New { get; set; } //todo
        public int Priority { get; set; } //for news

        public string ShareLink { get; set; } //for sharing!

        public string ImageId { get; set; }
        public string ImageColor { get; set; }

        public GalleryImageType Type { get; set; }

    }
}