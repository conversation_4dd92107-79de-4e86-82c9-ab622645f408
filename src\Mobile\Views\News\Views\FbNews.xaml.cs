﻿
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.Xam;


using NavBar = AppoMobi.UI.NavBar;

namespace AppoMobi.Pages
    {
        [XamlCompilation(XamlCompilationOptions.Compile)]
        public partial class FbNews
        {
            private string _content;

            protected string _url;


            private void ControlBrowser_OnSizeChanged(object sender, EventArgs e)
            {
                if (ControlBrowser.Height > 100)
                {
                    int height = (int)(ControlBrowser.Height * Core.DisplayDensity);
                    //if (newSize < 2000)
                    //    newSize = 2000;

                    var width = (int)Core.ScreenSizePixels.Width;
                    if (width > 500)
                        width = 500; //widget width
                    var zoomAmount = Core.ScreenSizePixels.Width / width;

                    height = (int)(height / zoomAmount);

                    //_url = @"https://m.facebook.com/artoffoto";
                    _url = $"https://www.facebook.com/v9.0/plugins/page.php?height={height}&hide_cover=false&href=https%3A%2F%2Fm.facebook.com%2Fartoffoto&locale=ru_RU&show_facepile=false&small_header=true&tabs=timeline%2C%20events&width={width}";

                    SetSource(ResStrings.X_OurNews, _url, true);
            }
            }

        
        public FbNews()
            
            {
                InitializeComponent();

                ControlBrowser.ZoomEnabled = true;

                Title = ResStrings.X_OurNews;

                if (!TenantOptions.DarkSkin)
                {
                    ControlBrowser.WebViewColor = Colors.White; //todo move this to onappearing for ios
                }


            AllowedUrls = new List<string>
                {
                    @"appomobi.com",
                    //@"facebook.com",
             //       @"about:blank",
                    @"staticxx.facebook.com/connect/xd_arbiter.php",
                    @"/plugins/page.php",
                    @"/AppoMobi.iOS.app/"
               
                };

            //get page from resources
            //var pagePath = $"Resources.Html.fb.html";
            //_content = ReadFromAssembly(pagePath);

            var icon = new FontIconsPreset(FaPro.Rotate, 1.0);
            RightIcon1Symbol.SetIcon(icon);
            ToggleButtonVisibility(ButtonType.Right1, true);

            BindingContext = this;

            //Device.StartTimer(TimeSpan.FromSeconds(3), () =>
            //{
            //    _url = $"https://appomobi.com/artoffoto/news?lang={Settings.Current.SelectedLang}&needEvents=true&height={newSize}&darkSkin={TenantOptions.DarkSkin}";
            //    SetSource(ResStrings.X_OurNews, _url, true);

            //    return false;
            //});

        }



            public List<string> AllowedUrls { get; set; } = new List<string>();


            
            public override void OnRightIcon1Clicked()
            
            {
            //reload page
 
            SetSource(ResStrings.X_OurNews, _url, true);

            base.OnRightIcon1Clicked();
            }

        /// <summary>
        /// Ex: "Json.info.json"
        /// </summary>
        /// <param name="nameAfterAssembly"></param>
        /// <returns></returns>
        
        public string ReadFromAssembly(string nameAfterAssembly)
        
        {
            var assembly = typeof(Core).GetTypeInfo().Assembly;
            Stream stream = assembly.GetManifestResourceStream(assembly.GetName().Name + $".{nameAfterAssembly}");

            if (stream == null)
                return "";
            string json = "";
            using (var reader = new System.IO.StreamReader(stream))
            {
                json = reader.ReadToEnd();
            }
            return json;
        }

        
        public void SetSource(string title, string source, bool isUrl = true)
        
        {
            ShowLoaderOverlay = true;

        //    Title = title;

            if (isUrl)
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "about:blank";
                }
                var url = new UrlWebViewSource
                {
                    Url = source
                };
                
                ControlBrowser.Source = url;
            }
            else
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "";
                }
                var html = new HtmlWebViewSource
                {
                    Html = source
                };
                
                ControlBrowser.Html = source;
                
                ControlBrowser.Source = html;


            }

            //ShowLoaderOverlay = false;

        }


        private void MyBrowser_OnNavigating(object sender, WebNavigatingEventArgs e)
        {
            if (e.Url.ToLowerInvariant().Contains("onrendered"))
            {
                e.Cancel = true;
                var args = new WebNavigatedEventArgs(e.NavigationEvent, e.Source, e.Url, WebNavigationResult.Cancel);
                MyBrowser_OnNavigated(sender, args);
                return;
            }

            var allowed = false;
            foreach (var url in AllowedUrls)
            {
                if (e.Url.Contains(url))
                {
                    allowed = true;
                }
            }

            if (!allowed)
            {
                e.Cancel = true;
                if (e.Url.Contains("facebook.com"))
                {
                    if (!Core.Native.OpenInAppFacebook(e.Url))
                    {
                        Core.Native.OpenUrl(e.Url);
                    }
                }
            }


            //if (!e.Url.Contains("appomobi.com"))
            //{
            //    e.Cancel = true;

            //    if (!Core.Native.OpenInAppFacebook(e.Url))
            //    {
            //        Core.Native.OpenUrl(e.Url);
            //    }

            //}

            ShowTransparentLoader(false);

            var stop = true;
        }
            
            private void MyBrowser_OnNavigated(object sender, WebNavigatedEventArgs e)
            
            {

                if (e.Result == WebNavigationResult.Cancel)
                {
                }
                else
                {

                ShowLoaderOverlay = false;
                
                ControlHide.IsVisible = false;

                    ControlBrowser.Url = e.Url;

                }

         

        }

        private void OnClicked(object sender, XamWebView.ClickEventArgs e)
            {
                

            }

        }
    }
