﻿using System.Linq;
using System.Threading.Tasks;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PageFonts
    {

        
        public PageFonts()
        
        {
            InitializeComponent();



      

            Task.Run(BuildFontList);
        }

        private bool Ready { get; set; }

        
        public override void ContentLoaderCheck(bool stop)
        
        {
            base.ContentLoaderCheck(Ready);
        }

        
        private async Task BuildFontList()
        
        {
         //   var stack = new Microsoft.Maui.Controls.StackLayout();
            var placeholder =Core.Current.Info.Title;
            var fonts = Core.Native.ListAvailableFonts().OrderBy(q => q);
            foreach (var font in fonts)
            {
                var label = new Microsoft.Maui.Controls.Label();
                label.StyleId = "generic";
                label.Text = $"{font} {placeholder}";
                label.FontFamily = font;
                Stack.Children.Add(label);
            }

            Ready = true;

        }



        //
        //protected override bool OnBackButtonPressed()
        //
        //{


        //    if (Navigation.NavigationStack.Count < 3)
        //    {
        //        Globals.Values.Root?.ShowMenu();
        //        //Globals.Values.AppMenu.ClickMenuItem(Constants.CONST_MENU_HOME);
        //        return true;
        //    }
        //    else
        //        return false;
        //    //    MainThread.BeginInvokeOnMainThread(async () => {
        //    //        var result = await this.DisplayAlert("Thalion", "Вы хотите выйти из программы?", "Да", "Нет");
        //    //        if (result) { _canClose = false; base.OnBackButtonPressed();}
        //    //    });
        //    return true;
        //}


    }
}