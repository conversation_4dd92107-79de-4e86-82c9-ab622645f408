﻿using System.ComponentModel;
using AppoMobi.Specials.Localization;
using AppoMobi.Forms.Common.ResX;

namespace AppoMobi.Common.Enums.UserData
{
    [TypeConverter(typeof(LocalizedEnumConverter))]
    [FromResources(Type = typeof(ResStrings))]
    public enum UserRequestResultType
    {
        Unset,

        Pending,

        /// <summary>
        /// Вас выбрали
        /// </summary>
        Approved,

        /// <summary>
        /// Вас отклонили
        /// </summary>
        Rejected,

        Owner

    }
}