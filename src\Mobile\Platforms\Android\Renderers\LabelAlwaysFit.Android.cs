﻿using Android.Content;
using Android.Util;
using Android.Views;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Handlers;
using Paint = Android.Graphics.Paint;
using Rect = Android.Graphics.Rect;
using TextAlignment = Microsoft.Maui.TextAlignment;


namespace AppoMobi.Droid.Renderers
{
    public class HandlerLabelAlwaysFit : LabelHandler
    {
        ShrinkTextOnLayoutChangeListener listener;
        private ShrinkTextOnLayoutChangeListener _listener;

        protected override void ConnectHandler(AppCompatTextView platformView)
        {
            base.ConnectHandler(platformView);

            _listener = new ShrinkTextOnLayoutChangeListener(Control, (Label)VirtualView);

            Control.AddOnLayoutChangeListener(listener = _listener);

            Control.ForceLayout();
        }

        protected override void DisconnectHandler(AppCompatTextView platformView)
        {
            Control.RemoveOnLayoutChangeListener(listener =_listener);

            base.DisconnectHandler(platformView);
        }

        AppCompatTextView Control => PlatformView as AppCompatTextView;

  

        class ShrinkTextOnLayoutChangeListener : Java.Lang.Object, global::Android.Views.View.IOnLayoutChangeListener
        {
            const string textMeasure = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
            const float threshold = 0.5f; // How close we have to be

            readonly TextView textView;
            private readonly Label FormsControl;
            //-----------------------------------------------------------------
            public ShrinkTextOnLayoutChangeListener(TextView textView, Label formsControl)
            //-----------------------------------------------------------------
            {
                this.textView = textView;
                this.FormsControl = formsControl;
            }

            private float _originalSize { get; set; } = 0f;

            //-----------------------------------------------------------------
            public void OnLayoutChange(global::Android.Views.View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom)
            //-----------------------------------------------------------------
            {
                if (textView.Width <= 0 || textView.Height <= 0)
                    return;
                

                var hi = ConvertSpToPixels(textView.TextSize, textView.Context);
                if (_originalSize == 0f) 
                    _originalSize = hi;

                var lo = 1f;

                var paint = new Paint(textView.Paint);
                var bounds = new Rect();

                while ((hi - lo) > threshold)
                {
                    var size = (hi + lo) / 2;
                    paint.TextSize = size;
                    paint.GetTextBounds(textMeasure, 0, textMeasure.Length, bounds);

                    var measure = paint.MeasureText(textView.Text);
                    if (measure >= textView.Width || bounds.Height() >= textView.Height)
                        hi = size; // too big
                    else
                        lo = size; // too small
                }

              //  var check = textView.Text;

                var difference = (_originalSize - lo);
                if (Math.Abs(difference) > threshold)
                {
                    if (difference > 0)
                    {
                        //todo apply MaxLines
                        //lo = _originalSize - difference / Core.DisplayDensity;
                    }
                    else
                        lo = _originalSize;//... no zoom
                }

                if (FormsControl.HorizontalTextAlignment == TextAlignment.Center)
                {
                    textView.Gravity = GravityFlags.Center;
                }
                else
                {
                    textView.Gravity = GravityFlags.CenterVertical;
                }
                textView.SetTextSize(ComplexUnitType.Px, lo);

                //if (textView.Text == "Кто это?")
                //{
                //    var stop = true;
                //}
            }

            static float ConvertSpToPixels(float sp, Context context) => TypedValue.ApplyDimension(ComplexUnitType.Px, sp, context.Resources.DisplayMetrics);
        }
    }
}