﻿using AppoMobi.Touch;


namespace AppoMobi.Forms.Controls
{
    public class Hotspot : AppoMobi.Touch.LegacyGesturesContentView
    {
        public Hotspot()
        {
            Down += OnDown;
            Up += OnUp;
        }



        //-------------------------------------------------------------
        // Reaction
        //-------------------------------------------------------------
        private const string nameReaction = "Reaction";
        public static readonly BindableProperty ReactionProperty = BindableProperty.Create(nameReaction, typeof(HotspotReaction), typeof(Hotspot), HotspotReaction.Minify); //, BindingMode.TwoWay
        public HotspotReaction Reaction
        {
            get { return (HotspotReaction)GetValue(ReactionProperty); }
            set { SetValue(ReactionProperty, value); }
        }




        //-------------------------------------------------------------
        // TintColor
        //-------------------------------------------------------------
        private const string nameTintColor = "TintColor";
        public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(Hotspot), Colors.Transparent); //, BindingMode.TwoWay
        public Color TintColor
        {
            get { return (Color)GetValue(TintColorProperty); }
            set { SetValue(TintColorProperty, value); }
        }



        //-------------------------------------------------------------
        // TransformView
        //-------------------------------------------------------------
        private const string nameTransformView = "TransformView";
        public static readonly BindableProperty TransformViewProperty = BindableProperty.Create(nameTransformView, typeof(View), typeof(Hotspot), null); //, BindingMode.TwoWay
        public View TransformView
        {
            get { return (View)GetValue(TransformViewProperty); }
            set { SetValue(TransformViewProperty, value); }
        }


        //-------------------------------------------------------------
        // DownOpacity
        //-------------------------------------------------------------
        private const string nameDownOpacity = "DownOpacity";
        public static readonly BindableProperty DownOpacityProperty = BindableProperty.Create(nameDownOpacity, typeof(double), typeof(Hotspot), 0.75); //, BindingMode.TwoWay

        private double _savedOpacity;

        public double DownOpacity
        {
            get { return (double)GetValue(DownOpacityProperty); }
            set { SetValue(DownOpacityProperty, value); }
        }

        protected Color _savedColor;

        private bool _TouchDown;
        public bool TouchDown
        {
            get { return _TouchDown; }
            set
            {
                if (_TouchDown != value)
                {
                    _TouchDown = value;
                    OnPropertyChanged();
                }
            }
        }

        private void OnUp(object sender, DownUpEventArgs e)
        {
            if (TransformView != null)
            {
                if (Reaction == HotspotReaction.Minify)
                {
                    TransformView.Scale = 1.0;
                }
                else
                if (Reaction == HotspotReaction.Zoom)
                {
                    TransformView.Scale = 1.0;
                }

                TransformView.Opacity = _savedOpacity;
                TransformView.BackgroundColor = _savedColor;
            }

            TouchDown = false;
        }

        private void OnDown(object sender, DownUpEventArgs e)
        {

            if (TransformView != null)
            {
                if (_savedOpacity != DownOpacity)
                    _savedOpacity = TransformView.Opacity;

                if (_savedColor != TintColor)
                    _savedColor = TransformView.BackgroundColor;

                if (Reaction == HotspotReaction.Tint)
                {
                    if (TintColor != null && TintColor != Colors.Transparent)
                    {
                        TransformView.BackgroundColor = TintColor;
                    }
                }
                else
                if (Reaction == HotspotReaction.Minify)
                {
                    TransformView.Opacity = DownOpacity;
                    TransformView.Scale = 0.985;
                    if (TintColor != null && TintColor != Colors.Transparent)
                    {
                        TransformView.BackgroundColor = TintColor;
                    }
                }
                else
                if (Reaction == HotspotReaction.Zoom)
                {
                    TransformView.Scale = 1.1;
                    if (TintColor != null && TintColor != Colors.Transparent)
                    {
                        TransformView.BackgroundColor = TintColor;
                    }
                }



            }

            TouchDown = true;
        }
    }
}