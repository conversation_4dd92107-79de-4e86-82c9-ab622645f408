﻿
using AppoMobi.Mobile.Views.Popups;


namespace AppoMobi.Xam
{
    public class PopupDialogBase : PopupPage
    {

        private string _message = "";
        public string Message
        {
            get { return _message; }

            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged("Message");
                }
            }
        }

        public Action<bool> Callback { get; set; } = null;

        public Action<int> CallbackList { get; set; } = null;

        public Action<string> CallbackListKey { get; set; } = null;



        // Invoced when background is clicked
        protected override bool OnBackgroundClicked()

        {
            //if (DisableBackgroundClick)
            //    // Return default value - CloseWhenBackgroundIsClicked
            //    return base.OnBackgroundClicked();

            return false;
        }

        public bool DisableBackgroundClick { get; set; }
        public bool QuitOnBackPressed { get; set; }



        protected async Task DismissDialog(int ret)
        {
            CallbackList(ret);
            Dismiss();
        }

    }
}
