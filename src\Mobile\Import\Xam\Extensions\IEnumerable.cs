﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Xam.Extensions
{
    public static class IEnumerableExtensions
    {

        //-------------------------------------------------------------------------------
        public static IEnumerable<T> Clone<T>(this IEnumerable<T> source)
            //-------------------------------------------------------------------------------
        {
            if (Object.ReferenceEquals(source, null))
            {
                return default(IEnumerable<T>);
            }

            // In the PCL we do not have the BinaryFormatter
            return JsonConvert.DeserializeObject<IEnumerable<T>>(
                JsonConvert.SerializeObject(source));
        }


    }
}
