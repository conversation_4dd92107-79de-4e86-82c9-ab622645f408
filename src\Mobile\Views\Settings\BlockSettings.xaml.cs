﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Services;
using AppoMobi.Touch;
using AppoMobi.UI;
using AppoMobi.Xam;
using FFImageLoading;
using FFImageLoading.Cache;
using FFImageLoading.Maui;
using System;
using System.Linq;

namespace AppoMobi
{
    public partial class BlockSettings
    {
 

        public BlockSettings()
        {
            InitializeComponent();

#if DEBUG

            //cDebug.IsVisible = true;
            //btnClearImagesCache.IsVisible = true;
            //btnColors.IsVisible = true;
            //ListFonts.IsVisible = true;

#endif

            _Setup = true;

            //LANGUAGES
            if (Core.Current.Info.Languages.Count() > 1)
            {
                OptionLanguage.ActionDesc = ResStrings.Settings_SelectLanguage;
                OptionLanguage.SelectionDesc = Core.GetLanguageName(Settings.Current.SelectedLang).ToTitleCase().ToTitleCase("(");
            }
            else
            {
                OptionLanguage.IsVisible = false;
            }

            //OptionTheme.ActionDesc = ResStrings.Settings_SelectTheme;
            //OptionTheme.SelectionDesc = Core.Current.Themes.Selected.Title;//Core.GetLanguageName(Settings.Current.SelectedLang).ToTitleCase().ToTitleCase("(");


            OptionStandartCameraFolder.ActionDesc = ResStrings.X_OptionSpecialCameraFolder;
            OptionStandartCameraFolder.IsSwitch = true;
            OptionStandartCameraFolder.SwitchValue = Settings.Current.OptionSpecialCameraFolder;

            OptionUseGeo.ActionDesc = ResStrings.X_OptionUseGeo;
            OptionUseGeo.IsSwitch = true;
            OptionUseGeo.SwitchValue = Settings.Current.OptionUseGeo;


            OptionIconsScreenAlwaysOn.IsSwitch = true;
            OptionIconsScreenAlwaysOn.SwitchValue = Settings.Current.OptionScreenOn == "yes";
            OptionIconsScreenAlwaysOn.ActionDesc = ResStrings.OptionScreenOn;

            //bottom menu icons text
            //OptionIconsText.IsSwitch = true;
            //OptionIconsText.SwitchValue = Settings.Current.iShowTabsText == "no";
            //OptionIconsText.ActionDesc = ResStrings.Settings_NoTitlesInTabs;

            
            _Setup = false;

        }
  

        private async void OnDown_Theme(object sender, EventArgs e)

        {
            var changed = await Core.Current.PresentThemesSelectionList();

            if (!changed)
                return;

            App.Instance.Messager.All("NeedRestart", "Now");
        }



        private async void OnDown_optionLanguage(object sender, EventArgs e)

        {
            var changed = await Core.Current.PresentLanguagesSelectionList();

            if (!changed)
                return;

            App.Instance.Messager.All("NeedRestart", "Now");
        }


        private bool _Setup { get; set; } = true;


        private bool goback { get; set; } = false;

        public void Submit()
        {
            App.Instance.Messager.All("SettingsUpdated", "");

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                App.Instance.Messager.All("Settings", "Apply");
            }
        }

        //
        //private async void OnDown_OptionSilentPush(object sender, EventArgs e)
        //
        //{
        //    Settings.Current.Settings_SilentPush = OptionSilentPush.SwitchValue;
        //     Core.Native.PushEnableSound(!Settings.Current.Settings_SilentPush);
        //}

        private async void OnDown_OptionIconsText(object sender, EventArgs e)
        {

            //Settings.Current.iShowTabsText = OptionIconsText.SwitchValue ? "no" : "yes";
            Globals.Values.iShowTabsText = Settings.Current.iShowTabsText == "yes";

            // Core.Native.UpdateTabbedMenu();
            //Globals.Values.TabsMain.UpdateSkin();
            App.Instance.Messager.All("UpdateTabs", $"");
        }

        private async void OnDown_OptionStandartCameraFolder(object sender, EventArgs e)
        {
            Settings.Current.OptionSpecialCameraFolder = OptionStandartCameraFolder.SwitchValue;
            Submit();
        }

        private async void OnDown_OptionUseGeo(object sender, EventArgs e)
        {
            Settings.Current.OptionUseGeo = OptionUseGeo.SwitchValue;
            Submit();
        }

        private async void OnDown_OptionScreenAlwaysOn(object sender, EventArgs e)
        {
            Settings.Current.OptionScreenOn = OptionIconsScreenAlwaysOn.SwitchValue ? "yes" : "no";
            App.current.SetupScreenOn();
        }

        //debug show tutorial

        private async void ButtonColors_OnClicked(object sender, EventArgs e)
        {
            var page = new PageColors();
            await NavigationService.PushModalAsync(Navigation, page, true);
        }


        private async void OnDown_Fonts(object sender, EventArgs e)
        {
            await Core.PushInstance(Navigation, typeof(PageFonts));
        }

        //
        //protected override bool OnBackButtonPressed()
        //
        //{


        //    if (Navigation.NavigationStack.Count < 3)
        //    {
        //        Globals.Values.Root?.ShowMenu();
        //        //Globals.Values.AppMenu.ClickMenuItem(Constants.CONST_MENU_HOME);
        //        return true;
        //    }
        //    else
        //        return false;
        //    //    MainThread.BeginInvokeOnMainThread(async () => {
        //    //        var result = await this.DisplayAlert("Thalion", "Вы хотите выйти из программы?", "Да", "Нет");
        //    //        if (result) { _canClose = false; base.OnBackButtonPressed();}
        //    //    });
        //    return true;
        //}



        private void OnTapped_BtnSystemSettings(object sender, EventArgs e)

        {
            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                Microsoft.Maui.ApplicationModel.Launcher.OpenAsync(new Uri("app-settings:"));
            }
            else
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                Core.Native.OpenSettings();
            }




            //opens wifi settings droid
            //  Microsoft.Maui.Controls.Forms.Context.StartActivity(new Android.Content.Intent(Android.Provider.Settings.ActionWifiSettings));

            //ios
            //Change: var url = new NSUrl("prefs:root=Settings");
            //To: var url = new NSUrl("prefs:root=WIFI");

        }

        private void OnTapped_BtnSystemGPS(object sender, EventArgs e)
        {
            Core.Native.OpenGPSSettings();
        }



        private void OnTapping_Dev(object sender, TapEventArgs e)

        {
            if (e.NumberOfTaps == 5)
            {
                btnClearImagesCache.IsVisible = true;
                btnColors.IsVisible = true;
            }
        }

        private async void ButtonDev_ClearImagesCache(object sender, EventArgs e)

        {
            ImageService.Instance.InvalidateCacheAsync(CacheType.All);
            Toast.ShortMessage("Cache Cleared");
        }

        private void CachedImage_OnError(object sender, CachedImageEvents.ErrorEventArgs e)
        {
            var ej = e.Exception;
        }


        private async void OnTapped_Tabs(object sender, EventArgs e)

        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                var changed = await Core.Current.PresentFavTabsSelectionList();

                if (changed)
                {

                    Settings.Current.AddOrUpdateValue("canAskForRating", true);

                    App.Instance.Messager.All("NeedRestart", "Now");

                }


            });


        }

    }
}
