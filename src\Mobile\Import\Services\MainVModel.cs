﻿using AppoMobi.Xam;
using System;


namespace AppoMobi.Main
{
	//*******************************************************************************
	public class MainVModel : InstancedBaseModel<MainVModel>, IDisposable
	//*******************************************************************************
	{
		public PhotoCalcUnits BellowsExtensionUnits { get; set; }
		public PhotoCalcUnits BellowsFocalUnits { get; set; }

		
		public MainVModel()
		
		{
			Subscribe(true);
		}

		public void Dispose()
		{
			Subscribe(false);
		}

		public void Subscribe(bool sub)
		{
			if (sub)
			{
				App.Instance.Messager.Subscribe<string>(this, "SettingsUpdated", async (sender, arg) =>
				{
					UpdateBindings();
					return;
				});
			}
			else
			{
				App.Instance.Messager.Unsubscribe(this, "SettingsUpdated");
			}
		}

		public void UpdateBindings()
		{
			OnPropertyChanged(nameof(Geotag));
			OnPropertyChanged(nameof(CustomAlbum));
		}

		public bool Geotag
		{
			get
			{
				return Settings.Current.OptionUseGeo;
			}
		}

		public string CustomAlbum
		{
			get
			{
				if (Settings.Current.OptionSpecialCameraFolder)
				{
                    return "Art Of Foto";
				}
                return string.Empty;
            }
        }


	}
}
