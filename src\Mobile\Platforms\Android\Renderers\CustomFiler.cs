﻿using Android.Text;
using Java.Lang;

namespace AppoMobi.Droid;

public class CustomFiler : Java.Lang.Object, IInputFilter
{

    protected string FilterDefault(string input)
    {
        return input;
    }

    public void SetInputFilter(Func<string, int, int, string> filter)
    {
        Filter = filter;
    }

        
    public Func<string, int, int, string> Filter { get; set; }
    public ICharSequence FilterFormatted(ICharSequence source, int start, int end, ISpanned dest, int dstart, int dend)
    {
        if (Filter != null)
        {
            var totalLength = dest.Length();
            var sourceLength = source.Length();
            var range = end - start;
            var length = dend - dstart;
            if (range == 0)
            {

            }

            var output = new SpannableString(Filter(source.ToString(), dstart, length));
            return output;
        }
        else
            return source;
    }
}