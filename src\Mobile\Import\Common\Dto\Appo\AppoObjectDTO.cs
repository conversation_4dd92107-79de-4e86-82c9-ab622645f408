﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.Dto.Appo
{
    public class AppoObjectDTO
        //===================================================================
    {
        public string Id { get; set; } //hello UID

        public string Kind { get; set; } //hello UID

        public string Categories { get; set; } //todo
        public string Keywords { get; set; } //NEW

        public string Name { get; set; }

        public string Title { get; set; } //todo
        public string Subtitle { get; set; } //todo

        public List<string> ServicesIds { get; set; } //taged list

        public string Color { get; set; }

        public string Desc { get; set; }

        public decimal Price { get; set; } //todo tariff?..

        public bool New { get; set; } //todo

        public int PriorityToBeBooked { get; set; } //for news

        public int Priority { get; set; } //for news

        //  public string Link { get; set; } //for sharing!

        public string ImageId { get; set; }

        public string ImageThumbnailMicro { get; set; }
        public string ImageThumbnailMini { get; set; }
        public string ImageThumbnailMedium { get; set; }
        public string ImageThumbnailNormal { get; set; }
        public string ImageThumbnailLarge { get; set; }

        //new
        [JsonIgnore] //gonna read this apart
        public List<AppoTimeInterval> BookableIntervals { get; set; }

        //public double ImageWidth { get; set; }
        //public double ImageHeight { get; set; }

        //public double DynamicWidth { get; set; }
        //public double DynamicHeight { get; set; }
    }
}