﻿using System;
using Microsoft.Maui.Controls.Internals;
 

namespace AppoMobi.UI
{
    [ContentProperty("Source")]
    [Preserve(AllMembers = true)]
    //===================================================================
    class ImageFromResourcesExtension : IMarkupExtension<ImageSource>
    //===================================================================
    {
        public string Source { set; get; }

        
        public ImageSource ProvideValue(IServiceProvider serviceProvider)
        
        {
            if (String.IsNullOrEmpty(Source))
            {
                IXmlLineInfoProvider lineInfoProvider = serviceProvider.GetService(typeof(IXmlLineInfoProvider)) as IXmlLineInfoProvider;
                var lineInfo = (lineInfoProvider != null) ? lineInfoProvider.XmlLineInfo : new XmlLineInfo();
                throw new XamlParseException("XAML  ImageFromResources requires Source property to be set", lineInfo);
            }
            
            //string assemblyName = GetType().GetTypeInfo().Assembly.GetName().Name;
            var generatedFilename = Core.AssemblyName + $".Images.{Source}";
            //Console.WriteLine($"***ImageFromResourcesExtension: {generatedFilename}");
            return ImageSource.FromResource(generatedFilename);
        }
        
        object IMarkupExtension.ProvideValue(IServiceProvider serviceProvider)
        
        {
            //Console.WriteLine($"***ImageFromResourcesExtension OBJECT");
            return (this as IMarkupExtension<ImageSource>).ProvideValue(serviceProvider);
        }
    }
}
