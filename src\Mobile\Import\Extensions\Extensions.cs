﻿namespace AppoMobi
{

    //===================================================================
    public static class ExtensionMethods
    //===================================================================
    {


        
        public static string GetHexString(this Color color)
        
        {
            var red = (int)(color.Red * 255);
            var green = (int)(color.Green * 255);
            var blue = (int)(color.Blue * 255);
            var alpha = (int)(color.Alpha * 255);
            var hex = $"#{alpha:X2}{red:X2}{green:X2}{blue:X2}";

            return hex;
        }
        
        public static string GetHexDesc(this Color color)
        
        {
            var red = (int)(color.Red * 255);
            var green = (int)(color.Green * 255);
            var blue = (int)(color.Blue * 255);
            var alpha = (int)(color.Alpha * 255);
            var hex = $"#{red:X2}{green:X2}{blue:X2}";

            return hex;
        }

        
        public static string CorrectStringUponNumber(this int num, string zero, string one, string with_one, string with_two, string other)
        
        {
            string ret = "";
            var lastDigit = "";
            int iDigit = 0;
            if (num > 0)
            {
                lastDigit = num.ToString().Substring(num.ToString().Length - 1);
                iDigit = int.Parse(lastDigit);
            }
            if (num < 1)
            {
                ret = zero;
            }
            else
            {
                if (num == 1)
                {
                    ret = string.Format(one, num);
                }
                else
                if (iDigit == 1 && !(num >= 10 && num <= 20))
                {
                    ret = string.Format(with_one, num);
                }
                else
                {
                    if ((iDigit >= 2 && iDigit <= 4) && !(num >= 10 && num <= 20))
                    {
                        ret = string.Format(with_two, num);
                    }
                    else
                    {
                        ret = string.Format(other, num);
                    }
                }

            }

            return ret;
        }

      
    }
}
