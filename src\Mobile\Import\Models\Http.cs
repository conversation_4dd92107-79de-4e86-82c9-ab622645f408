﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;


namespace NiftyTools
{
    public class NiftyHttp
    {

        public class HttpResponce
        {
            public HttpStatusCode Code { get; set; }
            public string Headers { get; set; }
            public string Body { get; set; }
            public string RequestUrl { get; set; }
            public string RequestContent { get; set; }
        }

        public class NiftyHttpClient : HttpClient
        {
            public NiftyHttpClient()
            {
                Timeout = TimeSpan.FromSeconds(10);
            }

        }


        
        public static string QueryString(IDictionary<string, string> dict)
        
        {
            if (dict == null) return string.Empty;
            var list = new List<string>();
            foreach (var item in dict)
            {
                list.Add(item.Key + "=" + item.Value);
            }
            return string.Join("&", list);
        }

        //-------------------------------------------------------
        public static async Task<NiftyHttp.HttpResponce> PostAsync(string fullUrl, Dictionary<string, string> parameters, string content = null)
        //-------------------------------------------------------
        {
            StringContent encodedContent = null;
            if (!string.IsNullOrEmpty(content))
                encodedContent = new StringContent(content, Encoding.UTF8, "application/json");

            NiftyHttpClient client = new NiftyHttpClient();
            var url = fullUrl + "?" + QueryString(parameters);

            #region AddAuthTokenToHeaders
            try
            {
                var token = Preferences.Default.Get("id_token", "");
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch
            { Debug.WriteLine("Auth Token Not Found"); }
            #endregion


            try
            {
                //_client.DefaultRequestHeaders.Add("Accept", "application/json");
                //_client.DefaultRequestHeaders.Add("Content-Type", "application/x-www-form-urlencoded");

                var response = await client.PostAsync(url, encodedContent);
                var contents = await response.Content.ReadAsStringAsync();
                return new HttpResponce
                {
                    Code = response.StatusCode,
                    Headers = response.ToString(),
                    Body = contents.ToString(),
                    RequestUrl = url,
                };
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return null;
            }
        }
        //-------------------------------------------------------
        public static async Task<HttpResponce> GetAsync(string fullUrl, Dictionary<string, string> parameters = null)
        //-------------------------------------------------------
        {
            NiftyHttpClient client = new NiftyHttpClient();
            var url = fullUrl + "?" + QueryString(parameters);

            #region AddAuthTokenToHeaders
            try
            {
                var token = Preferences.Default.Get("id_token", "");
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch
            { Debug.WriteLine("Auth Token Not Found"); }
            #endregion

            try
            {
                var response = await client.GetAsync(url);
                var contents = await response.Content.ReadAsStringAsync();
                return new HttpResponce
                {
                    Code = response.StatusCode,
                    Headers = response.ToString(),
                    Body = contents.ToString(),
                    RequestUrl = url,
                };
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return null;
            }
        }
        //-------------------------------------------------------
        public static HttpResponce Get(string fullUrl, Dictionary<string, string> parameters = null)
        //-------------------------------------------------------
        {
            NiftyHttpClient client = new NiftyHttpClient();
            var url = fullUrl + "?" + QueryString(parameters);

            #region AddAuthTokenToHeaders
            try
            {
                var token = Preferences.Default.Get("id_token", "");
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch
            { Debug.WriteLine("Auth Token Not Found"); }
            #endregion

            try
            {
                var response = client.GetAsync(url).Result;
                var contents = client.GetStringAsync(url).Result;
                return new HttpResponce
                {
                    Code = response.StatusCode,
                    Headers = response.ToString(),
                    Body = contents.ToString(),
                    RequestUrl = url,
                };
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return null;
            }
        }
        //-------------------------------------------------------
        public static async Task<HttpResponce> GetUrlWithAuthAsync(string uri, string token, Dictionary<string, string> parameters = null)
        //-------------------------------------------------------
        {
            NiftyHttpClient client = new NiftyHttpClient();
            var url = uri + "?" + QueryString(parameters);

            #region AddAuthTokenToHeaders
            try
            {
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch
            { Debug.WriteLine("Auth Token Not Found"); }
            #endregion
            try
            {
                var response = await client.GetAsync(url);
                var contents = await response.Content.ReadAsStringAsync();
                return new HttpResponce
                {
                    Code = response.StatusCode,
                    Headers = response.ToString(),
                    Body = contents.ToString(),
                    RequestUrl = url,
                };
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return null;
            }
        }
        //-------------------------------------------------------
        public static HttpResponce GetUrlWithAuth(string uri, string token, Dictionary<string, string> parameters = null)
        //-------------------------------------------------------
        {
            NiftyHttpClient client = new NiftyHttpClient();
            var url = uri + "?" + QueryString(parameters);

            #region AddAuthTokenToHeaders
            try
            {
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch
            { Debug.WriteLine("Auth Token Not Found"); }
            #endregion
            try
            {
                var response = client.GetAsync(url).Result;
                var contents = response.Content.ReadAsStringAsync().Result;
                return new HttpResponce
                {
                    Code = response.StatusCode,
                    Headers = response.ToString(),
                    Body = contents.ToString(),
                    RequestUrl = url,
                };
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return null;
            }
        }
    }
}
