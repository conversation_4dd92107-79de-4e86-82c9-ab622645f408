﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class NotConverter : ConverterBase
    {

        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool result;

            if (value is bool input)
            {
                result = !input;
            }
            else
            {
                result = false;
            }
            return result;
        }
    }
}