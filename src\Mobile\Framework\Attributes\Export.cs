﻿using System;

namespace AppoMobi.Framework.Attributes
{
    public class Export : Attribute
    
    {
        //...
        //public Type ChildType { get; set; }
        public Type ChildContextType { get; set; }
        public Type ChildExportType { get; set; }
        public string ChildController { get; set; }

        public string Key { get; set; } //content type for class, ex:  [Export(Key = DataConstants.KeyProdElems)] // ex: "prodelems"
        public string Name { get; set; }
        public bool NeedUploadId { get; set; }
        public string CopyInternationalLocalization { get; set; }
        //public string ImageRatio { get; set; }
    }
}