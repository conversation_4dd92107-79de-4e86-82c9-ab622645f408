﻿using System;


namespace AppoMobi.Xam.Antispam
{
    //================================================
    public class AntiSpamClick
    //================================================
    {
        public AntiSpamClick(EventHandler func)
        {
            ClickFunc += func;
        }

        public AntiSpamClick()
        {
        }

        public string Tag { get; set; }

        private object _sender { get; set; }
        private EventArgs _e { get; set; }

        public AntiSpamClick(object sender, EventArgs e)
        {
            _sender = sender;
            _e = e;
        }

        public EventHandler ClickFunc { get; set; }
        
        private bool locked { get; set; }
        private bool timerOn { get; set; }
        public void Click(object sender, EventArgs e)
         
        {
            if (locked || timerOn) return;
            locked = true;
            timerOn = true;

            Device.StartTimer(TimeSpan.FromMilliseconds(250), () =>
            {
                timerOn = false;
                return false;
            });

            ClickFunc?.Invoke(_sender, _e);

            locked = false;
        }
    }



    /*

        private AntiSpamClick CNavBar_OnDownIconScrollUp_Clicker;
        private async void CNavBar_OnDownIconScrollUp(object sender, EventArgs e) //tapped or dow event
        {
            if (CNavBar_OnDownIconScrollUp_Clicker == null)
                CNavBar_OnDownIconScrollUp_Clicker = new AntiSpamClick(CNavBar_OnDownIconScrollUp_Func);
            CNavBar_OnDownIconScrollUp_Clicker.Click(sender, e);
        }

     

     */
}
