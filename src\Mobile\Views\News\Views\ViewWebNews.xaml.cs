﻿
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Tenant;
using AppoMobi.Xam;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using AppoMobi.Services;
using NavBar = AppoMobi.UI.NavBar;

namespace AppoMobi.Pages
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class ViewWebNews
	{
		private string _content;

		protected string _url;
        private bool once;

		private void ControlBrowser_OnSizeChanged(object sender, EventArgs e)
		{
			if (ControlBrowser.Height > 1 && !once)
            {
                once = true;

				//int height = (int)(ControlBrowser.Height * Core.DisplayDensity);
				////if (newSize < 2000)
				////    newSize = 2000;

				//var width = (int)Core.ScreenSizePixels.Width;
				//if (width > 500)
				//	width = 500; //widget width
				//var zoomAmount = Core.ScreenSizePixels.Width / width;

				//height = (int)(height / zoomAmount);

				_url = App.NewsUrl;

                Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), () =>
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        SetSource(ResStrings.X_OurNews, _url, true);
                    });
                });
			}
		}

		
		public ViewWebNews()
		
		{
			InitializeComponent();

			ControlBrowser.ZoomEnabled = true;

			Title = ResStrings.X_OurNews;

			if (!TenantOptions.DarkSkin)
			{
				ControlBrowser.WebViewColor = Colors.White; //todo move this to onappearing for ios
			}


			AllowedUrls = new List<string>
				{
					@"appomobi.com",
					@"artoffoto.com",
             //       @"about:blank",
					@"/AppoMobi.iOS.app/"
				};


			var icon = new FontIconsPreset(FaPro.Rotate, 1.0);
			RightIcon1Symbol.SetIcon(icon);
			ToggleButtonVisibility(ButtonType.Right1, true);

			BindingContext = this;

		}



		public List<string> AllowedUrls { get; set; } = new List<string>();


		
		public override void OnRightIcon1Clicked()
		
		{
			//reload page

			SetSource(ResStrings.X_OurNews, _url, true);

			base.OnRightIcon1Clicked();
		}

		/// <summary>
		/// Ex: "Json.info.json"
		/// </summary>
		/// <param name="nameAfterAssembly"></param>
		/// <returns></returns>
		
		public string ReadFromAssembly(string nameAfterAssembly)
		
		{
			var assembly = typeof(Core).GetTypeInfo().Assembly;
			Stream stream = assembly.GetManifestResourceStream(assembly.GetName().Name + $".{nameAfterAssembly}");

			if (stream == null)
				return "";
			string json = "";
			using (var reader = new System.IO.StreamReader(stream))
			{
				json = reader.ReadToEnd();
			}
			return json;
		}

		
		public void SetSource(string title, string source, bool isUrl = true)
		
		{
			ShowLoaderOverlay = true;

			//    Title = title;

			if (isUrl)
			{
				if (string.IsNullOrEmpty(source))
				{
					source = "about:blank";
				}
				var url = new UrlWebViewSource
				{
					Url = source
				};

				ControlBrowser.Source = url;
			}
			else
			{
				if (string.IsNullOrEmpty(source))
				{
					source = "";
				}
				var html = new HtmlWebViewSource
				{
					Html = source
				};

				ControlBrowser.Html = source;

				ControlBrowser.Source = html;


			}

			//ShowLoaderOverlay = false;

		}


		private void MyBrowser_OnNavigating(object sender, WebNavigatingEventArgs e)
		{
			if (e.Url.ToLowerInvariant().Contains("onrendered"))
			{
				e.Cancel = true;
				var args = new WebNavigatedEventArgs(e.NavigationEvent, e.Source, e.Url, WebNavigationResult.Cancel);
				MyBrowser_OnNavigated(sender, args);
				return;
			}

			var allowed = false;
			foreach (var url in AllowedUrls)
			{
				if (e.Url.Contains(url))
				{
					allowed = true;
				}
			}

			if (!allowed)
			{
				e.Cancel = true;
				if (e.Url.Contains("facebook.com"))
				{
					if (!Core.Native.OpenInAppFacebook(e.Url))
					{
						Core.Native.OpenUrl(e.Url);
					}
				}
			}


			//if (!e.Url.Contains("appomobi.com"))
			//{
			//    e.Cancel = true;

			//    if (!Core.Native.OpenInAppFacebook(e.Url))
			//    {
			//        Core.Native.OpenUrl(e.Url);
			//    }

			//}

			ShowTransparentLoader(false);

			var stop = true;
		}
		
		private void MyBrowser_OnNavigated(object sender, WebNavigatedEventArgs e)
		
		{

			if (e.Result == WebNavigationResult.Cancel)
			{
			}
			else
			{

				ShowLoaderOverlay = false;

				//ControlHide.IsVisible = false;

				ControlBrowser.Url = e.Url;

			}



		}

		private void OnClicked(object sender, XamWebView.ClickEventArgs e)
		{


		}

	}
}
