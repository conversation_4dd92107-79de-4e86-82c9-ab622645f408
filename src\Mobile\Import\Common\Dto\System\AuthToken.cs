﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.Common.Dto.System
{
    public class AuthToken
    {
        [JsonProperty("username")] public string username { get; set; } = "";

        [JsonProperty("id")]
        public string id { get; set; } = "";

        public bool welcome { get; set; }

        [JsonProperty("expires_in")]
        public double expires_in { get; set; }

        public string access_token { get; set; } = "";
        public string token_type { get; set; } = "";

        [JsonProperty(".issued")]
        public DateTime issued { get; set; }

        [JsonProperty(".expires")]
        public DateTime expires { get; set; }

        [JsonIgnore]
        public TimeSpan TimeLeft {
            get
            {
                try
                {
                    return (issued.AddMinutes(expires_in) - DateTime.UtcNow);
                }
                catch
                {
                    return new TimeSpan(0,0,0);
                }
            }
            
        }
    }

}
