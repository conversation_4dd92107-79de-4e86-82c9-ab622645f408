﻿using Android.Util;
using AnimatedNavi.Forms;
using AnimatedNavi.Droid;
using Xamarin.Forms;

[assembly: Dependency(typeof(DroidDevice))]
namespace AnimatedNavi.Droid
{
    public class DroidDevice : IDeviceInfo
    {
        public static DisplayMetrics Metrics => Android.App.Application.Context.Resources.DisplayMetrics;

        private static float ActionBarHeight
        {
            get
            {
                var styledAttributes = Android.App.Application.Context.Theme.ObtainStyledAttributes( new[] { Android.Resource.Attribute.ActionBarSize });
                var actionbarHeight = styledAttributes.GetDimension(0, 0);
                styledAttributes.Recycle();
                return actionbarHeight;
            }
        }

        public double ScreenHeight => Metrics.HeightPixels - ActionBarHeight;

        public double ScreenWidth => Metrics.WidthPixels;
    }
}