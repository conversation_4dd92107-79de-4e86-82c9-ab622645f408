﻿
 

namespace AppoMobi.Xam
{
    //specialized class for showing a page within a page
    public partial class PageViewContainer : View
    {

       
        public PageViewContainer()
        {

        }


        public static readonly BindableProperty ContentProperty = BindableProperty.Create(
            nameof(Content),
            typeof(Page),
            typeof(PageViewContainer),
            null);

        public Page Content
        {
            get { return (Page)GetValue(ContentProperty); }
            set { SetValue(ContentProperty, value); }
        }


 
        //public static readonly BindableProperty ContentProperty = BindableProperty
        //    .Create<PageViewContainer, Page>(s => s.Content, null);

        //public Page Content
        //{
        //    get { return (Page)GetValue(ContentProperty); }
        //    set { SetValue(ContentProperty, value); }
        //}
    }
}
