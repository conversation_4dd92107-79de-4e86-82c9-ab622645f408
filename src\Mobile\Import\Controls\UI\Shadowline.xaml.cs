﻿using AppoMobi.Tenant;



namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Shadowline
    {
        public Shadowline()
        {
            InitializeComponent();
            Initialize();
        }

        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                IsVisible = false;
            }
            else
            {
                IsVisible = true;
            }
            if (TenantOptions.DarkSkin)
            {
                BackgroundColor = Color.Parse("#15fefefe");
            }
        }

    }
}