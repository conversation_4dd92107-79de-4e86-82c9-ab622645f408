﻿using System;


namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class CellOptionsList
    {
        
        
        public CellOptionsList()
        
		{
			InitializeComponent ();
		  

		}

	    
	    protected override void OnBindingContextChanged()
	    
	    {
	        SetupCell();

	        base.OnBindingContextChanged();
	    }


        
        public void SetupCell()
        
        {
            var item = BindingContext as OptionsListItem;
            if (item == null) return;

            //todo

            var show = true;
            var isVisible = item.IsVisible?.Invoke();
            if (isVisible.HasValue)
            {
                if (!isVisible.Value) show = false;
            }

            if (show)
            {
                cOptionLine.IsSwitch = item.IsSwitch;
                cOptionLine.SwitchValue = item.SwitchValue;
                cOptionLine.ActionDesc = item.ActionDesc;
                cOptionLine.SelectionDesc = item.SelectionDesc;
            }
            else
            {
                cOptionLine.IsVisible = false;
            }

        }

        public event EventHandler ProcessTap;

        
        private void OnDown_OptionIconsText(object sender, EventArgs e)
        
        {
            ProcessTap?.Invoke(this, e);

            var item = BindingContext as OptionsListItem;
            if (item == null) return;

            item.Switched?.Invoke(cOptionLine.SwitchValue);
        }
    }
}