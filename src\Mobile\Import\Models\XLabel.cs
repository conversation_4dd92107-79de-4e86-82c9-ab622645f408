﻿using System;
using Xamarin.Forms;

namespace AppoMobi
{
     //****************************************************
    public class XLabel : Label
     //****************************************************
    {
        public double LineSpacing { get; set; }
        public bool Justify { get; set; } = false;
        public bool FontMaterial { get; set; }

        
        public XLabel()
        
        {
            this.SizeChanged += VisualElement_OnSizeChanged;
            VerticalTextAlignment = TextAlignment.Center;
        }

        private double last_height { get; set; }
        private double last_width { get; set; }
        
        private void VisualElement_OnSizeChanged(object sender, EventArgs e)
        
        {
         

            var view = (View)sender;                      
            if (view.Height > 0)
            {
                if (last_height == 0 || (view.Height < KeepMinimumHeight)) //first time
                {
                    if (KeepMinimumHeight>0)
                    {
                        if (view.Height < KeepMinimumHeight)
                        {
                            view.HeightRequest = KeepMinimumHeight;
                        }
                    }
                    else
                        view.HeightRequest = view.Height;
                }
                else
                if (last_width != view.Width) //rotated or something..
                {
                    //recalculate
                    HeightRequest = -1;
                }
                else
                if (last_height != view.Height)
                {
                    if (KeepMinimumHeight > 0)
                    {
                        if (view.Height < KeepMinimumHeight)
                        {
                            view.HeightRequest = KeepMinimumHeight;
                        }
                    }
                    else
                    view.HeightRequest = view.Height;
                }

                last_height = view.Height;
                last_width = view.Width;
            }
        }


        //-------------------------------------------------------------
        // KeepMinimumHeight
        //-------------------------------------------------------------
        private const string nameKeepMinimumHeight = "KeepMinimumHeight";
        public static readonly BindableProperty KeepMinimumHeightProperty = BindableProperty.Create(nameKeepMinimumHeight, typeof(double), typeof(XLabel), 0.0); //, BindingMode.TwoWay
        public double KeepMinimumHeight
        {
            get { return (double)GetValue(KeepMinimumHeightProperty); }
            set { SetValue(KeepMinimumHeightProperty, value); }
        }	

		
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                //property changed
                case nameKeepMinimumHeight:
                    OnPropertyChanged("Text"); //redraw
                    break;
            }

        }


    }
}
