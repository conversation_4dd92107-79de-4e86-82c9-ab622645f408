﻿using System;
using System.Globalization;



namespace AppoMobi.Forms.Framework.Xaml
{
    public class ZeroGridLengthIfStringEmptyConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var defaultValue = ((string)parameter).ToDouble();

                if (string.IsNullOrEmpty((string)value))
                    return defaultValue = 0.0;

                return new GridLength(defaultValue);
            }
            catch (Exception e)
            {
            }

            return value;
        }
    }
}