﻿using System;
using AppoMobi.Models;

#if BACKEND
using Microsoft.Azure.Mobile.Server;
#else

#endif

namespace AppoMobi
{
//===================================================================
    public interface IBaseDataObject
//===================================================================
    {
        string Id { get; set; }
    }
#if BACKEND
    public class BaseDataObject : EntityData
    {
        public BaseDataObject ()
        {
            Id = Guid.NewGuid().ToString();
        }

        public string RemoteId { get; set; }
    }
#else
    public class BaseDataObject : ObservableObject, IBaseDataObject
    {
        public BaseDataObject()
        {
            Id = Guid.NewGuid().ToString();
        }

        public string RemoteId { get; set; }

        [Newtonsoft.Json.JsonProperty("Id")]
        public string Id { get; set; }

        //[Microsoft.WindowsAzure.MobileServices.Version]
        //public string AzureVersion { get; set; }
    }
#endif

//===================================================================
    public class MoreInfo : BaseDataObject
//===================================================================
    {
        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        /// <value>The name of the company.</value>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the company website URL.
        /// </summary>
        /// <value>The company website URL.</value>
        public string CompanyWebsiteUrl { get; set; }

        /// <summary>
        /// Gets or sets the blog URL.
        /// </summary>
        /// <value>The blog URL.</value>
        public string BlogUrl { get; set; }

        public string FacebookUrl { get; set; }
        public string VKUrl { get; set; }


        /// <summary>
        /// Gets or sets the twitter profile: 
        /// For http://twitter.com/JamesMontemagno this is: JamesMontemagno NO @
        /// </summary>
        /// <value>The twitter URL.</value>
        public string TwitterUrl { get; set; }

        /// <summary>
        /// Gets or sets the linked in profile name.
        /// https://www.linkedin.com/in/jamesmontemagno we just need: jamesmontemagno
        /// </summary>
        /// <value>The linked in URL.</value>
        public string LinkedInUrl { get; set; }

        public string InstagramUrl { get; set; }

        /// <summary>
        /// This is the big Hero Image (Rectangle)
        /// </summary>
        /// <value>The photo URL.</value>
        public string PhotoUrl { get; set; }

        public string Email { get; set; }

        //        public virtual ICollection<Session> Sessions { get; set; }


        [Newtonsoft.Json.JsonIgnore]
        public Uri PhotoUri 
        { 
            get 
            { 
                try
                {
                    return new Uri(PhotoUrl);
                }
                catch
                {

                }
                return null;
            } 
        }

    }
}
