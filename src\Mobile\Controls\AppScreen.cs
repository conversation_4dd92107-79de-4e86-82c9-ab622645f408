﻿using System.Diagnostics;
using AppoMobi.Maui.Gestures;
using AppoMobi.Models;
using AppoMobi.ViewModels;

namespace AppoMobi.Main
{

  
    public class AppScreen : SkiaLayout
    {
        private object _lastContext;

        public AppScreen()
        {
            Debug.WriteLine($"Created drawn screen {this.GetType()}");

            HorizontalOptions = LayoutOptions.Fill;
            //VerticalOptions = LayoutOptions.Fill;
            //BackgroundColor = AppColors.BackgroundPrimary;
            //FillGradient = new SkiaGradient()
            //{
            //    Colors = new[]
            //    {
            //        AppColors.BackgroundPrimary,
            //        AppColors.BackgroundColoredLight
            //    }
            //};
        }

        //block gestures below

        public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
            GestureEventProcessingInfo apply)
        {
            var consumed = base.ProcessGestures(args, apply);

            //Debug.WriteLine($"[AppScreen] got {args.Type}'");

            if (consumed == null && args.Type != TouchActionResult.Up)
            {
                //Debug.WriteLine($"[AppScreen] consuming {args.Type}'");
                consumed = this;
            }

            return consumed;
        }

        // Release disposable view model

        protected override void OnBindingContextChanged()
        {
            var vm = _lastContext as IDisposable;

            base.OnBindingContextChanged();

            _lastContext = BindingContext;

            if (BindingContext == null)
            {
                _lastContext = null;

                if (vm is BaseViewModel)
                {
                    vm?.Dispose();
                }
            }
        }
    }
}
