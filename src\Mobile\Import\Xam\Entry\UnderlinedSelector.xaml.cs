﻿using System;
using System.Collections.Generic;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Controls.Input;
using AppoMobi.Touch;


using Picker = Microsoft.Maui.Controls.Picker;

namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class UnderlinedSelector
	{

        //-------------------------------------------------------------
        // Items
        //-------------------------------------------------------------
        private const string nameItems = "Items";
        public static readonly BindableProperty ItemsProperty = BindableProperty.Create(nameItems, typeof(IList<string>), typeof(UnderlinedSelector), null); //, BindingMode.TwoWay
        public IList<string> Items
        {
            get { return (IList<string>)GetValue(ItemsProperty); }
            set { SetValue(ItemsProperty, value); }
        }


        public event EventHandler<int> OnSelected;

        //-----------------------------------------------------------------
        protected void InitPicker()
        //-----------------------------------------------------------------
        {
            if (MainPicker != null)
            {
                MainPicker.SelectedIndexChanged -= OnIndexChanged_Picker;
                PickerContainer.Content = null;
            }
            MainPicker = new Picker();
            MainPicker.SelectedIndexChanged += OnIndexChanged_Picker;
            foreach (var position in Items)
            {
                MainPicker.Items.Add(position);
            }
            PickerContainer.Content = MainPicker;
        }


        //-------------------------------------------------------------
        // SelectedValue
        //-------------------------------------------------------------
        private const string nameSelectedValue = "SelectedValue";
        public static readonly BindableProperty SelectedValueProperty = BindableProperty.Create(nameSelectedValue, typeof(string), typeof(UnderlinedSelector), string.Empty); //, BindingMode.TwoWay
        public string SelectedValue
        {
            get { return (string)GetValue(SelectedValueProperty); }
            set { SetValue(SelectedValueProperty, value); }
        }	


        
        private void OnIndexChanged_Picker(object sender, EventArgs e)
        
        {
            try
            {
                var maybeValue = Items[MainPicker.SelectedIndex];
               SelectedValue = maybeValue;
            }
            catch (Exception exception)
            {
            }
            UpdateBottomPlaceholder();
            OnSelected?.Invoke(sender, MainPicker.SelectedIndex);          
        }

        private Picker MainPicker { get; set; } = null;
        
        private void OnTapped_Selector(object sender, TapEventArgs tapEventArgs)
        
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                InitPicker();
                MainPicker.Focus();
            });
        }
        //-------------------------------------------------------------
        // PlaceholderSmallTextSize
        //-------------------------------------------------------------
        private const string namePlaceholderSmallTextSize = "PlaceholderSmallTextSize";
	    public static readonly BindableProperty PlaceholderSmallTextSizeProperty = BindableProperty.Create(namePlaceholderSmallTextSize, typeof(double), typeof(UnderlinedSelector), 10.0); //, BindingMode.TwoWay
	    public double PlaceholderSmallTextSize
	    {
	        get { return (double)GetValue(PlaceholderSmallTextSizeProperty); }
	        set { SetValue(PlaceholderSmallTextSizeProperty, value); }
	    }

        //-------------------------------------------------------------
        // Required
        //-------------------------------------------------------------
        private const string nameRequired = "Required";
        public static readonly BindableProperty RequiredProperty = BindableProperty.Create(nameRequired, typeof(bool), typeof(UnderlinedSelector), false); //, BindingMode.TwoWay
        public bool Required
        {
            get { return (bool)GetValue(RequiredProperty); }
            set { SetValue(RequiredProperty, value); }
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {
 
                case nameSelectedValue:
                    if (!string.IsNullOrEmpty(SelectedValue))
                    {
                        cEntry.Text = SelectedValue;
                        HasSelection = true;
                    }
                    else
                    {
                        HasSelection = false;
                    }
                    UpdateBottomPlaceholder();
                    break;

	            case namePlaceholderSmallTextSize:
	                cPlaceholder.FontSize = PlaceholderSmallTextSize;
                    break;

 

                case namePlaceholderText:
                    //cPlaceholder.Text = PlaceholderText;
 
        
                        cEntry.Text = PlaceholderText;
                    UpdateBottomPlaceholder();
                  
	                break;

                case nameRequired:
	            case nameText:
                    UpdateBottomPlaceholder();
	                break;
 

	            case nameReadOnly:
	                cEntry.InputTransparent = ReadOnly;
	                if (FadeUnfocused)
	                {
	                    if (ReadOnly) 
	                        cEntry.Opacity = 0.5;
	                    else
	                        cEntry.Opacity = 1.0;
	                }
                    else
	                {
	                    
	                }
	                break;

	            case nameTextSize:
	                cEntry.FontSize = TextSize;
                    break;

	            case namePlaceholderActiveColor:
	                UpdateBottomPlaceholder();
                    break;


                case nameHideNativePlaceholder:
                    if (HideNativePlaceholder)
                    {
                        Placeholder = NativePlaceholderText;
                    }
                    else
                    {
                        Placeholder = PlaceholderText;
                    }
                    UpdateBottomPlaceholder();
                    break;

                case nameNativePlaceholderText:
                    if (HideNativePlaceholder)
                    {
                        Placeholder = NativePlaceholderText;
                    }
                    break;
            }

        }

        //-------------------------------------------------------------
        // PlaceholderColor
        //-------------------------------------------------------------
        private const string namePlaceholderColor = "PlaceholderColor";
        public new static readonly BindableProperty PlaceholderColorProperty = BindableProperty.Create(namePlaceholderColor, typeof(Color), typeof(EntryCentered), Colors.Gray); //, BindingMode.TwoWay
        public new Color PlaceholderColor
        {
            get { return (Color)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }


        //-------------------------------------------------------------
        // PlaceholderTextSize
        //-------------------------------------------------------------
        private const string namePlaceholderTextSize = "PlaceholderTextSize";
        public static readonly BindableProperty PlaceholderTextSizeProperty = BindableProperty.Create(namePlaceholderTextSize, typeof(double), typeof(UnderlinedEntry), FontSizes.Medium); //, BindingMode.TwoWay
        public double PlaceholderTextSize
        {
            get { return (double)GetValue(PlaceholderTextSizeProperty); }
            set { SetValue(PlaceholderTextSizeProperty, value); }
        }

        //-------------------------------------------------------------
        // NativePlaceholderText
        //-------------------------------------------------------------
        private const string nameNativePlaceholderText = "NativePlaceholderText";
        public static readonly BindableProperty NativePlaceholderTextProperty = BindableProperty.Create(nameNativePlaceholderText, typeof(string), typeof(UnderlinedEntry), ""); //, BindingMode.TwoWay
        public string NativePlaceholderText
        {
            get { return (string)GetValue(NativePlaceholderTextProperty); }
            set { SetValue(NativePlaceholderTextProperty, value); }
        }


        
        public void AjustEntryHeight(double height)
        
	    {
	        cEntry.HeightRequest = height;
        }


 



        //-------------------------------------------------------------
        // prop1
        //-------------------------------------------------------------
        private const string nameprop1 = "prop1";
        public static readonly BindableProperty prop1Property = BindableProperty.Create(nameprop1, typeof(double), typeof(UnderlinedSelector), 1.0); //, BindingMode.TwoWay
        public double prop1
        {
            get { return (double)GetValue(prop1Property); }
            set { SetValue(prop1Property, value); }
        }	




        //-------------------------------------------------------------
        // TextSize
        //-------------------------------------------------------------
        private const string nameTextSize = "TextSize";
        public static readonly BindableProperty TextSizeProperty = BindableProperty.Create(nameTextSize, typeof(double), typeof(UnderlinedSelector), FontSizes.Medium); //, BindingMode.TwoWay
        public double TextSize
        {
            get { return (double)GetValue(TextSizeProperty); }
            set { SetValue(TextSizeProperty, value); }
        }	


 


        //-------------------------------------------------------------
        // Text
        //-------------------------------------------------------------
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(UnderlinedSelector), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get
            {
                return cEntry.Text;
                //return (string)GetValue(TextProperty);
            }
            set
            {
                cEntry.Text = value;
                SetValue(TextProperty, value);
            }
        }	

        public string Placeholder { get; set; }

        //-------------------------------------------------------------
        // PlaceholderText
        //-------------------------------------------------------------
        private const string namePlaceholderText = "PlaceholderText";
        public static readonly BindableProperty PlaceholderTextProperty = BindableProperty.Create(namePlaceholderText, typeof(string), typeof(UnderlinedSelector), string.Empty); //, BindingMode.TwoWay
        public string PlaceholderText
        {
            get { return (string)GetValue(PlaceholderTextProperty); }
            set { SetValue(PlaceholderTextProperty, value); }
        }


        //-------------------------------------------------------------
        // ReadOnly
        //-------------------------------------------------------------
        private const string nameReadOnly = "ReadOnly";
        public static readonly BindableProperty ReadOnlyProperty = BindableProperty.Create(nameReadOnly, typeof(bool), typeof(UnderlinedSelector), false); //, BindingMode.TwoWay
        public bool ReadOnly
        {
            get { return (bool)GetValue(ReadOnlyProperty); }
            set { SetValue(ReadOnlyProperty, value); }
        }

	    //-------------------------------------------------------------
	    // FadeUnfocused
	    //-------------------------------------------------------------
	    private const string nameFadeUnfocused = "FadeUnfocused";
	    public static readonly BindableProperty FadeUnfocusedProperty = BindableProperty.Create(nameFadeUnfocused, typeof(bool), typeof(UnderlinedSelector), false); //, BindingMode.TwoWay
	    public bool FadeUnfocused
	    {
	        get { return (bool)GetValue(FadeUnfocusedProperty); }
	        set { SetValue(FadeUnfocusedProperty, value); }
	    }

        //-------------------------------------------------------------
        // PlaceholderActiveColor
        //-------------------------------------------------------------
        private const string namePlaceholderActiveColor = "PlaceholderActiveColor";
        public static readonly BindableProperty PlaceholderActiveColorProperty = BindableProperty.Create(namePlaceholderActiveColor, typeof(Color), typeof(UnderlinedSelector), TextColors.PlaceholderActive); //, BindingMode.TwoWay
        public Color PlaceholderActiveColor
        {
            get { return (Color)GetValue(PlaceholderActiveColorProperty); }
            set { SetValue(PlaceholderActiveColorProperty, value); }
        }	

        public new bool IsFocused { get; private set; }

        
        public UnderlinedSelector()
        
		{
			InitializeComponent ();
          
            try
            {
		        cPlaceholder.Text = Placeholder;
                cPlaceholder.TextColor = PlaceholderColor;
            }
		    catch
		    {
		    }


            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
		    {
		        cGradientLine.HeightRequest = 0.5;
		        cPlaceholder.TranslationY = 0;
		    }
		    else
		    {
		        cGradientLine.HeightRequest = 0.8;
		        cPlaceholder.TranslationY = 0.5;
            }

		    UpdateBottomPlaceholder();

 

        }



        //-------------------------------------------------------------
        // HideNativePlaceholder
        //-------------------------------------------------------------
        private const string nameHideNativePlaceholder = "HideNativePlaceholder";
        public static readonly BindableProperty HideNativePlaceholderProperty = BindableProperty.Create(nameHideNativePlaceholder, typeof(bool), typeof(UnderlinedEntry), false); //, BindingMode.TwoWay
        public bool HideNativePlaceholder
        {
            get { return (bool)GetValue(HideNativePlaceholderProperty); }
            set { SetValue(HideNativePlaceholderProperty, value); }
        }


        protected bool HasSelection { get; set; }

        
        protected void UpdateBottomPlaceholder()
        
	    {
            cPlaceholder.TextColor = TextColors.Placeholder;

            if (HasSelection)
	        {
 
	                cEntry.TextColor = TextColors.Entry;
                  //  cPlaceholder.TextColor = PlaceholderActiveColor;
            }
	        else
	        {
               
                cEntry.TextColor = TextColors.Placeholder;
              
            }

            if (!HasSelection && Required)
                cPlaceholder.Text = ResStrings.Required;
            else
                cPlaceholder.Text = PlaceholderText;

            if (HasSelection && !HideNativePlaceholder)
	        {
                if (Required)
                {
                    //required
                    cPlaceholder.IsVisible = true;
                }
                else
                {
                    cPlaceholder.IsVisible = false;
                }
            }
	        else
	        {
	            cPlaceholder.IsVisible = true;
	        }

        }

        /// <summary>
        /// Focus left the field
        /// </summary>
        public event EventHandler UnfocusedEvent;

	    //focus left the entry
	    /// <summary>
	    /// Focus entered field
	    /// </summary>
	    public event EventHandler FocusedEvent;

	    /// <summary>
	    /// Fires when text is typed so you can react to changes in entry
	    /// </summary>
	    public event EventHandler EditingEvent;


    }
}