﻿using System.Reflection;
using Microsoft.Maui.Controls.Internals;

namespace AppoMobi.Xam
{
    [Preserve(AllMembers = true)]
    
    public static class FontIcons
    
    {
        //FABRANDS
        public static FontIconsPreset fa_facebook_square  = new FontIconsPreset("\uf082", 1.00, "FaBrands", 0.5);
        public static FontIconsPreset fa_google = new FontIconsPreset("\uf1a0", 1.00, "FaBrands", 0.5);

        public static FontIconsPreset fa_save = new FontIconsPreset("\uf0c7", 1.00, "FaSolid", 0.5);

        public static FontIconsPreset fa_sms = new FontIconsPreset("\uf7cd", 1.00, "FaSolid", 0.5);

        public static FontIconsPreset fa_star = new FontIconsPreset("\uf005", 1.00, null, 0.5);

        /// <summary>
        /// Menu
        /// </summary>
        public static FontIconsPreset fa_bars = new FontIconsPreset("\uf0c9", 1.075, null, 0.5);

        /// <summary>
        /// Go Back
        /// </summary>
        public static FontIconsPreset fa_long_arrow_left = new FontIconsPreset("\uf177", 1.2, null, 0.5);
        
        public static FontIconsPreset fa_long_arrow_up = new FontIconsPreset("\uf176", 1.1, null, 0.5);

        public static FontIconsPreset fa_arrow_to_top = new FontIconsPreset("\uf341", 1.2, null, 0.5);

        public static FontIconsPreset fa_ballot = new FontIconsPreset("\uf732", 1.0, null, 0.5);
        /// <summary>
        /// Privacy
        /// </summary>
        public static FontIconsPreset fa_file_contract = new FontIconsPreset("\uf56c", 1.0, null, 0.5);

        public static FontIconsPreset fa_file_alt = new FontIconsPreset("\uf15c", 1.0, null, 0.5);


        public static FontIconsPreset fa_paw = new FontIconsPreset("\uf1b0", 1.0, null, 0.5);

        public static FontIconsPreset fa_sitemap = new FontIconsPreset("\uf0e8", 1.0, null, 0.5);


        public static FontIconsPreset fa_book = new FontIconsPreset("\uf02d", 1.0, null, 0.5);



        /// <summary>
        /// Home and News
        /// </summary>
        public static FontIconsPreset fa_home = new FontIconsPreset("\uf015", 1.0, null, 0.5);

        public static FontIconsPreset fa_box_alt = new FontIconsPreset("\uf49a", 1.0, null, 0.5);


        public static FontIconsPreset fa_fill_drip = new FontIconsPreset("\uf576", 0.9, null, 0.5);
        public static FontIconsPreset fa_fill = new FontIconsPreset("\uf575", 1.0, null, 0.5);


        //flasks
        public static FontIconsPreset fa_flask_potion = new FontIconsPreset("\uf6e1", 1.0, null, 0.5);
        /// <summary>
        /// water
        /// </summary>
        public static FontIconsPreset fa_tint = new FontIconsPreset("\uf043", 1.0, null, 0.0);

        public static FontIconsPreset fa_water = new FontIconsPreset("\uf773", 1.0, null, 0.5);



        //GPS etc

        public static FontIconsPreset fa_atlas = new FontIconsPreset("\uf558", 0.95, null, 0.5);
        /// <summary>
        /// World
        /// </summary>
        public static FontIconsPreset fa_globe = new FontIconsPreset("\uf0ac", 0.95, null, 0.5);

        public static FontIconsPreset fa_compass = new FontIconsPreset("\uf14e", 1.0, null, 0.5);

        public static FontIconsPreset fa_flag = new FontIconsPreset("\uf024", 1.0, null, 0.5);

        public static FontIconsPreset fa_heart = new FontIconsPreset("\uf004", 1.0, null, 0.5);


        //public static FontIconsPreset fa_info_circle = new FontIconsPreset("\uf05a", 0.0, null, 0.5);

        /// <summary>
        /// Go Back
        /// </summary>
        public static FontIconsPreset arrow_alt_circle_left = new FontIconsPreset("\uf359", 1.1, null, 0.5);

        /// <summary>
        /// Call
        /// </summary>
        public static FontIconsPreset fa_phone = new FontIconsPreset("\uf095", 1.0, null, 1.1);
        /// <summary>
        /// Call
        /// </summary>
        public static FontIconsPreset fa_phone_volume = new FontIconsPreset("\uf2a0", 1.0, null, 0.5);
        /// <summary>
        /// Call square
        /// </summary>
        public static FontIconsPreset fa_phone_square = new FontIconsPreset("\uf098", 1.1, null, 0.5);


        public static FontIconsPreset fa_envelope = new FontIconsPreset("\uf0e0", 0.9, null, 0.5);

        public static FontIconsPreset fa_car = new FontIconsPreset("\uf1b9", 1.1, null, 0.5);

        public static FontIconsPreset fa_shopping_basket = new FontIconsPreset("\uf291", 1.1, null, 0.5);

        public static FontIconsPreset fa_box_heart = new FontIconsPreset("\uf49d", 1.1, null, 0.5);

        public static FontIconsPreset fa_calendar_check = new FontIconsPreset("\uf274", 1.1, null, 0.5);

        public static FontIconsPreset fa_plus_octagon = new FontIconsPreset("\uf301", 1.1, null, 0.5);

        public static FontIconsPreset fa_info_circle = new FontIconsPreset("\uf05a", 1.1, null, 0.5);

        /// <summary>
        /// Navigate by car
        /// </summary>
        public static FontIconsPreset fa_route = new FontIconsPreset("\uf4d7", 1.1, null, 0.5);

        /// <summary>
        /// Navigate by car
        /// </summary>
        public static FontIconsPreset fa_location_arrow = new FontIconsPreset("\uf124", 1.0, null, 0.5);


        public static FontIconsPreset fa_arrow_alt_left = new FontIconsPreset("\uf355", 1.0, null, 0.5);
        public static FontIconsPreset fa_arrow_alt_right = new FontIconsPreset("\uf356", 1.0, null, 0.5);

        public static FontIconsPreset fa_dice_one = new FontIconsPreset("\uf525", 0.9, null, 0.5);

        public static FontIconsPreset fa_images = new FontIconsPreset("\uf302", 1.0, null, 0.5);

        public static FontIconsPreset fa_times_circle = new FontIconsPreset("\uf057", 1.0, null, 0.5);

        /// <summary>
        /// On Map
        /// </summary>
        public static FontIconsPreset fa_map_marked_alt = new FontIconsPreset("\uf5a0", 0.9, null, 0.5);
        /// <summary>
        /// GPS
        /// </summary>
        public static FontIconsPreset fa_map_marker_alt = new FontIconsPreset("\uf3c5", 1.0, null, 0.5);
        /// <summary>
        /// Browse
        /// </summary>
        public static FontIconsPreset fa_browser = new FontIconsPreset("\uf37e", 1.0, null, 0.5);

        /// <summary>
        /// Tweaks and settings
        /// </summary>
        public static FontIconsPreset fa_sliders_h = new FontIconsPreset("\uf1de", 1.0, null, 0.5);

        public static FontIconsPreset fa_wrench = new FontIconsPreset("\uf0ad", 1.0, null, 0.5);

        public static FontIconsPreset fa_cog = new FontIconsPreset("\uf013", 1.1, null, 0.5);

        public static FontIconsPreset fa_exclamation_triangle = new FontIconsPreset("\uf071", 1.0, null, 0.5);


        /// <summary>
        /// Arrow > for menus
        /// </summary>
        public static FontIconsPreset fa_chevron_right = new FontIconsPreset("\uf054", 1.0, null, 0.5);

        public static FontIconsPreset fa_chevron_left = new FontIconsPreset("\uf053", 1.01, null, 0.5);

        public static FontIconsPreset fa_angle_left = new FontIconsPreset("\uf104", 1.2, null, 0.5);

        public static FontIconsPreset fa_caret_left = new FontIconsPreset("\uf0d9", 1.3, null, 0.5);

        public static FontIconsPreset fa_undo_alt = new FontIconsPreset("\uf2ea", 1.0, null, 0.5);


        public static FontIconsPreset fa_search = new FontIconsPreset("\uf002", 1.0, null, 0.5);

        public static FontIconsPreset fa_sort_amount_down = 
            new FontIconsPreset("\uf160", 1.1, null, 0.5);


        /// <summary>
        /// share android
        /// </summary>
        public static FontIconsPreset fa_share_square = new FontIconsPreset("\uf14d", 1.0, null, 0.5);
        /// <summary>
        /// share ios
        /// </summary>
        public static FontIconsPreset fa_share_alt = new FontIconsPreset("\uf1e0", 1.0, null, 0.5);



        // FontAwesome BRANDS

        /// <summary>
        ///  Facebook in brands
        /// </summary>
        public static FontIconsPreset fa_facebook = new FontIconsPreset("\uf082", 1.0, "FaBrands");
        /// <summary>
        ///  VKontakte in brands
        /// </summary>
        public static FontIconsPreset fa_vk = new FontIconsPreset("\uf189", 1.0, "FaBrands");
        /// <summary>
        ///  VKontakte
        /// </summary>
        public static FontIconsPreset fa_instagram = new FontIconsPreset("\uf16d", 1.1, "FaBrands");


        //-----------------------------------------------------------------
        public static FontIconsPreset GetPresetById(string id)
        //-----------------------------------------------------------------
        {
            FontIconsPreset ret = null;
            if (!string.IsNullOrEmpty(id))
            {
                FieldInfo[] properties = typeof(FontIconsPreset).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontIconsPreset))
                    {
                        FontIconsPreset preset = (FontIconsPreset)property.GetValue(null);
                        if (preset.Id.ToLowerInvariant() == id.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        //-----------------------------------------------------------------
        public static string GetPresetName(string id)
        //-----------------------------------------------------------------
        {
            string ret = "";
            if (!string.IsNullOrEmpty(id))
            {
                FieldInfo[] properties = typeof(FontIconsPreset).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontIconsPreset))
                    {
                        FontIconsPreset preset = (FontIconsPreset)property.GetValue(null);
                        if (preset.Id.ToLowerInvariant() == id.ToLowerInvariant())
                        {
                            ret = property.Name;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        //-----------------------------------------------------------------
        public static FontIconsPreset GetPresetByIcon(string unicode)
        //-----------------------------------------------------------------
        {
            FontIconsPreset ret = null;
            if (!string.IsNullOrEmpty(unicode))
            {
                FieldInfo[] properties = typeof(FontIconsPreset).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontIconsPreset))
                    {
                        FontIconsPreset preset = (FontIconsPreset)property.GetValue(null);
                        if (preset.icon.ToLowerInvariant() == unicode.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        //-----------------------------------------------------------------
        public static FontIconsPreset GetPresetByName(string name)
        //-----------------------------------------------------------------
        {
            FontIconsPreset ret = null;
            if (!string.IsNullOrEmpty(name))
            {
                var search = name.Trim().Replace(" ", "_").Replace("-", "_");
                FieldInfo[] properties = typeof(FontIcons).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontIconsPreset))
                    {
                        FontIconsPreset preset = (FontIconsPreset)property.GetValue(null);
                        if (property.Name.ToLowerInvariant() == search.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

    }

}
