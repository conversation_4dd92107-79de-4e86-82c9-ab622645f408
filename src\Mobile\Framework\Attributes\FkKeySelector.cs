﻿using System;

namespace AppoMobi.Framework.Attributes
{
    public class FkKeySelector : Attribute
    {
        public FkKeySelector()
        {
            KeyProperty = "Key";
        }

        public string TitleProperty { get; set; }
        
        public string KeyProperty { get; set; }
        
        /// <summary>
        /// To generate dropdowns for this type
        /// </summary>
        public Type ObjectType { get; set; }
        
        public string LinkMask { get; set; }
        
        public string LinkGenericKey { get; set; }
        
        public bool Nullable { get; set; }

        /// <summary>
        /// Need present a special list instead of dropdown, can be used when you have a huge data not suitable for a small dropdown
        /// </summary>
        public bool SelectFromList { get; set; }

        public string NullDesc { get; set; }

    }
}