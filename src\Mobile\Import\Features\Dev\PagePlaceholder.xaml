﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PageCustomized
    x:Class="AppoMobi.PagePlaceholder"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    Title="{x:Static resX:ResStrings.PageTitleSettings}"
    BackgroundColor="{x:Static appoMobi:AppColors.PrimaryDark}"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}">




    <pages:PageEnhancedNav.InsertContent>
        <pages:IncludedContent
            Margin="3"
            Padding="8,7,8,0"
            HorizontalOptions="FillAndExpand">

            <Grid VerticalOptions="FillAndExpand">

                <forms:CachedImage
                    x:Name="imgUC"
                    Aspect="AspectFit"
                    HeightRequest="125"
                    HorizontalOptions="Center"
                    Opacity="0.5"
                    Source="{ui:ImageFromResources UI.uc.png}"
                    VerticalOptions="Center"
                    WidthRequest="125" />


            </Grid>




        </pages:IncludedContent>
    </pages:PageEnhancedNav.InsertContent>

</pages:PageCustomized>







