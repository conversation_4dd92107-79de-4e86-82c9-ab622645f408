﻿namespace AppoMobi.Framework.Api
{
    public class DataListRequestOptions 
    {
        public DataListRequestOptions()
        {
            PageSize = 20;
            PageNb = 0;
        }

        public int PageSize { get; set; }

        public int PageNb { get; set; }

        public string Filter { get; set; }

        public string Order { get; set; }

        public string Search { get; set; }

        public string SearchFields { get; set; }

        public bool FullDto { get; set; }

        public bool Randomize { get; set; }

        /// <summary>
        /// from+max(inclusive)+lastRandom
        /// </summary>
        public int? DoNotRepeatRandomChecksum { get; set; }

        public void SetSearchFieldsIfEmpty(string defaultValue)
        {
            if (string.IsNullOrEmpty(SearchFields))
                SearchFields = defaultValue;
        }
    }
}