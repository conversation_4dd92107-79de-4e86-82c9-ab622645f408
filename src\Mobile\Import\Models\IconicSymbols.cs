﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace AppoMobi
{
    //*************************************************************
    public class IconicSymbols
    //*************************************************************
    {

        protected string AssemblyName { get; set; }
        protected List<SymbolEntry> Entries { get; set; }

        
        public IconicSymbols(string filesSubPath)
        
        {
            var assembly = typeof(App).GetTypeInfo().Assembly;
            AssemblyName = assembly.GetName().Name;

            Entries=new List<SymbolEntry>();

            Init(filesSubPath);
        }

        
        public void Init(string filesSubPath)
        
        {
            var assembly = typeof(App).GetTypeInfo().Assembly;
           // var generatedFilename = AssemblyName + ".Images.flags.flag_" + item.CountryCode?.ToLower() + @".jpg"; //@".png";

            foreach (var res in assembly.GetManifestResourceNames())
            {
                if (res.Contains(filesSubPath))
                {
                    var fn = Path.GetFileNameWithoutExtension(res);
                    fn=fn.Split('.').Last();
                    var item = new SymbolEntry(fn.ToLower(),res);
                    Entries.Add(item);
                }
            }

            return;
        }

        
        public string GetFilename(string symbol, string size="large")
        
        {
            var items = Entries.Where(x => x.Symbol == symbol.ToLower());            
            var item = items?.FirstOrDefault(x => x.Filename.Contains("."+size+"."));
            if (item == null)
                item = items?.FirstOrDefault();
            return item?.Filename;
        }

        //*************************************************************
        protected class SymbolEntry
        //*************************************************************
        {
            public string Filename { get; set; }
            public string Symbol { get; set; }
            
            public SymbolEntry(string symbol, string filename)
            
            {
                Filename = filename;
                Symbol = symbol;
            }
        }



    }
}
