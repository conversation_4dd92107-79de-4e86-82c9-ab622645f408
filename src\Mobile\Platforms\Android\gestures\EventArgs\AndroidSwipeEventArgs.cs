using Android.Views;
using AppoMobi.Touch;
using System;
using Xamarin.Forms;
using View = Android.Views.View;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidSwipeEventArgs : SwipeEventArgs
	{
		public AndroidSwipeEventArgs(MotionEvent previous, MotionEvent current, PanEventArgs prevArgs, View view, AppoMobi.Touch.Direction direction)
		{
			this.Cancelled = current.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(current);
			base.Direction = direction;
			base.CalculateDistances(prevArgs);
			if (this.DeltaDistance.IsEmpty && prevArgs != null)
			{
				this.DeltaDistance = prevArgs.DeltaDistance;
			}
			this.Velocity = this.GetVelocity(previous, current);
		}

		private Point GetVelocity(MotionEvent previous, MotionEvent current)
		{
			if (previous == null)
			{
				return new Point(0, 0);
			}
			Point deltaDistance = this.DeltaDistance;
			double eventTime = (double)(current.EventTime - previous.EventTime);
			return new Point(deltaDistance.X * 1000 / eventTime, deltaDistance.Y * 1000 / eventTime);
		}
	}
}