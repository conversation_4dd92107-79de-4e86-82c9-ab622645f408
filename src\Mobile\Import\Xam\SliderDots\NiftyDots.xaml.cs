﻿using System.Collections.Generic;



namespace AppoMobi.Xam
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NiftyDots 
    {
        
        public NiftyDots()
        
        {
            InitializeComponent();
            //PaddingWidth = "0";
            Total = 1;
            BindingContext = this;
        }

        public NiftyObservableCollection<NiftyDot> DotsList { get; } = new NiftyObservableCollection<NiftyDot>();


        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameSelected:
                    ChangeSelection();//btnNifty.Text = Caption;//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;

                case nameTotal:
                case nameDotSize:
                case nameColorAccent:
                case nameColorPrimary:
                    BuildTotal();
                    break;


            }

        }


        
        
        // ColorPrimary
        
        private const string nameColorPrimary = "ColorPrimary";
        public static readonly BindableProperty ColorPrimaryProperty = BindableProperty.Create(nameColorPrimary, typeof(Color), typeof(NiftyDots), Colors.AliceBlue); //, BindingMode.TwoWay
        public Color ColorPrimary
        {
            get { return (Color)GetValue(ColorPrimaryProperty); }
            set { SetValue(ColorPrimaryProperty, value); }
        }


        
        // ColorAccent
        
        private const string nameColorAccent = "ColorAccent";
        public static readonly BindableProperty ColorAccentProperty = BindableProperty.Create(nameColorAccent, typeof(Color), typeof(NiftyDots), Colors.Red); //, BindingMode.TwoWay
        public Color ColorAccent
        {
            get { return (Color)GetValue(ColorAccentProperty); }
            set { SetValue(ColorAccentProperty, value); }
        }	



        
        // Total of dots
        
        private const string nameDotSize = "DotSize";
        public static readonly BindableProperty DotSizeProperty = BindableProperty.Create(nameDotSize, typeof(int), typeof(NiftyDots), 2);
        public int DotSize
        {
            get { return (int)GetValue(DotSizeProperty); }
            set { SetValue(DotSizeProperty, value); }
        }
        
        // Total of dots
        
        private const string nameTotal = "Total";
        public static readonly BindableProperty TotalProperty = BindableProperty.Create(nameTotal, typeof(int), typeof(NiftyDots), 1);
        public int Total
        {
            get { return (int)GetValue(TotalProperty); }
            set { SetValue(TotalProperty, value); }
        }
        
        // Selected dot
        
        private const string nameSelected = "Selected";
        public static readonly BindableProperty SelectedProperty = BindableProperty.Create(nameSelected, typeof(int), typeof(NiftyDots), -1);
        public int Selected
        {
            get { return (int)GetValue(SelectedProperty); }
            set { SetValue(SelectedProperty, value); }
        }
        

        
        private void BuildTotal()
        
        {           
            var newList = new List<NiftyDot>();
            for (int a = 0; a < Total; a++)
            {

                var dot = new NiftyDot
                {
                    Id = a.ToString(),
                    DotSize = DotSize,
                    ColorPrimary = ColorPrimary,
                    ColorAccent = ColorAccent
                };
                if (a == Selected)
                {
                    dot.Selected = true;
                }
            newList.Add(dot);
            }
            DotsList.Clear();
            DotsList.AddRange(newList);
            Selected = 0;
        }


        
        private void ChangeSelection()
        
        {

            if (Selected < 0 || Selected > DotsList.Count)
            {
                Selected = 0;
                return;
            }          
            for (int a = 0; a < DotsList.Count; a++)
            {
                if (a != Selected && DotsList[a].Selected)
                {
                    DotsList[a].Selected = false;
                    break;
                }
            }
            DotsList[Selected].Selected = true;
        }

        
        private void ChangeDotSize()
        
        {
            for (int a = 0; a < DotsList.Count; a++)
            {
                    DotsList[a].DotSize = DotSize;
                    break;
            }
        }

    
        //============================================================================
        public class NiftyDot : BindableObject
        //============================================================================
        {
            public string Id { get; set; }

            private bool _Selected;
            public bool Selected
            {
                get { return _Selected; }
                set
                {
                    if (_Selected != value)
                    {
                        _Selected = value;
                        OnPropertyChanged();
                        OnPropertyChanged("NotSelected");
                    }
                }
            }

            private double _DotSize;
            public double DotSize
            {
                get { return _DotSize; }
                set
                {
                    if (_DotSize != value)
                    {
                        _DotSize = value;
                        OnPropertyChanged();
                    }
                }
            }

            private Color _ColorPrimary;
            public Color ColorPrimary
            {
                get { return _ColorPrimary; }
                set
                {
                    if (_ColorPrimary != value)
                    {
                        _ColorPrimary = value;
                        OnPropertyChanged();
                    }
                }
            }



            private Color _ColorAccent;
            public Color ColorAccent
            {
                get { return _ColorAccent; }
                set
                {
                    if (_ColorAccent != value)
                    {
                        _ColorAccent = value;
                        OnPropertyChanged();
                    }
                }
            }



            public bool NotSelected
            {
                get
                {
                    return !Selected;
                }
            }
        }

      


    }
}