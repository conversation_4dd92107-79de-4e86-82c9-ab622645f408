﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class CompareEnum : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var ret = true;

            try
            {
                var iVisualStep = int.Parse((string)parameter);

                var iStep = (int)value;

                if (iStep != iVisualStep)
                    ret = false;

                return ret;
            }
            catch (Exception e)
            {
            }

            try
            {
                ret = false;
                var ints = ((string)parameter).Split(',');
                foreach (var number in ints)
                {
                    var iStep = int.Parse(number);
                    if (iStep == (int)value)
                    {
                        ret = true;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
            }

            return ret;
        }


    }
}