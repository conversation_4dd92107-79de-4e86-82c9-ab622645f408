﻿using System;
using System.Threading.Tasks;

using FFImageLoading.Maui;
using FFImageLoading.Transformations;
using AppoMobi.Touch;
using AppoMobi.Xam;
using AppoMobi.Xam.Antispam;



namespace AppoMobi.UI
{
    /// <summary>
    /// Custom button with in image left of its caption. Originating from a MR Gestures Frame.
    /// </summary>
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ArrowButton
    {

        public enum LayoutPosition
        {
            Left,
            Right
        }


        public ArrowButton()
        {
            InitializeComponent();
            InitGesturesForCell(SelectionBox);

            FontSize = 15;

            //var assembly = typeof(NiftyImageButton).GetTypeInfo().Assembly;
            //var AssemblyName = assembly.GetName().Name;
            //var generatedFilename = AssemblyName + ".Images.btn." + "fxbtn.png";

            //imgBack.Source = ImageSource.FromResource(generatedFilename);
            cMyArrow.Font = "FaLight";
            cMyArrow.SetIcon(FontIcons.fa_chevron_right);

        }

        
        private bool lock_down { get; set; }
        private void NiftyImageButton_OnDown(object sender, DownUpEventArgs e)
        
        {
            if (lock_down) return;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                lock_down = true;
                // imgFx.FadeTo(0.75, 75);
                try
                {
                    await cSelectedGrid.ScaleTo(0.85, 75);
                    await cSelectedGrid.ScaleTo(0.90, 75);
                    await cSelectedGrid.ScaleTo(1, 75);
                }
                catch (Exception exception)
                {
                }
                await Task.Delay(75*3);
                lock_down = false;
            });
        }

        
        // Clicked
        
        public event EventHandler Clicked = null;

        
        private AntiSpamClick Clicker_Grid_OnTapped;

        private void OnTapped_Frame(object sender, EventArgs e)
            
        {
            if (Clicker_Grid_OnTapped == null)
            {
                Clicker_Grid_OnTapped = new AntiSpamClick();
                Clicker_Grid_OnTapped.ClickFunc += async (sEnder, pArams) =>
                {
                    

                    Clicked?.Invoke(this, EventArgs.Empty);

                    
                };
            }

            Clicker_Grid_OnTapped.Click(sender, e);
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            var imgControl = GetImageControl();

            if (imgControl == null) return; //startup

            switch (propertyName)
            {

                //Label props
                case nameText:
                    ControlLabel.Text = Text; //DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;                    
                    break;
                case nameSelected:
                    txtSelected.Text = Selected; //DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    //txtSelectedBack.Text = txtSelected.Text;
                    break;
                case nameFormattedText:
                    ControlLabel.FormattedText = FormattedText;
                    break;
                case nameTextColor:
                    ControlLabel.TextColor = TextColor;
                    break;
                case nameSelectedTextColor:
                    txtSelected.TextColor = SelectedTextColor;
                    //txtSelectedBack.TextColor = txtSelected.TextColor.MakeDarker(10); ;
                    //cMyArrow.TextColor = SelectedTextColor;
                    break;
                case nameFontAttributes:
                    ControlLabel.FontAttributes = FontAttributes;
                    break;
                case nameFontFamily:
                    ControlLabel.FontFamily = FontFamily;
                    break;
                case nameFontSize:
                    ControlLabel.FontSize = FontSize;
                    break;
                //property changed
                case nameHorizontalTextAlignment:
                    ControlLabel.HorizontalTextAlignment = HorizontalTextAlignment;
                    break;

                case nameFxSource:
                    imgBack.Source = FxSource;
                    break;

                //Image props
                case nameImageSource:
                    if (ImageSource != null)
                    {
                        try
                        {
                            imgControl.Source = ImageSource;
                            imgControl.IsVisible = true;
                        }
                        catch
                        {

                        }
                    }

                    break;





                case nameImageHeightRequest:
                    imgControl.HeightRequest = ImageHeightRequest;
                    imgControl.MinimumHeightRequest = ImageHeightRequest;


                    break;
                case nameImageWidthRequest:
                    imgControl.WidthRequest = ImageWidthRequest;

                    break;

                //property changed
                case nameImageAspect:
                    imgControl.Aspect = ImageAspect;
                    break;

                //Layout props
                case nameSpacing:
                    ControlLayout.ColumnSpacing = Spacing;
                    break;

                case nameImageTintColor:
                    imgControl.Transformations.Clear();
                    if (ImageTintColor != null && ImageTintColor != Colors.Transparent)
                    {
                        var hex = ImageTintColor.ToHex();
                        var tint = new TintTransformation(hex);
                        tint.EnableSolidColor = true;
                        imgControl.Transformations.Add(tint);
                    }

                    break;

                //property changed
                case nameImageMargin:
                    ControlImageLeft.Margin = ImageMargin;
                    ControlImageRight.Margin = ImageMargin;
                    break;


                case nameImagePosition:
                    if (ImagePosition == LayoutPosition.Left)
                    {
                        ControlImageRight.IsVisible = false;
                    }
                    else if (ImagePosition == LayoutPosition.Right)
                    {
                        ControlImageLeft.IsVisible = false;
                    }

                    OnPropertyChanged(nameImageHeightRequest);
                    OnPropertyChanged(nameImageWidthRequest);
                    OnPropertyChanged(nameImageSource);
                    OnPropertyChanged(nameImageTintColor);
                    break;
                //property changed

                case nameUppercase:
                    OnPropertyChanged(nameText);
                    break;

            }

        }

        
        private ref CachedImage GetImageControl()
            
        {
            if (ImagePosition == LayoutPosition.Left)
            {
                return ref ControlImageLeft;
            }
            else // if (ImagePosition == LayoutPosition.Right)
            {
                return ref ControlImageRight;
            }
        }


        
        // ImageAspect
        
        private const string nameImageAspect = "ImageAspect";

        public static readonly BindableProperty ImageAspectProperty =
            BindableProperty.Create(nameImageAspect, typeof(Aspect), typeof(NiftyImageButton),
                Aspect.Fill); //, BindingMode.TwoWay

        public Aspect ImageAspect
        {
            get { return (Aspect) GetValue(ImageAspectProperty); }
            set { SetValue(ImageAspectProperty, value); }
        }


        
        // ImageMargin
        
        private const string nameImageMargin = "ImageMargin";

        public static readonly BindableProperty ImageMarginProperty = BindableProperty.Create(nameImageMargin,
            typeof(Thickness), typeof(NiftyImageButton), new Thickness(0)); //, BindingMode.TwoWay

        public Thickness ImageMargin
        {
            get { return (Thickness) GetValue(ImageMarginProperty); }
            set { SetValue(ImageMarginProperty, value); }
        }


        
        // Uppercase
        
        private const string nameUppercase = "Uppercase";

        public static readonly BindableProperty UppercaseProperty =
            BindableProperty.Create(nameUppercase, typeof(bool), typeof(NiftyImageButton),
                false); //, BindingMode.TwoWay

        public bool Uppercase
        {
            get { return (bool) GetValue(UppercaseProperty); }
            set { SetValue(UppercaseProperty, value); }
        }



        
        // ImagePosition
        
        private const string nameImagePosition = "ImagePosition";

        public static readonly BindableProperty ImagePositionProperty = BindableProperty.Create(nameImagePosition,
            typeof(LayoutPosition), typeof(NiftyImageButton), LayoutPosition.Left); //, BindingMode.TwoWay

        public LayoutPosition ImagePosition
        {
            get { return (LayoutPosition) GetValue(ImagePositionProperty); }
            set { SetValue(ImagePositionProperty, value); }
        }



        
        // HorizontalTextAlignment
        
        private const string nameHorizontalTextAlignment = "HorizontalTextAlignment";

        public static readonly BindableProperty HorizontalTextAlignmentProperty =
            BindableProperty.Create(nameHorizontalTextAlignment, typeof(TextAlignment), typeof(NiftyImageButton),
                TextAlignment.Start); //, BindingMode.TwoWay

        public TextAlignment HorizontalTextAlignment
        {
            get { return (TextAlignment) GetValue(HorizontalTextAlignmentProperty); }
            set { SetValue(HorizontalTextAlignmentProperty, value); }
        }



        
        // Spacing
        
        private const string nameSpacing = "Spacing";

        public static readonly BindableProperty SpacingProperty =
            BindableProperty.Create(nameSpacing, typeof(double), typeof(NiftyImageButton), 8.0d); //, BindingMode.TwoWay

        public double Spacing
        {
            get { return (double) GetValue(SpacingProperty); }
            set { SetValue(SpacingProperty, value); }
        }



        
        // FontAttributes
        
        private const string nameFontAttributes = "FontAttributes";

        public static readonly BindableProperty FontAttributesProperty = BindableProperty.Create(nameFontAttributes,
            typeof(FontAttributes), typeof(NiftyImageButton), new FontAttributes()); //, BindingMode.TwoWay

        public FontAttributes FontAttributes
        {
            get { return (FontAttributes) GetValue(FontAttributesProperty); }
            set { SetValue(FontAttributesProperty, value); }
        }

        
        // FontSize
        
        private const string nameFontSize = "FontSize";

        public static readonly BindableProperty FontSizeProperty =
            BindableProperty.Create(nameFontSize, typeof(double), typeof(NiftyImageButton), 1.0d);

        public double FontSize
        {
            get { return (double) GetValue(FontSizeProperty); }
            set { SetValue(FontSizeProperty, value); }
        }


        
        // FontFamily
        
        private const string nameFontFamily = "FontFamily";

        public static readonly BindableProperty FontFamilyProperty =
            BindableProperty.Create(nameFontFamily, typeof(string), typeof(NiftyImageButton),
                string.Empty); //, BindingMode.TwoWay

        public string FontFamily
        {
            get { return (string) GetValue(FontFamilyProperty); }
            set { SetValue(FontFamilyProperty, value); }
        }

        
        // TextColor
        
        private const string nameTextColor = "TextColor";

        public static readonly BindableProperty TextColorProperty =
            BindableProperty.Create(nameTextColor, typeof(Color), typeof(NiftyImageButton),
                Colors.Black); //, BindingMode.TwoWay

        public Color TextColor
        {
            get { return (Color) GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }

        
        // SelectedTextColor
        
        private const string nameSelectedTextColor = "SelectedTextColor";

        public static readonly BindableProperty SelectedTextColorProperty =
            BindableProperty.Create(nameSelectedTextColor, typeof(Color), typeof(NiftyImageButton),
                Colors.Black); //, BindingMode.TwoWay

        public Color SelectedTextColor
        {
            get { return (Color)GetValue(SelectedTextColorProperty); }
            set { SetValue(SelectedTextColorProperty, value); }
        }

        
        // FormattedText
        
        private const string nameFormattedText = "FormattedText";

        public static readonly BindableProperty FormattedTextProperty =
            BindableProperty.Create(nameFormattedText, typeof(string), typeof(NiftyImageButton),
                string.Empty); //, BindingMode.TwoWay

        public string FormattedText
        {
            get { return (string) GetValue(FormattedTextProperty); }
            set { SetValue(FormattedTextProperty, value); }
        }

        
        // Text
        
        private const string nameText = "Text";

        public static readonly BindableProperty TextProperty =
            BindableProperty.Create(nameText, typeof(string), typeof(NiftyImageButton),
                string.Empty); //, BindingMode.TwoWay

        public string Text
        {
            get
            {
                if (!Uppercase) return (string) GetValue(TextProperty);
                else
                {
                    var s = (string) GetValue(TextProperty);
                    return s.ToUpper();
                }
            }
            set { SetValue(TextProperty, value); }
        }

        
        // Selected
        
        private const string nameSelected = "Selected";

        public static readonly BindableProperty SelectedProperty =
            BindableProperty.Create(nameSelected, typeof(string), typeof(NiftyImageButton),
                string.Empty); //, BindingMode.TwoWay

        public string Selected
        {
            get
            {
                if (!Uppercase) return (string)GetValue(SelectedProperty);
                else
                {
                    var s = (string)GetValue(SelectedProperty);
                    return s.ToUpper();
                }
            }
            set { SetValue(SelectedProperty, value); }
        }

        
        // ImageSource
        
        private const string nameImageSource = "ImageSource";

        public static readonly BindableProperty ImageSourceProperty =
            BindableProperty.Create(nameImageSource, typeof(ImageSource), typeof(NiftyImageButton),
                null); //, BindingMode.TwoWay

        public ImageSource ImageSource
        {
            get { return (ImageSource) GetValue(ImageSourceProperty); }
            set { SetValue(ImageSourceProperty, value); }
        }

        
        // FxSource
        
        private const string nameFxSource = "FxSource";

        public static readonly BindableProperty FxSourceProperty =
            BindableProperty.Create(nameFxSource, typeof(ImageSource), typeof(NiftyImageButton),
                null); //, BindingMode.TwoWay

        public ImageSource FxSource
        {
            get { return (ImageSource) GetValue(FxSourceProperty); }
            set { SetValue(FxSourceProperty, value); }
        }


        
        // LeftAccessorySource
        
        private const string nameLeftAccessorySource = "LeftAccessorySource";

        public static readonly BindableProperty LeftAccessorySourceProperty =
            BindableProperty.Create(nameLeftAccessorySource, typeof(ImageSource), typeof(NiftyImageButton),
                null); //, BindingMode.TwoWay

        public ImageSource LeftAccessorySource
        {
            get { return (ImageSource) GetValue(LeftAccessorySourceProperty); }
            set { SetValue(LeftAccessorySourceProperty, value); }
        }


        
        // RightAccessorySource
        
        private const string nameRightAccessorySource = "RightAccessorySource";

        public static readonly BindableProperty RightAccessorySourceProperty =
            BindableProperty.Create(nameRightAccessorySource, typeof(ImageSource), typeof(NiftyImageButton),
                null); //, BindingMode.TwoWay

        public ImageSource RightAccessorySource
        {
            get { return (ImageSource) GetValue(RightAccessorySourceProperty); }
            set { SetValue(RightAccessorySourceProperty, value); }
        }


        
        // ImageHeightRequest
        
        private const string nameImageHeightRequest = "ImageHeightRequest";

        public static readonly BindableProperty ImageHeightRequestProperty =
            BindableProperty.Create(nameImageHeightRequest, typeof(double), typeof(NiftyImageButton),
                24.0d); //, BindingMode.TwoWay

        public double ImageHeightRequest
        {
            get { return (double) GetValue(ImageHeightRequestProperty); }
            set { SetValue(ImageHeightRequestProperty, value); }
        }

        
        // ImageWidthRequest
        
        private const string nameImageWidthRequest = "ImageWidthRequest";

        public static readonly BindableProperty ImageWidthRequestProperty =
            BindableProperty.Create(nameImageWidthRequest, typeof(double), typeof(NiftyImageButton),
                24.0d); //, BindingMode.TwoWay

        public double ImageWidthRequest
        {
            get { return (double) GetValue(ImageWidthRequestProperty); }
            set { SetValue(ImageWidthRequestProperty, value); }
        }


        
        // ImageTintColor
        
        private const string nameImageTintColor = "ImageTintColor";

        public static readonly BindableProperty ImageTintColorProperty =
            BindableProperty.Create(nameImageTintColor, typeof(Color), typeof(NiftyImageButton),
                Colors.Transparent); //, BindingMode.TwoWay

        public Color ImageTintColor
        {
            get { return (Color) GetValue(ImageTintColorProperty); }
            set { SetValue(ImageTintColorProperty, value); }
        }

        private void OnImageErrors(object sender, CachedImageEvents.ErrorEventArgs e)
        {


        }

        private void ImgBack_OnError(object sender, CachedImageEvents.ErrorEventArgs e)
        {
            var stop = 1; //  throw new NotImplementedException();
        }

        private void ImgBack_OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        {
            var stop = 1; //  throw new NotImplementedException();
        }

        public void Refresh()
        {
            return;
            imgBack.Source = null;
            imgBack.Source = FxSource;
        }

        
        private void VisualElement_OnSizeChanged(object sender, EventArgs e)
            
        {
            var view = (View)sender;
            if (view.Height > 0 && Height < AppUI.MinimumHeightNormal)
                view.HeightRequest = AppUI.MinimumHeightNormal;
        }
    }
}