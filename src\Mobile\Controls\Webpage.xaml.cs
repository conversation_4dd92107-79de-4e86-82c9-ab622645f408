﻿using System;
using System.Diagnostics;
using System.Windows.Input;
using AppoMobi.Common.Constants;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;





namespace AppoMobi.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Webpage : PageCustomized
    {

        public void EnableZoom()
        {
            MyBrowser.EnableZoom();
        }

        public void DisableZoom()
        {
            MyBrowser.DisableZoom();
        }

        
        private bool _isLoading = true;
        public bool IsLoading
        {
            get { return _isLoading; }

            private set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Tel { get; set; } = "";

        
        public override void ReleaseOnClosing()
        
        {
          //  App.Instance.Messager.Unsubscribe(this, "Keyboard");

            base.ReleaseOnClosing();
        }

        
        public override double OnKeyboardResized(double size)
        
        {
            GetContainer().Margin = new Thickness(0, 0, 0, size);
   

            return base.OnKeyboardResized(size);
        }

        
        public Webpage(string url, string title, string tel)
        
        {
            InitializeComponent();

         
            Title = title;

            if (!string.IsNullOrEmpty(tel))
            {
                Tel = tel;

                RightIcon1Symbol.SetIcon(FontIcons.fa_phone);
                RightIcon1Symbol.RotationY = 180;
                ToggleButtonVisibility(ButtonType.Right1, true);
                //cNavBar.RightIcon1Source = "resource://AppoMobi.Mobile.Images.Navbar.phones.png";
                //cNavBar.ToggleButtonVisibility(ButtonType.Right1, true);
            }
            else
            {

            }
            ToggleButtonVisibility(ButtonType.Right2, false);
            RightIcon2Symbol.SetIcon(FontIcons.arrow_alt_circle_left);
       

            if (string.IsNullOrEmpty(url))
            {
                url = string.Format(Constants.ServerRedirectLinkMask, TenantOptions.TenantKey, "site");
            }


            bool secure = url.ToLower().Contains("https");

            var myurl = url.Replace(@"http://", "");
            myurl = myurl.Replace(@"http//", "");
            myurl = myurl.Replace(@"https://", "");

            if (secure)
                myurl = @"https://" + myurl;
            else
                myurl = @"http://" + myurl;

            Uri uri = new Uri(myurl);

     

            try
            {
                MyBrowser.Source = uri; //;

            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            ////================================================
            //App.Instance.Messager.Subscribe<string>(this, "Keyboard", async (sender, arg) =>
            ////================================================
            //{
            //    switch (arg)
            //    {

            //        case "Visible":
 
            //            break;

            //        case "Hidden":
      
            //            break;
            //    }
            //});

        }

        
        ICommand telCommand;
        public ICommand TelCommand =>
            telCommand ?? (telCommand = new Command(TelClicked));
        
        void TelClicked()
        
        {
            //var World = new CWorld();
            
            Core.Dial(Tel);
        }
        
        private int _navigated = 0;
        private void MyBrowser_OnNavigating(object sender, WebNavigatingEventArgs e)
        {
//            Indicator.IsVisible = true;
            IsLoading = true;
        }
        
        protected override void OnSizeAllocated(double width, double height)
        {
            base.OnSizeAllocated(width, height);

            var testmeh = IsLoading;

            return;
        }
        
        private void MyBrowser_OnNavigated(object sender, WebNavigatedEventArgs e)
        {
            IsLoading = false;
            //Indicator.IsVisible = false;
            _navigated++;
            if (_navigated > 2)
            {

                ToggleButtonVisibility(ButtonType.Right2, true);


                //ToolbarItems.Add(new ToolbarItem
                //{
                //    Order = ToolbarItemOrder.Primary,
                //    Icon = "tbback",
                //    Text = ResStrings.WebBack,
                //    Command = BackCommand

                //});

            }

        }
        
        ICommand backCommand;
        public ICommand BackCommand => backCommand ?? (backCommand = new Command(BrowserGoBack));
        
        void BrowserGoBack()
        
        {
            try
            {
                if (MyBrowser.CanGoBack) MyBrowser.GoBack();
            }
            catch
            {

            }
            //World.Dial(ResStrings.OwnerTel);
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
           
        }

        public override async void OnRightIcon1Clicked()
        {
            TelClicked();
        }

        public override async void OnRightIcon2Clicked()
        {
            BrowserGoBack();
        }



    }
}