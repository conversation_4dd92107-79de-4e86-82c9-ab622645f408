﻿using System;
using AppoMobi.Touch;



namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SearchDesc 
    {
        
        public SearchDesc()
        
        {
            InitializeComponent();
        }

        
        private void OnTapped_ResetSearch(object sender, TapEventArgs e)
        
        {
            Clicked?.Invoke(this,null);
        }


        
        // Text
        
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(SearchDesc), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }	

       ////property changed
	


        #region EventHandlers

        public EventHandler Clicked { get; set; }

        #endregion


        // DO NOT CLOSE
        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {

                //property changed

                case nameText:
                    txtLabel.Text = Text;
                    // Text="{Binding SearchStringDesc}"
                    break;	


            }

        }
    }
}