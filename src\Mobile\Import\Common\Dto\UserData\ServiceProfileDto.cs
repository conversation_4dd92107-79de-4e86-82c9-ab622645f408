﻿using System;
using System.Collections.Generic;
using AppoMobi.Framework.Api;
using AppoMobi.Common.Dto.Reviews;
using AppoMobi.Common.Enums.System;
using AppoMobi.Common.Enums.UserData;

namespace AppoMobi.Common.Dto.UserData
{
    public class ServiceProfileDto : ServiceProfileUpdatableDto
    {
        public ServiceProfileDto()
        {
            CalculatedRating = 4.0;
        }

        public string Role { get; set; }

        public int ReviewsCount { get; set; }
        
        public string RatingDetails { get; set; }

        public List<ReviewDto> Reviews { get; set; }

        public double CalculatedRating { get; set; }

        public int? TimesShared { get; set; }

        public int? TimesViewed { get; set; }

        public bool CanReview { get; set; }

        public bool IsFav { get; set; }

        public string Notes { get; set; }

        public string RatesRoles { get; set; }

        public bool IsVerified { get; set; }

        public ConfirmationStatusType Status { get; set; }

        public DateTime? StatusChangedTime { get; set; }

    }

    public class ServiceProfileUpdatableDto : BaseFrameworkDto
    {
        public List<FinancialRateDto> FinancialRates { get; set; }

        public string Languages { get; set; }

        public string AboutMe { get; set; }

        public decimal MinutePrice { get; set; }

        public ProUserType RequestedRate { get; set; }

        public string AttachUploads { get; set; }

        public string UserComments { get; set; }

    }
}