﻿using System;

namespace AppoMobi.Framework.Api
{
    public class ApiRequestPagedWithOptionalDatesDto : ApiRequestPagedDto
    {
        public DateTime? TimeStart { get; set; }

        public DateTime? TimeEnd { get; set; }
    }

    public class ApiRequestPagedPeopleDto : ApiRequestPagedDto
    {
        public bool? FriendsOnly { get; set; }

        public bool ExcludeMyself { get; set; }
    }
}