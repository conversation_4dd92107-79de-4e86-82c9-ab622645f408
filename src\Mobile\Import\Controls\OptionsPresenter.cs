﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Xam;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;



namespace AppoMobi.Forms.Controls
{
    public class OptionsPresenter
    {
        public OptionsPresenter(List<OptionsListItem> options)
        {
            OptionList = options;
        }



        public async Task<OptionsListItem> ShowGetOne(string title)
        {
            //output list
            var buidList = new Dictionary<string, bool>();
            foreach (var item in OptionList)
            {
                buidList.Add(item.ActionDesc, item.SwitchValue);
            }

            var dialog = new MultiSelectionList(title, buidList,
                ResStrings.ButtonCancel, false);

            Dictionary<string, bool> retList = await dialog.ShowAsync(false);

            KeyValuePair<string, bool> newSelected;
            foreach (var item in retList)
            {
                if (item.Value)
                {
                    var existing = OptionList.FirstOrDefault(x => x.ActionDesc == item.Key);
                    if (existing != null)
                    {
                        return existing;
                    }
                }
            }

            return null;
        }


        public async Task<List<OptionsListItem>> ShowGetMany(string title)
        {
            //output list
            var buidList = new Dictionary<string, bool>();
            foreach (var item in OptionList)
            {
                buidList.Add(item.ActionDesc, item.SwitchValue);
            }

    
            //show view
            var dialog = new MultiSelectionList(title, buidList,
                ResStrings.ButtonCancel, true);

            //dialog.Test();
 
            
            Dictionary<string, bool> retList = await dialog.ShowAsync(false);

            if (retList != null)
            {
                var ret = new List<OptionsListItem>();
                KeyValuePair<string, bool> newSelected;
                foreach (var item in retList)
                {
                    var existing = OptionList.FirstOrDefault(x => x.ActionDesc == item.Key);
                    if (existing != null)
                    {
                        existing.SwitchValue = item.Value;
                    }
                }

                return OptionList;
            }
        

            return new List<OptionsListItem>();
        }

        private List<OptionsListItem> OptionList;
    }
}
