﻿using AppoMobi;
using AppoMobi.Maui.Gestures;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Framework.Maui.Touch;
using AppoMobi.Maui.Navigation;
using AppoMobi.Models;
using AppoMobi.Tenant;
using AppoMobi.ViewModels.Navigation;
using System.Diagnostics;
using Designer = AppoMobi.Framework.Maui.Designer;

namespace AppoMobi.Mobile.Views
{

    //startup tab not selected at once, switching from 0
    //reciprocity wheels not rendering
    //developer ui fucked up
    //camera not activated
    //pop animation not removing view over tab


    public partial class PageTabs : BasePage, ISupportsLazyViews
    {

        #region ROOT

        public static Color OverrideBarTextColor { get; set; } = Colors.Black;

        //public ObservableRangeCollection<TabbedMenuItem> TabbedMenuItems { get; private set; } = new();
        //public ObservableRangeCollection<View> Pages { get; private set; } = new();

        public IPageEnhanced PrevPage { get; protected set; } = null;
        public bool Rendered { get; internal set; }

        private bool initmenuonce;
        private bool _Startup;
        private bool _tabchanged { get; set; }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            if (ViewSwitcher != null)
            {
                ViewSwitcher.OnDisappearing();
            }
        }

        private bool appeared; //fixing maui not sending onappearing
        protected override void OnSizeAllocated(double _width, double _height)
        {
            base.OnSizeAllocated(_width, _height);

            if (!appeared && _width > 0 || _height > 0)
            {
                OnAppearing();
            }
        }

        void UpdateControls(DeviceRotation orientation)
        {
            Switcher?.UpdateControls(orientation);
            BottomInsets.HeightRequest = Super.Screen.BottomInset;
        }

        protected override void OnAppearing()
        {
            appeared = true;

            base.OnAppearing();

            if (ViewSwitcher != null)
            {
                ViewSwitcher.OnAppearing();
                if (!once)
                {
                    once = true;
                    App.Instance.UI.HideAllPopups();
                    SelectWishTab();
                }
            }
        }


        protected void OnChangedTab(bool force = true)
        {
            _tabchanged = true;

            //Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific.Page.SetPrefersStatusBarHidden(App.Shell, StatusBarHiddenMode.True);
            //Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific.Page.SetUseSafeArea(App.Shell, false);

#if IOS
            //UIKit.UIApplication.SharedApplication.StatusBarHidden = true
            //var frame = UIKit.UIApplication.SharedApplication.Windows.Last().Frame;

            //var view = Handler.PlatformView as UIKit.UIView;
            //var check = view.Frame;
            //view.Frame = frame;

#endif

            var index = ViewSwitcher.SelectedIndex;

            if (index == Settings.Current.GetValueOrDefault("LastTab", 0))
                _tabchanged = false;

            //if (Globals.Values.TabsMain != null)
            //{
            //    if (Globals.Values.TabsMain.SelectionOff)
            //    {
            //        App.Instance.Messager.All("PopToRoot", "");
            //        Core.Current.PopToRoot().ConfigureAwait(false);
            //    }
            //}

            //todo set ActiveTab property:
            var CurrentPage = Switcher.ActiveView;

            if (CurrentPage != null || force)
            {
                dynamic selectedPage = null;

                if (CurrentPage is ILazyView)
                {
                    selectedPage = ((ILazyView)CurrentPage).Content;
                }
                else
                {
                    selectedPage = CurrentPage;
                }

                if (selectedPage != null)
                {
                    var selectedNifty = selectedPage as IPageEnhanced;
                    if (selectedPage != PrevPage)
                    {
                        var prevNifty = PrevPage as IPageEnhanced;
                        if (prevNifty != null && prevNifty.IsInTabs)
                        {
                            if (prevNifty != selectedNifty)
                                PrevPage.TabDisactivated();

                            if (selectedNifty != null && selectedNifty.IsInTabs)
                            {
                                prevNifty.IsActiveTab = false;
                            }
                        }
                    }

                    if (selectedNifty != null)
                    {
                        if (selectedNifty.IsInTabs)
                        {
                            if (DeviceInfo.Current.Platform == DevicePlatform.Android && selectedPage.Appeared)
                            {
                                selectedPage.SignalAppearing();
                            }

                            selectedPage.TabActivated();

                            //android bug: after first tab click onappearing is not called
                            //cuz it was called upon creation in tabs..

                            App.Instance.Messager.All("TabSelected", selectedPage.GetType().ToString());

                            //new                

                        }
                    }

                    PrevPage = selectedPage;

                }

                Settings.Current.AddOrUpdateValue("LastTab", index);
            }
        }

        private Color colorBar { get; set; }
        private Color colorIcon { get; set; }
        private Color colorText { get; set; }
        private Color colorTextSel { get; set; }
        private Color colorIconSel { get; set; }
        private Color colorIndicator { get; set; }
        private Color colorBack { get; set; }
        private bool showText { get; set; }



        public void SetupSkin()
        {
            //DroidCore.MainWindow?.SetNavigationBarColor(Color.Black.ToAndroid());

            if (TenantOptions.DarkSkin)
            {
                colorIndicator = Colors.Transparent;//AppColors.Accent;
                colorBar = Colors.Black;//Color.Parse(AppColors.ios_tabs_background_notxt);
                colorText = Color.Parse(AppColors.ColorTabsTextUnselected).MultiplyAlpha(AppColors.tabs_unselected_a);
                colorIcon = Color.Parse(AppColors.ColorTabsIconUnselected).MultiplyAlpha(AppColors.tabs_unselected_a);
                colorIconSel = Color.Parse(AppColors.ios_tabs_selected_notxt).MultiplyAlpha(AppColors.tabs_selected);
                colorTextSel = colorIconSel;
            }
            else
            {
                colorIndicator = Colors.Transparent;//AppColors.Accent;
                colorBar = Color.Parse(AppColors.ios_tabs_background_notxt);
                colorText = Color.Parse(AppColors.ColorTabsTextUnselected).MultiplyAlpha(AppColors.tabs_unselected_a);
                colorIcon = Color.Parse(AppColors.ColorTabsIconUnselected).MultiplyAlpha(AppColors.tabs_unselected_a);
                colorIconSel = Color.Parse(AppColors.ios_tabs_selected_notxt).MultiplyAlpha(AppColors.ios_tabs_selected_notxt_a);
                colorTextSel = colorIconSel;
            }
        }

        void CreateTabs()
        {
            //Globals.Values.TabsMain = this;

            SetupSkin();

            int counter = 0;
            var TabbedMenuItems = new List<TabbedMenuItem>();
            var Pages = new List<View>();

            //NEW favs for tabs
            var visibility = Core.Current.GetTabsVisibility();
            var index = -1;

            Debug.WriteLine($"[BOOTSTRAP] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Adding pages...");

            var TimeThen = DateTime.Now;

            //Switcher.Reset();
            //TabHost.Tabs.Clear();

            foreach (var item in TabsAndMenu.TabsList)
            {
                if (!string.IsNullOrEmpty(item.Platform))
                {
                    if (!item.Platform.Contains(DeviceInfo.Platform.ToString()))
                        continue;
                }

                if (TabbedMenuItems.Count >= TabsAndMenu.MaxTabs)
                    break;

                index++;
                if (index < visibility.Count)
                {
                    if (!visibility[index])
                        continue;
                }

                View lazy = null;
                if (item.ContentClass != null)
                {
                    //todo optional  pageTab.TitleImage = item.NavBarImage;
                    lazy = new LazyWrapContentType(item.ContentClass, item.NameInHeader, item.ContentIsFullScreen, item.HideNavigation);
                }
                else
                {
                    throw new UnsolvableConstraintsException(
                        "PageTabs unimplemented case for item.ContentClass == null");
                    //pageTab = Activator.CreateInstance(item.PageClass);
                }

                Pages.Add(lazy);

                //todo inside lazy!!!!
                //if (pageTab is IPageEnhancedNav)
                //{
                //    ((IPageEnhancedNav)pageTab).AddedToTabs();
                //}

                //TODO move this to shell somehow?
                //fixes status bar color
                //naviTab.BarTextColor = OverrideBarTextColor; //StatusBar text color for ios
                //naviTab.IconImageSource = item.IconSource;

                if (counter > 0)
                {
                    _Startup = true;
                }
                TabbedMenuItems.Add(item);

                TabHost.Tabs.Add(new SkiaBottomTabFontIcon()
                {
                    Route = item.Id,
                    SvgImage = item.IconString,
                    Text = item.NameInTabs.ToUpperInvariant(),
                    //TabAlign = LayoutOptions.Center,
                    //TabWidth = GridLength.Star,

                    TextSize = TenantOptions.AndroidTabsTextFontSize, //9
                    IconSize = 26,//TenantOptions.AndroidTabsIconsFontSize, //22 todo diff sizes sel/unsel

                    ShowText = Globals.Values.iShowTabsText,

                    SelectedIconColor = colorIconSel,
                    SelectedTextColor = colorTextSel,
                    IconColor = colorIcon,
                    TextColor = colorText,

                });

                counter++;

                var now = DateTime.Now;
                var time = now - TimeThen;
                TimeThen = now;
                Debug.WriteLine($"[BOOTSTRAP] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Added {item.NameInHeader} in {time.TotalSeconds:0.0}...");

            }

            // OpenLastTab
            var openLastTab = Settings.Current.GetValueOrDefault("OpenLastTab", false);
            if (openLastTab)
            {
                try
                {
                    var selectTab = Settings.Current.GetValueOrDefault("LastTab", 0);

                    this.ViewSwitcher.SelectedIndex = selectTab;
                    //OnChangedTab(true);

                    //var CurrentPage = Switcher.ActiveView;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    App.Logger.Error(this.GetType().Name, e);
                }
            }
            else
            {
                //fire for first tab
                //OnChangedTab(true);
            }

            Switcher.Views = new(Pages);

            App.Instance.Messager.Subscribe<string>(this, "TabBarRendered", async (sender, arg) =>
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    var CurrentPage = Switcher.ActiveView;

                    if (CurrentPage is ILazyView lazy)
                        CurrentPage = lazy.Content;

                    Rendered = true;
                    if (CurrentPage != null)
                    {
                        try
                        {
                            var selectedPage = (IPageEnhanced)CurrentPage;
                            selectedPage.TabsRendered();
                        }
                        catch (Exception e)
                        {
                            var shit = e;
                        }
                    }
                });

            });

            App.Instance.Messager.Subscribe<string>(this, "TabBarClicked", async (sender, arg) =>
            {

                App.Instance.Messager.All("PopToRoot", "");

                if (!_tabchanged)//|| Core.IsAndroid
                {
                    //not working in android...
                    //await Navigation.PopToRootAsync();
                    //   await Core.Current.PopToRoot();//.ConfigureAwait(false);
                }
                _tabchanged = false;

                ////1 get current tab position
                //var log = ((dynamic)CurrentPage).CurrentPage.GetType().Name;
                //if (log == "WrapContent")
                //{
                //    log = ((dynamic)CurrentPage).CurrentPage.ContentType.Name;
                //}
                //App.Logger.Info("TAB", log);

            });

            Debug.WriteLine($"[BOOTSTRAP] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Done building TabbedBar..");

            _Startup = false;
        }

 
        private bool setupBusy = false;

        void Setup()
        {

            if (setupBusy)
                return;

            setupBusy = true;

            Core.Current.HasTabs = true;

            CreateTabs();

            if (DeviceInfo.Current.Platform == DevicePlatform.WinUI) IconImageSource = "swap.png";
            if (DeviceInfo.Current.Platform == DevicePlatform.iOS) IconImageSource = "menu.png";

            //todo PUSH CONTENT
            //App.Instance.Messager.Subscribe<string[]>(this, "PushContentSent",
            //    async (sender, values) => { await PushContent(values[0], values[1]); });

            Cleanup();

            App.Instance.Messager.Subscribe<string>(this, "SelectTab", async (sender, arg) =>
            {
                if (arg == "appo")
                {
                    // SelectTab(typeof(PageAppoRoot));
                }
            });

            App.Instance.Messager.Subscribe<string>(this, "PopToRoot", async (sender, arg) =>
            {
                Switcher?.PopAllTabsToRoot();
                WishTabBinding = arg;

                Device.StartTimer(TimeSpan.FromMilliseconds(250), () =>
                {
                    SelectWishTab();
                    return false;
                });
            });

            App.Instance.Messager.Subscribe<string>(this, "On", async (sender, arg) =>
            {
                // do something whenever the "Login" message is sent
                // using the 'arg' parameter which is a string

                if (arg == "Resume")
                {
                    OnChangedTab(true);
                }
            });

            setupBusy = false;
        }

        void Cleanup()
        {
            App.Instance.Messager.Unsubscribe(this, "SelectTab");
            App.Instance.Messager.Unsubscribe(this, "PopToRoot");
            App.Instance.Messager.Unsubscribe(this, "On");

        }

        #endregion

        private bool maybe_exit { get; set; } = false;
        protected override bool OnBackButtonPressed()
        {
            if (Navigation.NavigationStack.Count > 1)
            {
                return false; //do nothing, lets the system pop us
            }

            if (maybe_exit)
            {
                return false; //exit
            }

            var myIndex = Switcher.GetCurrentTabNavigationIndex();
            if (myIndex > 0)
            {
                Switcher.PopTab();
                return true;
            }

            App.Instance.UI.ShowToast(ResStrings.PressBACKOnceAgain);
            maybe_exit = true;

            Tasks.StartTimerAsync(TimeSpan.FromSeconds(2), async () =>
            {
                maybe_exit = false;
                return false;
            });

            return true; //true - dont process BACK by system
        }

        //public override void OnRendered()
        //{
        //    base.OnRendered();

        //    App.Instance.Messager.All("UIRendered", "Tabs");
        //}

        public override double OnKeyboardResized(double size)
        {
            if (Model.NavbarModel.Keyboard != size)
            {
                Model.NavbarModel.Keyboard = size;
                Switcher.InvokeOnKeyboardResized(size);
            }

            return base.OnKeyboardResized(size);
        }


        public override void OnPageWasRotated()
        {
            base.OnPageWasRotated();

            UpdateControls(Orientation);
        }



        public override void OnDisposing()
        {
            App.Instance.Messager.Unsubscribe(this, "TabBarRendered");
            App.Instance.Messager.Unsubscribe(this, "TabBarClicked");
            App.Instance.Messager.Unsubscribe(this, "AppLayoutInvalidated");

            Switcher.Dispose();
            TabHost.Dispose();

            TabHost = null;
            Switcher = null;

            base.OnDisposing();
        }


        public TabsViewModel Model;

        #region STYLES 



        //protected override void OnSizeAllocated(double _width, double _height)
        //{
        //    App.Current.UpdateDynamicStylesUponWidth(_width, () =>
        //        {
        //            Resources["BottomTabStyle"] = Resources["BottomTabDefault"];
        //        },
        //        () =>
        //        {
        //            Resources["BottomTabStyle"] = Resources["BottomTabSmall"];
        //        },
        //        this.Id.ToString());

        //    base.OnSizeAllocated(_width, _height);
        //}


        #endregion


        public IViewSwitcher ViewSwitcher
        {
            get
            {
                return this.Switcher;
            }
        }

        public IViewsContainer ViewsContainer
        {
            get
            {
                return this.Container as IViewsContainer;
            }
        }

        public PageTabs()
        {
            try
            {
                BindingContext = Model = new TabsViewModel();

                InitializeComponent();

                if (!Core.IsAndroid)
                {
                    BackgroundCanvas.IsVisible = true;
                }


                Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific.Page.SetUseSafeArea(this, false);

                App.Shell.Tabs = TabHost;
                App.Shell.ViewSwitcher = Switcher;

                OnLoaded();
            }
            catch (Exception e)
            {
                Designer.DisplayException(this, e);
            }


            App.Instance.Messager.Subscribe<string>(this, "SelectRootTabExec", async (sender, arg) =>
            {
                WishTabBinding = arg;
            });

            App.Instance.Messager.Subscribe<string>(this, "Release", async (sender, arg) =>
            {
                App.Instance.Messager.Unsubscribe(this, "Release");
                App.Instance.Messager.Unsubscribe(this, "SelectRootTabExec");
            });

            Setup();
        }

        public void OnLoaded()
        {
            App.Instance.Messager.Subscribe<string>(this, "AppLayoutInvalidated", async (sender, arg) =>
            {
                UpdateControls(Orientation);
            });

            UpdateControls(Orientation);
        }

        private bool once;




        //-------------------------------------------------------------
        // WishTabBinding
        //-------------------------------------------------------------
        private const string nameWishTabBinding = "WishTabBinding";
        public static readonly BindableProperty WishTabBindingProperty = BindableProperty.Create(nameWishTabBinding, typeof(string), typeof(PageTabs), string.Empty); //, BindingMode.TwoWay
        public string WishTabBinding
        {
            get { return (string)GetValue(WishTabBindingProperty); }
            set { SetValue(WishTabBindingProperty, value); }
        }

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == nameWishTabBinding)
            {
                SelectWishTab();
            }

        }


        public void SelectWishTab()
        {
            try
            {
                if (!string.IsNullOrEmpty(WishTabBinding))
                {
                    var oldIndex = Model.SelectedIndex;

                    // Update the UI
                    if (WishTabBinding.SafeContainsInLower("last"))
                    {
                        var i = Switcher.Children.Count;
                        if (i >= 0)
                            Model.SelectedIndex = i - 1;
                    }
                    else
                    if (WishTabBinding.SafeContainsInLower("first"))
                    {
                        //todo!!! - select first visible child
                        Model.SelectedIndex = 0;
                    }
                    else
                    {
                        var index = WishTabBinding.ToInteger();
                        var at = 0;
                        bool found = false;
                        foreach (var tab in TabHost.Tabs)
                        {
                            if (tab is TabItem tabItem)
                            {
                                if (tabItem.TabIndex == index)
                                {
                                    Model.SelectedIndex = at;
                                    found = true;
                                    break;
                                }
                            }
                            at++;
                        }
                        if (!found)
                            Model.SelectedIndex = WishTabBinding.ToInteger();
                    }

                    //if (oldIndex == Model.SelectedIndex)
                    //{
                    //    Model.UpdateSelectedIndex();
                    //}

                    WishTabBinding = null;
                }

            }
            catch (Exception e)
            {
                Super.Log(e);
            }
        }

        private void PannedLeft(object sender, TouchActionEventArgs e)
        {
            if (sender is PanAwareHotspot pan && pan.DistanceX < -40)
            {
                Switcher.NavigateNext();
            }
        }

        private async void PannedRight(object sender, TouchActionEventArgs e)
        {
            if (sender is PanAwareHotspot pan && pan.DistanceX > 40)
            {
                await Switcher.NavigatePrevious();
            }
        }

        private void SwipedLeft(object sender, TouchActionEventArgs e)
        {
            //Switcher.NavigateNext();
        }

        private void SwipedRight(object sender, TouchActionEventArgs e)
        {
            //Switcher.NavigatePrevious();
        }

        private void OnCurrentPageChanged(object? sender, View e)
        {
            OnChangedTab();

        }
    }
}
