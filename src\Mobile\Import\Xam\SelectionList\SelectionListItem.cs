﻿using AppoMobi.Models;


namespace AppoMobi.Xam
{
    public class SelectionListItem : BindableObject
    {
        private string _Title;
        public string Title
        {
            get { return _Title; }
            set
            {
                if (_Title != value)
                {
                    _Title = value;
                    OnPropertyChanged();
                }
            }
        }

        private string _IconSource;
        public string IconSource
        {
            get { return _IconSource; }
            set
            {
                if (_IconSource != value)
                {
                    _IconSource = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _Selected;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    OnPropertyChanged();
                }
            }
        }

    }
}
