using Android.Views;
using AppoMobi.Touch;
using AppoMobi.Touch.Droid;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Xamarin.Forms;
using View = Android.Views.View;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public static class AndroidEventArgsHelper
	{
		public static Point[] GetTouches(MotionEvent current)
		{
			MotionEvent.PointerCoords pointerCoord = new MotionEvent.PointerCoords();
			int pointerCount = current.PointerCount;
			Point[] point = new Point[pointerCount];
			for (int i = 0; i < pointerCount; i++)
			{
				current.GetPointerCoords(i, pointerCoord);
				point[i] = DIP.ToPoint((double)pointerCoord.X, (double)pointerCoord.Y);
			}
			return point;
		}

        public static Point[] GetTouches(MotionEvent current, int requiredTouches, BaseGestureEventArgs previous)
		{
			if (current.PointerCount < requiredTouches && previous != null)
			{
				return previous.Touches;
			}
			return AndroidEventArgsHelper.GetTouches(current);
		}

		public static Point[] GetTouches(IEnumerable<Point> touches)
		{
		    var ret = new List<Point>();
		    foreach (var p in touches)
		    {
		        ret.Add(DIP.ToPoint(p.X, p.Y));
		    }
		    return ret.ToArray();
		}

		public static Rect GetViewPosition(View view)
		{
			int[] numArray = new int[2];
			view.GetLocationInWindow(numArray);
			int num = numArray[0];
			int num1 = numArray[1];
			int width = view.Width;
			int height = view.Height;
			return DIP.ToRectangle((double)num, (double)num1, (double)width, (double)height);
		}
	}
}