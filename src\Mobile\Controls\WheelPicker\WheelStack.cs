﻿using System.Numerics;

namespace DrawnUi.Draw
{
    public class WheelStack : SkiaLayout
    {
        public WheelStack()
        {
            Type = LayoutType.Column;
            RecyclingTemplate = RecyclingTemplate.Disabled;
            Spacing = 0;
        }

        public override ScaledRect GetOnScreenVisibleArea(DrawingContext context, Vector2 inflateByPixels = default)
        {
            return ScaledRect.FromPixels(new(0, 0, Single.PositiveInfinity, Single.PositiveInfinity), context.Scale);
        }


    }
}
