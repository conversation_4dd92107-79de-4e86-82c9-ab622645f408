﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyText"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:appoMobi="clr-namespace:AppoMobi">

    <ContentView.Content>
        <Grid
            x:Name="ControlContainer"
            Padding="18,13,18,12"
            ColumnSpacing="16"
            HorizontalOptions="FillAndExpand">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  column 0 - TEXT  -->
            <ContentView Grid.Column="0">
                <Label x:Name="ControlLabelText"         FontSize="14.5"      TextColor="{x:Static appoMobi:AppColors.BwGrey}"               />
                <ContentView.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnTapped" />
                </ContentView.GestureRecognizers>
            </ContentView>
        </Grid>
    </ContentView.Content>

</ContentView>