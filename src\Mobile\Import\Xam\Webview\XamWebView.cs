﻿using System;
using System.Windows.Input;


namespace AppoMobi.Xam
{
    public class FacebookWidgetWebView : XamWebView
    {
        public event EventHandler<WebNavigatedEventArgs> CustomNavigated;

        public event EventHandler<WebNavigatingEventArgs> CustomNavigating;

        public void RaiseCustomNavigated(WebNavigatedEventArgs e)
        {
            CustomNavigated?.Invoke(this, e);
        }

        public void RaiseCustomNavigating(WebNavigatingEventArgs e)
        {
            CustomNavigating?.Invoke(this, e);
        }
    }

    public class XamWebView: WebView
    {
        public XamWebView()
        {

        //    LoadFinished += (sender, e) =>
        //        InjectJavaScript(@"
        //    document.body.addEventListener('click', function(e) {
        //        e = e || window.event;
        //        var target = e.target || e.srcElement;
        //        Native('invokeClick', 'tag='+target.tagName+' id='+target.id+' name='+target.name);
        //    }, true /* to ensure we capture it first*/);
        //");

        //    this.RegisterCallback("invokeClick", (string el) => {
        //        var args = new ClickEventArgs { Element = el };

        //        Clicked?.Invoke(this, args);
        //        ClickCommand?.Execute(args);
        //    });

        }

        public bool ZoomEnabled { get; set; }



        protected new void OnSourceChanged(object sender, EventArgs e)
        {
            var stop = true;
        }

        //-------------------------------------------------------------
        // WebViewColor
        //-------------------------------------------------------------
        private const string nameWebViewColor = "WebViewColor";
        public static readonly BindableProperty WebViewColorProperty = BindableProperty.Create(nameWebViewColor, typeof(Color), typeof(XamWebView), Colors.White); //, BindingMode.TwoWay
        public Color WebViewColor
        {
            get { return (Color)GetValue(WebViewColorProperty); }
            set
            {
                SetValue(WebViewColorProperty, value);               
            }
        }

        protected override void OnPropertyChanged([CallerMemberName]string propertyName=null)
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameWebViewColor:
                    RendererCommand?.Invoke("Update", null);
                    break;
            }
        }

   
		


        //-------------------------------------------------------------
        // Html
        //-------------------------------------------------------------
        private const string nameHtml = "Html";
        public static readonly BindableProperty HtmlProperty = BindableProperty.Create(nameHtml, typeof(string), typeof(XamWebView), string.Empty); //, BindingMode.TwoWay
        public string Html
        {
            get { return (string)GetValue(HtmlProperty); }
            set { SetValue(HtmlProperty, value); }
        }

        //-------------------------------------------------------------
        // Url
        //-------------------------------------------------------------
        private const string nameUrl = "Url";
        public static readonly BindableProperty UrlProperty = BindableProperty.Create(nameUrl, typeof(string), typeof(XamWebView), string.Empty); //, BindingMode.TwoWay
        public string Url
        {
            get { return (string)GetValue(UrlProperty); }
            set { SetValue(UrlProperty, value); }
        }


        public class ClickEventArgs : EventArgs
        {
            public string Element { get; set; }
        }

        public event EventHandler<ClickEventArgs> Clicked;
        public static readonly BindableProperty ClickCommandProperty =
            BindableProperty.Create("ClickCommand", typeof(ICommand), typeof(XamWebView), null);
        public ICommand ClickCommand
        {
            get { return (ICommand)GetValue(ClickCommandProperty); }
            set { SetValue(ClickCommandProperty, value); }
        }

        public event EventHandler RendererCommand;

        public void ResumePlay()
        {
            RendererCommand?.Invoke("ResumePlay", null);
        }

        public void PausePlay()
        {
            RendererCommand?.Invoke("PausePlay", null);
        }

        public void EnableZoom()
        {
            ZoomEnabled = true;
            RendererCommand?.Invoke("EnableZoom", null);
        }

        public void DisableZoom()
        {
            ZoomEnabled = false;
            RendererCommand?.Invoke("DisableZoom", null);
        }

    }
}
