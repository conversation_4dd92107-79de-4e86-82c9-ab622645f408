using Android.Views;
using Java.Lang;
using AppoMobi.Touch;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using StringBuilder = System.Text.StringBuilder;
using View = Android.Views.View;

namespace AppoMobi.Touch.Droid
{
    public class AndroidGestureHandler
    {
        private static int Instances;

      //  [TupleElementNames(new string[] { "element", "view" })]
        private static Dictionary<ValueTuple<IWithTouch, View>, AndroidGestureHandler> allGestureHandlers;

        private View view;

 

        private bool isVisible = true;

        private DownUpGestureDetector downUpGestureDetector;

        private SimpleGestureListener simpleListener;

        private GestureDetector simpleGestureDetector;

        private TapGestureListener tapGestureListener;

        private TapGestureDetector tapGestureDetector;

        private MultiTouchDetector _multiTouchDetector;

        private MotionEventActions lastAction = MotionEventActions.Up;

        private long lastEventTime;

        private int lastPointerCount;

        private static StringBuilder logSb;

        internal IWithTouch Element
        {
       
            get;
            private set;
        }

        static AndroidGestureHandler()
        {
            allGestureHandlers = new Dictionary<ValueTuple<IWithTouch, View>, AndroidGestureHandler>();
            logSb = new StringBuilder();
            /*
            Device.StartTimer(TimeSpan.FromSeconds(2), new Func<bool>(u003cu003ec.u003cu003e9,  () => {
                        if (logSb != null && logSb.Length > 0)
                        {
                            logSb.Clear();
                        }
                        return true;
                    }));
                    */
        }

        internal AndroidGestureHandler(IWithTouch element, View view, IGestureListener listener = null)
        {
            this.Element = element;
            this.view = view;
            object gestureThrottler = listener;
            if (gestureThrottler == null)
            {
                gestureThrottler = new GestureThrottler(new GestureFilter(element, element.GestureHandler));
            }
            listener = (IGestureListener) gestureThrottler;
            this.downUpGestureDetector = new DownUpGestureDetector(new DownUpGestureListener(element, view, listener));
            this.simpleListener = new SimpleGestureListener(element, view, listener);
            this.simpleGestureDetector = new GestureDetector(view.Context, this.simpleListener);
            this.simpleGestureDetector.IsLongpressEnabled=(false);
            this.tapGestureListener = new TapGestureListener(element, view, listener);
            this.tapGestureDetector = new TapGestureDetector(view.Context, this.tapGestureListener);
            this._multiTouchDetector = new MultiTouchDetector(view.Context, this.simpleListener);
            view.Touch += HandleTouch;
            AndroidGestureHandler.Instances++;
            this.LogInstances("Constructor");
        }

        /// <summary>
        /// Adds the gesture recognizers to a View. Must be called from the Cell renderers GetCellCore method.
        /// </summary>
        /// <param name="element"></param>
        /// <param name="view"></param>
        public static void AddGestureRecognizers(IWithTouch element, View view)
        {
            if (AndroidGestureHandler.allGestureHandlers.ContainsKey(new ValueTuple<IWithTouch, View>(element, view)))
            {
                AndroidGestureHandler.Log(string.Concat("AddGestureRecognizers, but element ", AndroidGestureHandler.ElementLog(element), " already exists!"));
                AndroidGestureHandler.RemoveInstance(element, view);
            }
            
            AndroidGestureHandler androidGestureHandler = new AndroidGestureHandler(element, view, null);

            AndroidGestureHandler.allGestureHandlers[new ValueTuple<IWithTouch, View>(element, view)] = androidGestureHandler;

            //Cell cell = element as Cell;
            //if (cell != null)
            //{
            //    cell.Appearing += Cell_Appearing;
            //    cell.Disappearing+= Cell_Disappearing;
            //    ListView parent = cell.Parent as AppoMobi.Touch.ListView;
            //    if (parent != null)
            //    {
            //        parent.CellsToDispose.Add(cell);
            //    }
            //}

        }

        private void Cell_Appearing(object sender, System.EventArgs e)
        {
            this.isVisible = true;
        }

        private void Cell_Disappearing(object sender, System.EventArgs e)
        {
            this.isVisible = false;
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (this.view != null)
                {
                    this.view.Touch -= HandleTouch;
                    this.view = null;
                }
                if (this.simpleGestureDetector != null)
                {
                    this.simpleGestureDetector.Dispose();
                    this.simpleGestureDetector = null;
                    this.simpleListener.Dispose();
                    this.simpleListener = null;
                }
                this.downUpGestureDetector = null;
                this.tapGestureDetector = null;
                this.tapGestureListener = null;
                this._multiTouchDetector = null;
                AndroidGestureHandler.Instances--;
                this.LogInstances("Dispose");
            }
        }

        private static string ElementLog(IWithTouch element)
        {
            if (element == null)
            {
                return "null";
            }
            object bindingContext = ((BindableObject)element).BindingContext;
            if (bindingContext == null)
            {
                bindingContext = "null";
            }
            object obj = bindingContext;
            return string.Format("{0}({1}), BindingContext={2}", element.GetType().Name, element.GetHashCode(), obj);
        }

        /// <summary>
        /// Is called from the renderers DispatchTouchEvent method.
        /// </summary>
        /// <param name="element"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        /// 
        public static bool HandleMotionEvent(IWithTouch element, View view, MotionEvent e)
        
        {
            ValueTuple<IWithTouch, View> valueTuple = new ValueTuple<IWithTouch, View>(element, view);
            if (!AndroidGestureHandler.allGestureHandlers.ContainsKey(valueTuple))
            {
                return false;
            }
            return AndroidGestureHandler.allGestureHandlers[valueTuple].HandleMotionEvent(e);
        }

        
        private bool HandleMotionEvent(MotionEvent e)
        
        {
            if (!this.isVisible)
            {
                return false;
            }
            if (this.MatchesLastMotionEvent(e))
            {
                return false;
            }
            bool flag = false;
            if (this.downUpGestureDetector != null)
            {
                flag |= this.downUpGestureDetector.OnTouchEvent(e);
            }
            if (this.simpleGestureDetector != null)
            {
                flag |= this.simpleGestureDetector.OnTouchEvent(e);
            }
            if (this.tapGestureDetector != null)
            {
                flag |= this.tapGestureDetector.OnTouchEvent(e);
            }
            if (e.PointerCount > 1 && this._multiTouchDetector != null)
            {
                flag |= this._multiTouchDetector.OnTouchEvent(e);
            }
            if ((e.Action == (MotionEventActions)1 || e.Action == MotionEventActions.Cancel) && this.simpleListener != null)
            {
                this.simpleListener.EndGestures(e);
            }
            return flag;
        }

        private void HandleTouch(object sender, View.TouchEventArgs e)
        {
            this.HandleMotionEvent(e.Event);
        }

        private static void Log(string s)
        {
        }

        private void LogInstances(string method)
        {
            //((BindableObject)this.Element).get_BindingContext();
            //AndroidGestureHandler.Log(string.Format("AndroidGestureHandler.{0}, Element is a {1} Total instances: {2}", method, AndroidGestureHandler.ElementLog(this.Element), AndroidGestureHandler.Instances));
        }

        private bool MatchesLastMotionEvent(MotionEvent e)
        {
            if (e.Action == this.lastAction && e.EventTime == this.lastEventTime && e.PointerCount == this.lastPointerCount)
            {
                return true;
            }
            this.lastAction = e.Action;
            this.lastEventTime = e.EventTime;
            this.lastPointerCount = e.PointerCount;
            return false;
        }

        /// <summary>
        /// Called from a View renderers OnElementChanged method.
        /// </summary>
        /// <param name="oldElement"></param>
        /// <param name="newElement"></param>
        /// <param name="view"></param>
        public static void OnElementChanged(IWithTouch oldElement, IWithTouch newElement, View view)
        {
            AndroidGestureHandler.Log(string.Concat(new string[] { "OnElementChanged(", AndroidGestureHandler.ElementLog(oldElement), ", ", AndroidGestureHandler.ElementLog(newElement), ")" }));
            if (oldElement == newElement)
            {
                return;
            }
            if (oldElement != null)
            {
                AndroidGestureHandler.RemoveInstance(oldElement, view);
            }
            if (newElement != null)
            {
                AndroidGestureHandler.AddGestureRecognizers(newElement, view);
            }
        }

        /// <summary>
        /// Called from a View renderers OnElementPropertyChanged method. (not used on Android currently, but maybe in the future)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// <param name="view"></param>
        public static void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e, View view)
        {
        }

        internal void PrintMotionEvent(string title, MotionEvent e)
        {
            StringBuilder stringBuilder = new StringBuilder(title);
            stringBuilder.AppendFormat(": Action: {0} on {1}", e.Action, this.Element.GetType().Name);
            for (int i = 0; i < e.PointerCount; i++)
            {
                stringBuilder.AppendFormat(", Pointer {0}: {1}/{2}", i, e.GetX(i), e.GetY(i));
            }
        }

        public static void PrintViewTree(View view, StringBuilder sb = null, int spaces = 0)
        {
            object name;
            if (sb == null)
            {
                sb = new StringBuilder();
            }
            string str = string.Join<char>("", Enumerable.Repeat<char>(' ', spaces));
            StringBuilder stringBuilder = sb;
            string str1 = str;
            if (view != null)
            {
                name = view.GetType().Name;
            }
            else
            {
                name = null;
            }
            if (name == null)
            {
                name = "null";
            }
            stringBuilder.AppendFormat("{0}{1}\n", str1, name);
            ViewGroup viewGroup = view as ViewGroup;
            if (viewGroup != null)
            {
                for (int i = 0; i < viewGroup.ChildCount; i++)
                {
                    AndroidGestureHandler.PrintViewTree(viewGroup.GetChildAt(i), sb, spaces + 2);
                }
            }
        }

        /// <summary>
        /// Removes the gesture recognizers for the given IWithTouch. Called from a Views Dispose method.
        /// </summary>
        /// <param name="element"></param>
        public static void RemoveInstance(IWithTouch element, View view)
        {
            IWithTouch[] array;
            int i;
            ValueTuple<IWithTouch, View> valueTuple = new ValueTuple<IWithTouch, View>(element, view);
            if (element != null && AndroidGestureHandler.allGestureHandlers.ContainsKey(valueTuple))
            {
                AndroidGestureHandler item = AndroidGestureHandler.allGestureHandlers[valueTuple];
                AndroidGestureHandler.allGestureHandlers.Remove(valueTuple);
                //Cell cell = element as Cell;
                //if (cell != null)
                //{
                //    cell.remove_Appearing(new EventHandler(item, AndroidGestureHandler.Cell_Appearing));
                //    cell.remove_Disappearing(new EventHandler(item, AndroidGestureHandler.Cell_Disappearing));
                //    ListView parent = cell.get_Parent() as ListView;
                //    if (parent != null)
                //    {
                //        parent.CellsToDispose.Remove(cell);
                //    }
                //}
                //ListView listView = element as ListView;
                //if (listView != null)
                //{
                //    array = Enumerable.ToArray<IWithTouch>(Enumerable.OfType<IWithTouch>(listView.CellsToDispose));
                //    for (i = 0; i < (int)array.Length; i++)
                //    {
                //        AndroidGestureHandler.RemoveInstances(array[i]);
                //    }
                //}
                //TableView tableView = element as TableView;
                //if (tableView != null)
                //{
                //    TableRoot root = tableView.get_Root();
                //    Func<TableSection, IEnumerable<Cell>> u003cu003e9_30 = AndroidGestureHandler.u003cu003ec.u003cu003e9__3_0;
                //    if (u003cu003e9_30 == null)
                //    {
                //        u003cu003e9_30 = new Func<TableSection, IEnumerable<Cell>>(AndroidGestureHandler.u003cu003ec.u003cu003e9, (TableSection r) => r);
                //        AndroidGestureHandler.u003cu003ec.u003cu003e9__3_0 = u003cu003e9_30;
                //    }
                //    array = Enumerable.ToArray<IWithTouch>(Enumerable.OfType<IWithTouch>(Enumerable.SelectMany<TableSection, Cell>(root, u003cu003e9_30)));
                //    for (i = 0; i < (int)array.Length; i++)
                //    {
                //        AndroidGestureHandler.RemoveInstances(array[i]);
                //    }
                //}
                item.Dispose();
            }
        }

        private static void RemoveInstances(IWithTouch element)
        {
            ValueTuple<IWithTouch, View>[] array = Enumerable.ToArray<ValueTuple<IWithTouch, View>>(AndroidGestureHandler.allGestureHandlers.Keys);
            for (int i = 0; i < (int)array.Length; i++)
            {
                ValueTuple<IWithTouch, View> valueTuple = array[i];
                if (valueTuple.Item1 == element)
                {
                    AndroidGestureHandler.RemoveInstance(valueTuple.Item1, valueTuple.Item2);
                }
            }
        }
 
    }
}