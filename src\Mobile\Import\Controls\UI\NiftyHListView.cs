﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using AppoMobi.Xam;
using FFImageLoading.Maui;


namespace AppoMobi.Nifty
{
    
    
    public class NiftyListView : SolidScrollView
    
    {
        Microsoft.Maui.Controls.Grid MyGrid = new Microsoft.Maui.Controls.Grid();
        private int SelectedRow = -1;
        private AppoMobi.Touch.LegacyGesturesBoxView SelectionBox = null;
        private Color OriginalBackColor = Colors.Transparent;
        private CachedImage SelectedIcon = null;
        private double VertMargin = 16;

        public bool IsPressed { get; set; } = false;
        public bool IsPanned { get; set; } = false;

        readonly AppoMobi.Touch.LegacyGesturesStackLayout _imageStack;

        
        public NiftyListView()
        
        {

            this.Orientation = ScrollOrientation.Horizontal;

            _imageStack = new AppoMobi.Touch.LegacyGesturesStackLayout
            {
                Orientation = StackOrientation.Horizontal
            };

            this.Content = _imageStack;

        }

        public new IList<IView> Children
        {
            get
            {
                return  _imageStack.Children;
            }
        }

        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameItemsSource:
                    ItemsSourceChanged();//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;

                //                case nameLabelIcon:
                //                    ControlLabelIcon.Source = LabelIcon;
                //                    break;
                //property changed
                case "Orientation":
                    if (_imageStack != null)
                    {
                        if (Orientation == ScrollOrientation.Horizontal)
                            _imageStack.Orientation = StackOrientation.Horizontal;
                        else if (Orientation == ScrollOrientation.Vertical)
                            _imageStack.Orientation = StackOrientation.Vertical;
                    }
                    break;
                case "Spacing":
                    if (_imageStack != null)
                    {
                        _imageStack.Spacing = Spacing;
                    }
                    break;
            }

        }


        
        // Spacing
        
        private const string nameSpacing = "Spacing";
        public static readonly BindableProperty SpacingProperty = BindableProperty.Create(nameSpacing, typeof(double), typeof(NiftyListView), 0.0); //, BindingMode.TwoWay
        public double Spacing
        {
            get { return (double)GetValue(SpacingProperty); }
            set { SetValue(SpacingProperty, value); }
        }	


        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(NiftyListView), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }

        
        // SelectedParams
        
        private const string nameSelectedParams = "SelectedParams";
        public static readonly BindableProperty SelectedParamsProperty = BindableProperty.Create(nameSelectedParams, typeof(string), typeof(NiftyListView), string.Empty);
        public string SelectedParams
        {
            get { return (string)GetValue(SelectedParamsProperty); }
            set { SetValue(SelectedParamsProperty, value); }
        }

        
        public void Dispose()
        
        {
            if (_imageStack.Children == null) return;
            //todo call all nifty cells dispose
            foreach (var child in _imageStack.Children)
            {
                try
                {
                    var disposable = (NiftyCell)child;
                    disposable.Dispose();
                }
                catch (Exception e)
                {
                }
            }
        }

        
        void ItemsSourceChanged()
        
        {
            Children.Clear();
            if (ItemsSource == null)
                return;

            //todo check old new itemsource
            //_imageStack.Children.Clear();
            foreach (var newItem in ItemsSource)
            {

                var view = (View)ItemTemplate.CreateContent();
                var bindableObject = view as BindableObject;
                if (bindableObject != null)
                    bindableObject.BindingContext = newItem;
                _imageStack.Children.Add(view);
            }

            var notifyCollection = ItemsSource as INotifyCollectionChanged;
            if (notifyCollection != null)
            {
                notifyCollection.CollectionChanged += (sender, args) =>
                {
                    if (args.NewItems != null)
                    {
                        foreach (var newItem in args.NewItems)
                        {

                            var view = (View)ItemTemplate.CreateContent();
                            var bindableObject = view as BindableObject;
                            if (bindableObject != null)
                                bindableObject.BindingContext = newItem;
                            _imageStack.Children.Add(view);
                        }
                    }
                    else
                    {
                        Children.Clear();
                    }

                    //if (args.OldItems != null)
                    //{
                    //    // not supported
                    //    Children.RemoveAt(args.OldStartingIndex);
                    //}
                };
            }

   }
        

        
        public DataTemplate ItemTemplate
        
        {
            get;
            set;
        }


        
        // ItemsSource
        
        private const string nameItemsSource = "ItemsSource";
        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameItemsSource, typeof(IList), typeof(NiftyListView), null, BindingMode.TwoWay);

        public IList ItemsSource
        {
            get { return (IList)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }

        //public IList ItemsSource
        //{
        //    get { return (IList)GetValue(ItemsSourceProperty); }
        //    set { SetValue(ItemsSourceProperty, value); }
        //}
        
        // Tapped
        
        public event EventHandler LongPressed = null;


        
        // Tapped
        
        public event EventHandler Tapped = null;
        //private async void OnTapped(object sender, EventArgs e)
        //{
          //  Tapped?.Invoke(this, EventArgs.Empty);
        //}


        //---------------------------------------------------------
        public class MySel : AppoMobi.Touch.LegacyGesturesBoxView
        //---------------------------------------------------------
        {
            public  string Tag { get; set; }
            public int Position { get; set; }
            public string Params { get; set; }

            //public NiftyListView Daddy { get; set; } = null;

            
        


        }
        //This is for accessing the passed listview item object        
        public class MySelEventArgs : EventArgs
        {
            public string Tag { get; set; }
            public string Params { get; set; }
        }

        
       
        

    }
}