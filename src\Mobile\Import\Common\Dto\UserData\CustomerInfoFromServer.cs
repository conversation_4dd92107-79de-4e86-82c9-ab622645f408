﻿using System;
using System.Collections.Generic;
using AppoMobi.Common.Dto.Finance;
using AppoMobi.Common.Dto.Required;
using AppoMobi.Common.Enums.System;
using AppoMobi.Common.Enums.UserData;
using Newtonsoft.Json;

namespace AppoMobi.Common.Dto.UserData
{
    public class CustomerInfoFromServer : DbCustomerData //must be stored secure
        //****************************************************
    {
        public CustomerInfoFromServer()
        {
            Profiles = new List<ServiceProfileDto>();
            Wallet = new CustomerFinanceDto();
        }

        public GalleryImageDTO Banner { get; set; }

        //can change on client side
        public string FirstName { get; set; } //can change
        public string MiddleName { get; set; } //can change
        public string LastName { get; set; } //can change
        public DateTime? BirthDate { get; set; } //can change


        public string Prof { get; set; }


        /// <summary>
        /// страна по умолчанию для контента
        /// </summary>
        public string CountryCode { get; set; }
        public string CountryDesc { get; set; }

        /// <summary>
        /// город по умолчанию для контента
        /// </summary>
        public string City { get; set; }
        public string CityDesc { get; set; }

        public ConfirmationStatusType ConfirmationStatus { get; set; }

        /// <summary>
        /// гражданство паспорт..
        /// </summary>
        public string LegalCountryCode { get; set; }
        public string LegalCountryDesc { get; set; }

        public string Email { get; set; } //can change
        public string ImageUrl { get; set; } //can change

        public string Code { get; set; }

        public GenderKind Gender { get; set; }

        public string INN { get; set; }
        public string OGRN { get; set; }

        public string AboutMe { get; set; }

        //server side
        public string Id { get; set; }
        //public string Username { get; set; }
        
        public string PhoneNumber { get; set; } //think about how to change it in the future
        public string PhoneCountry { get; set; }

        //project dependent
        public string Roles { get; set; }

        public string Card { get; set; }

        //TMP readonly
        public string CompanyName { get; set; }
        public int CustomerType { get; set; }

        public bool IsDemo { get; set; }
        
        public List<LegalDocumentDto> Documents { get; set; }

        public List<GalleryImageDTO> Images { get; set; }

        public string Favorites { get; set; }

        public string FavProfiles { get; set; }

        /// <summary>
        /// Native spoken languages
        /// </summary>
        public string Languages { get; set; }

        public CustomerFinanceDto Wallet { get; set; }

        public List<ServiceProfileDto> Profiles { get; set; }

        public bool DND { get; set; }
        
        public bool HideOnline { get; set; }

        public string BlacklistPlayers { get; set; }

        #region STATS

        [JsonProperty("revc")]
        public int MyReviewsCount { get; set; }

        [JsonProperty("calls")]
        public int MyCallsCount { get; set; }
        
        #endregion

    }
}