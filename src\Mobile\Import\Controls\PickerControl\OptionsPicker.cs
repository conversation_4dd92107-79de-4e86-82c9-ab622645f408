﻿using AppoMobi.Forms.Models;
using AppoMobi.Framework.Abstractions;
using AppoMobi.Xam;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Point = Microsoft.Maui.Graphics.Point;
using Size = Microsoft.Maui.Graphics.Size;


namespace AppoMobi.Libs.UI.Controls
{
    public class OptionsPicker : PopupDialogBase, IDisposable
    {

        public int SubmitDelayMs = 500;

        public OptionsPicker()
        {


        }

        public void ShowUnsafe()
        {
            //if (App.Settings.EnableBlur)
            //     Forms.Controls.Svg.Screenshot.Capture();
            this.Open(false);
        }


        public void Show()
        {
            if (MainThread.IsMainThread)
            {
                ShowUnsafe();
            }
            else
            {
                MainThread.BeginInvokeOnMainThread(ShowUnsafe);
            }
        }

        public void Close()
        {
            CommandInternalClose.Execute(null);
        }

        protected override bool OnBackgroundClicked()
        {
            CommandInternalClose.Execute(null);

            //return base.OnBackgroundClicked();
            return false;
        }

        protected bool disposed;
        public void Dispose()
        {
            if (disposed)
                return;
            disposed = true;


            if (Content is IDisposable disposableView)
            {
                disposableView.Dispose();
            }
        }


        #region UI BINDABLES


        private bool _gridOnDownLock;
        private bool _gridOnDownTracked;
        private bool _gridOnDownFired;

        public ICommand CommandBackgroundTapped
        {
            get
            {
                return new Command(async (object context) =>
                {

                    //if (_gridOnDownLock) return;
                    //_gridOnDownLock = true;

                    //var sender = context as View;
                    //if (sender == null)
                    //    return;

                    //    //check if withing childs
                    //if (!ClickedChildWindow(e.Center, (View)sender, new View[] { cDataStack }))
                    //{

                    //    Debug.WriteLine("[MULTISELECTION] Closing");
                    //    try
                    //    {
                    //        DismissDialog(ValuesList);
                    //    }
                    //    catch (Exception exception)
                    //    {
                    //    }

                    //}


                    //_gridOnDownLock = false;

                });
            }
        }

        public ICommand CommandInternalClose
        {
            get
            {
                return new Command(async (object context) =>
                {
                    await TryCloseDialog(false);
                });
            }
        }

        public IEnumerable<ISelectableOption> SelectedMany => ItemsSource.Where(x => x.Selected).ToList();

        public ISelectableOption SelectedOne => ItemsSource.FirstOrDefault(x => x.Selected);

        public OptionItem SelectedOption
        {
            get
            {
                return SelectedOne as OptionItem;
            }
        }

        public string SelectedButton
        {
            get
            {
                return SelectedOne != null ? SelectedOne.Title : string.Empty;
            }
        }

        public void SetSource(IEnumerable<string> buttons)
        {
            var newList = new List<OptionItem>();
            var index = -1;
            foreach (var line in buttons)
            {
                index++;
                var newItem =
                    new OptionItem
                    {
                        Id = index.ToString(),
                        //Title = $"{item.Title} ({item.Id})",
                        Title = $"{line}",
                        Selected = false
                    };
                newList.Add(newItem);
            }

            ItemsSource = newList;
        }

        protected async Task<bool> TryCloseDialog(bool validate = true)
        {
            try
            {
                if (NeedSelectMore && validate)
                {
                    _gridOnDownLock = false;
                    _gridOnDownTracked = false;
                    return false;
                }
                else
                {
                    Dismiss();
                    return true;
                }
            }
            catch (Exception e)
            {
                System.Console.WriteLine(e);
                return false;
            }
        }

        public ICommand CommandInternalSubmit
        {
            get
            {
                return new Command(async (object context) =>
                {
                    if (await TryCloseDialog())
                    {
                        if (Multiselect)
                        {
                            CommandOnSubmit?.Execute(ItemsSource);
                        }
                        else
                        {
                            CommandOnSubmit?.Execute(this.SelectedOption);
                        }
                    }
                });
            }
        }

        public ICommand CommandInternalCustomAction
        {
            get
            {
                return new Command(async (object context) =>
                {
                    if (await TryCloseDialog())
                    {
                        if (Multiselect)
                        {
                            CommandCustomAction?.Execute(ItemsSource);
                        }
                        else
                        {
                            CommandCustomAction?.Execute(this.SelectedOption);
                        }
                    }
                });
            }
        }

        private bool _tapped = false;
        public ICommand CommandInternalItemTapped
        {
            get
            {
                return new Command(async (object context) =>
                {

                    if (_tapped)
                        return;

                    _tapped = true;

                    _gridOnDownFired = false;

                    Debug.WriteLine("[MULTISELECTION] Tapped");


                    var item = context as ISelectableOption;
                    if (item == null)
                    {
                        throw new Exception("OptionsPicker: Need to pass items as parameter to tapped command");
                    }

                    //todo process tap
                    try
                    {
                        var oldValue = item.Selected;
                        if (!Multiselect)
                        {
                            if (oldValue)
                            {
                                //already selected
                                await Task.Delay(SubmitDelayMs);
                                CommandInternalSubmit.Execute(null);
                                return;
                            }
                            //deselect all
                            foreach (var key in ItemsSource)
                            {
                                if (key == item)
                                {
                                    key.Selected = true;
                                }
                                else
                                    key.Selected = false;
                            }
                            //select one
                            await Task.Delay(SubmitDelayMs);
                            CommandInternalSubmit.Execute(null);
                            return;
                        }

                        if (CanSelect(oldValue))
                        {
                            //inverse
                            item.Selected = !item.Selected;
                        }
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                        throw;
                    }

                    UpdateDisplay();


                    // modify ValuesList



                    //do not exit
                    //await DismissDialog(index);
                    Device.StartTimer(TimeSpan.FromMilliseconds(500), () =>
                    {
                        _tapped = false;
                        return false;
                    });

                });
            }
        }


        private string _Display;
        public new string Display
        {
            get { return _Display; }
            set
            {
                if (_Display != value)
                {
                    _Display = value;
                    OnPropertyChanged();
                }
            }
        }




        #endregion

        #region PROPERTIES  

        //-------------------------------------------------------------
        // ItemsSource
        //-------------------------------------------------------------
        private const string nameItemsSource = "ItemsSource";
        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameItemsSource, typeof(IEnumerable<ISelectableOption>), typeof(OptionsPicker), null); //, BindingMode.TwoWay
        public IEnumerable<ISelectableOption> ItemsSource
        {
            get { return (IEnumerable<ISelectableOption>)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }


        //-------------------------------------------------------------
        // Multiselect
        //-------------------------------------------------------------
        private const string nameMultiselect = "Multiselect";
        public static readonly BindableProperty MultiselectProperty = BindableProperty.Create(nameMultiselect, typeof(bool), typeof(OptionsPicker), true); //, BindingMode.TwoWay
        public bool Multiselect
        {
            get { return (bool)GetValue(MultiselectProperty); }
            set { SetValue(MultiselectProperty, value); }
        }


        //-------------------------------------------------------------
        // CommandOnSubmit
        //-------------------------------------------------------------
        private const string nameCommandOnSubmit = "CommandOnSubmit";
        public static readonly BindableProperty CommandOnSubmitProperty = BindableProperty.Create(nameCommandOnSubmit, typeof(ICommand), typeof(OptionsPicker), null, BindingMode.OneTime);
        public ICommand CommandOnSubmit
        {
            get { return (ICommand)GetValue(CommandOnSubmitProperty); }
            set { SetValue(CommandOnSubmitProperty, value); }
        }

        //-------------------------------------------------------------
        // CommandCustomAction
        //-------------------------------------------------------------
        private const string nameCommandCustomAction = "CommandCustomAction";
        public static readonly BindableProperty CommandCustomActionProperty = BindableProperty.Create(nameCommandCustomAction, typeof(ICommand), typeof(OptionsPicker), null, BindingMode.OneTime);
        public ICommand CommandCustomAction
        {
            get { return (ICommand)GetValue(CommandCustomActionProperty); }
            set { SetValue(CommandCustomActionProperty, value); }
        }


        //-------------------------------------------------------------
        // CustomActionTitle
        //-------------------------------------------------------------
        private const string nameCustomActionTitle = "CustomActionTitle";
        public static readonly BindableProperty CustomActionTitleProperty = BindableProperty.Create(nameCustomActionTitle, typeof(string), typeof(OptionsPicker), string.Empty); //, BindingMode.TwoWay
        public string CustomActionTitle
        {
            get { return (string)GetValue(CustomActionTitleProperty); }
            set { SetValue(CustomActionTitleProperty, value); }
        }


        #endregion

        #region LOGIC

        public void UpdateDisplay()
        {
            if (MultiselectMax < 1)
            {
                Display = Title;
                //cDisplay.Text = Display;
                return;
            }
            if (MultiselectMin < 1)
                Display = string.Format(Title, CountSelected, MultiselectMax);
            else
                Display = string.Format(Title, CountSelected, MultiselectMax, MultiselectMin);
            //cDisplay.Text = Display;
        }


        bool CanSelect(bool oldV)
        {
            //check max
            bool maxOk = false;
            bool minOk = false;

            if (MultiselectMax < 1) maxOk = true;
            if (CountSelected < MultiselectMax) maxOk = true;
            if (CountSelected == MultiselectMax && oldV) maxOk = true;

            if (MultiselectMin < 1) minOk = true;
            if (CountSelected < MultiselectMax || MultiselectMax < 1)
                minOk = true;
            if (CountSelected == MultiselectMin && !oldV) minOk = true;

            if (minOk && maxOk) return true;
            return false;
        }





        public int CountSelected

        {
            get
            {
                if (ItemsSource == null)
                    return 0;
                return ItemsSource.Count(x => x.Selected);
            }
        }


        public bool NeedSelectMore

        {
            get
            {
                if (MultiselectMin < 1) return false;
                if (CountSelected < MultiselectMin) return true;
                return false;
            }
        }

        private int _MultiselectMin;
        public int MultiselectMin
        {
            get { return _MultiselectMin; }
            set
            {
                if (_MultiselectMin != value)
                {
                    _MultiselectMin = value;
                    OnPropertyChanged();
                    UpdateDisplay();
                }
            }
        }

        private int _MultiselectMax;
        public int MultiselectMax
        {
            get { return _MultiselectMax; }
            set
            {
                if (_MultiselectMax != value)
                {
                    _MultiselectMax = value;
                    OnPropertyChanged();
                    UpdateDisplay();
                }
            }
        }



        #endregion

        #region HELPERS


        public bool ClickedChildWindow(Point click, View parent, View[] children)

        {
            var screenDensity = Super.Screen.Density;
            //Get parent screen abs pos in pixels
            //We are using native code get absolute screen position
            var positionParent = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(parent);

            foreach (var child in children)
            {
                //Gets childs (hotspots) screen abs position in pixels
                var positionChild = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(child);

                //Gets childs (hotspots) rectangles, everything in pixels using screen density
                var rectChild = new Rect(positionChild, new Size((int)(child.Width * screenDensity), (int)(child.Height * screenDensity)));

                //Convert the finger XY to screen pos in pixels
                var positionClick = new Point((int)(positionParent.X + click.X * screenDensity),
                    (int)(positionParent.Y + click.Y * screenDensity)); //absolute relative to screen

                if (rectChild.Contains(positionClick))
                    return true;
            }


            return false;
        }


        #endregion

    }
}
