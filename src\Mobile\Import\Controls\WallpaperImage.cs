﻿using System.Threading.Tasks;
using AppoMobi.Common.Dto;
using AppoMobi.Tenant;
using FFImageLoading.Maui;
using FFImageLoading.Transformations;


namespace AppoMobi
{
    //****************************************************
    public class WallpaperImage : CImage
    //****************************************************
    {
        private static ImageSource imgDefault { get; set; }



        //-------------------------------------------------------------
        // CustomImageId
        //-------------------------------------------------------------
        private const string nameCustomImageId = "CustomImageIdId";
        public static readonly BindableProperty CustomImageIdProperty = BindableProperty.Create(nameCustomImageId, typeof(string), typeof(WallpaperImage), null); //, BindingMode.TwoWay
        public string CustomImageId
        {
            get { return (string)GetValue(CustomImageIdProperty); }
            set { SetValue(CustomImageIdProperty, value); }
        }

        //-------------------------------------------------------------
        // Darken
        //-------------------------------------------------------------
        private const string nameDarken = "Darken";
        public static readonly BindableProperty DarkenProperty = BindableProperty.Create(nameDarken, typeof(double), typeof(WallpaperImage), 0.0); //, BindingMode.TwoWay
        public double Darken
        {
            get { return (double)GetValue(DarkenProperty); }
            set { SetValue(DarkenProperty, value); }
        }	


             
        // DO NOT CLOSE - OnPropertyChanged
        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                //property changed
                case nameDarken:
                    Opacity = 1.0 - Darken;
                    break;

                //property changed
                case nameCustomImageId:
                    var generatedFilename = BaseMobileDto.GetThumbnailUrl(CustomImageId, "large", TenantOptions.TenantKey);
                    //var generatedFilename = Core.AssemblyName + $".Images.{Source}";
                    Source = generatedFilename;
                    break;
            }
        }

        
        public WallpaperImage()
        
        {
            if (imgDefault == null)
            {
                imgDefault = "back.jpg";//resource://AppoMobi.Mobile.Images.Brand.
            }

            Aspect = Aspect.AspectFill;
            DownsampleToViewSize = false;
            IsOpaque = true;
            FadeAnimationEnabled = false;
            HorizontalOptions = LayoutOptions.FillAndExpand;
            VerticalOptions = LayoutOptions.FillAndExpand;
            Error += OnError;
            //Success += OnSuccess;

            //Transformations.Add(new ColorSpaceTransformation(new float[][] {
            //    new float[] { 1, 0, 0, 0, 0 },
            //    new float[] { 0, 1, 0, 0, 0 },
            //    new float[] { 0, 0, 1, 0, 0 },
            //    new float[] { -0.05f, -0.05f, -0.05f, 1, 0 },
            //    new float[] { 0, 0, 0, 0, 1 }}
            //));


                Source = imgDefault;
            
        }
        
        private async void OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        
        {

            await Task.Delay(500);

            var BW = new GrayscaleTransformation();
            Transformations.Add(BW);

            var Blur = new BlurredTransformation(2.2);
            Transformations.Add(Blur);

        }

        private bool once { get; set; }
        
        private void OnError(object sender, CachedImageEvents.ErrorEventArgs e)
            
        {
            if (once) return;
            once = true;
        }
    }
}
