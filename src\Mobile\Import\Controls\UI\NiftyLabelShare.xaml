﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyLabelShare"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:appoMobi="clr-namespace:AppoMobi">

    <ContentView.Content>


        <Grid
            x:Name="ControlContainer"
            Padding="15,0,15,10"
            ColumnSpacing="8">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="OnTapped" />
            </Grid.GestureRecognizers>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <!--  column 0 - ICON  -->
            <appoMobi:CImage
                x:Name="ControlLabelIcon"
                Grid.Column="0"
                Margin="{StaticResource NiftyLabelIconPadding}"
                DownsampleToViewSize="False"
                VerticalOptions="Center"
                WidthRequest="17">
                <appoMobi:CImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_MenuIconSelected}" />
                </appoMobi:CImage.Transformations>
            </appoMobi:CImage>
            <!--  column 1 - LABEL  -->
            <Label
                x:Name="ControlLabelText"
                Grid.Column="1"
                FontSize="18"
                TextColor="{x:Static appoMobi:AppColors.AccentDark75}"
                VerticalOptions="Center" />
            <!--  column 2 - SHARE ICON  -->
            <appoMobi:CImage
                Grid.Column="2"
                DownsampleToViewSize="False"
                HeightRequest="25"
                Source="sharew"
                VerticalOptions="Center"
                WidthRequest="25">
                <appoMobi:CImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_LinkIcon}" />
                </appoMobi:CImage.Transformations>
                <appoMobi:CImage.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnShared" />
                </appoMobi:CImage.GestureRecognizers>
            </appoMobi:CImage>
        </Grid>




    </ContentView.Content>

</ContentView>