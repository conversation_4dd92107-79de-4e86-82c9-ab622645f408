﻿namespace AppoMobi.Framework.Api
{
    public class ApiRequestPaged : ApiRequestPagedDto
    {
        public ApiRequestPaged()
        {
            Refresh = true;
            PageSize = 20;
            PageNb = 0;
        }

        public bool Refresh { get; set; }
    }

    public class ApiRequestPagedDto : DataListRequestOptions
    {
      

        //      public string Lang { get; set; }
    }

    //public class ApiRequestImamRequests: ApiRequestPagedDto
    //{
    //    public bool WithRelated { get; set; } 
    //}

    //public class ApiRequestRoutesDto : ApiRequestPagedWithOptionalDatesDto
    //{

    //    public bool SeatsAvalable { get; set; }
    //}

    public class ApiRequestWithFriendsDto : ApiRequestPagedWithOptionalDatesDto
    {
        public void Init()
        {
            if (PageSize < 1)
                PageSize = 20;
        }

        public bool Friends { get; set; }
    }

    //public class ApiRequestRequestsAsRoutesDto : ApiRequestPagedWithOptionalDatesDto
    //{
    //    ///// <summary>
    //    ///// Include my created content
    //    ///// </summary>
    //    //public bool Master { get; set; }

    //    public string RequestFilter { get; set; }
    //}

}