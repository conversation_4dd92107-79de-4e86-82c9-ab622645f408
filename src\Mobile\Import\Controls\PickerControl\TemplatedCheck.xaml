﻿<?xml version="1.0" encoding="UTF-8" ?>
<controls:TouchGrid
    x:Class="AppoMobi.Forms.Controls.Input.TemplatedCheck"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    x:Name="This"
    HeightRequest="16"
    HorizontalOptions="Start"
    TappedCommand="{Binding Source={x:Reference This}, Path=CommandInternalTapped}"
    VerticalOptions="Start"
    WidthRequest="16"
    mc:Ignorable="d">


    <Frame
        Margin="0"
        Padding="0"
        BackgroundColor="White"
        BorderColor="{StaticResource ColorPrimaryLight}"
        CornerRadius="0"
        HasShadow="False"
        HeightRequest="16"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        WidthRequest="16" />

    <!--<svg:SvgIcon
        HeightRequest="16"
        HorizontalOptions="Start"
        IconFilePath="{Binding Source={x:Reference This}, Path=SvgUnselected}"
        TintColor="{StaticResource ColorPrimaryLight}"
        VerticalOptions="Start"
        WidthRequest="16" />-->

    <BoxView
        Margin="0,0,0.,0"
        BackgroundColor="{StaticResource ColorAccentDark}"
        HeightRequest="8"
        HorizontalOptions="Center"
        IsVisible="{Binding Source={x:Reference This}, Path=Checked}"
        VerticalOptions="Center"
        WidthRequest="8" />

    <!--<svg:SvgIcon
        BackgroundColor="Aquamarine"
        HeightRequest="8"
        HorizontalOptions="Center"
        IconFilePath="{Binding Source={x:Reference This}, Path=SvgSelected}"
        IsVisible="{Binding Source={x:Reference This}, Path=Checked}"
        TintColor="{StaticResource ColorAccentDark}"
        VerticalOptions="Center"
        WidthRequest="8" />-->


</controls:TouchGrid>