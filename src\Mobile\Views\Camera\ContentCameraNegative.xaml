﻿<?xml version="1.0" encoding="utf-8" ?>

<pages:IncludedContent
    x:Class="AppoMobi.Forms.Content.Camera.ContentCameraNegative"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:camera="clr-namespace:AppoMobi.Forms.Content.Camera"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.Content.Camera.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:main="clr-namespace:AppoMobi.Main"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="ThisPage"
    Margin="0"
    Padding="0"
    x:DataType="main:MainVModel"
    BackgroundColor="{x:Static appoMobi:AppColors.Primary}">


    <Grid
        HeightRequest="{Binding Source={x:Reference ThisPage}, Path=Height}"
        RowDefinitions="*"
        VerticalOptions="Start">


        <controls1:CameraPreview
            x:Name="Camera"
            CustomAlbum="{Binding CustomAlbum}"
            Filter="ColorNegativeAuto"
            Geotag="{Binding Geotag}"
            HeightRequest="{Binding Source={x:Reference ThisPage}, Path=Height}"
            HorizontalOptions="Fill"
            IsEnabled="False"
            Position="Default"
            StyleId="NegativeCamera"
            Type="Default"
            VerticalOptions="Fill" />

        <!--  CONTROLS  -->
        <Grid IsVisible="{Binding Source={x:Reference Camera}, Path=IsEnabled}" VerticalOptions="Fill">


            <!--  FIT-FILL  -->
            <StackLayout
                Margin="0,30,20,0"
                HorizontalOptions="End"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
                Spacing="20"
                VerticalOptions="Start">

                <controls:TouchFrame
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Screen"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.Expand}" />
                </controls:TouchFrame>

                <Label
                    FontAttributes="Bold"
                    FontSize="12"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Text="AUTO"
                    TextColor="#99FFFFFF">
                    <Label.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=DisplayMode}"
                            TargetType="Label"
                            Value="2">
                            <Setter Property="Text" Value="FILL" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=DisplayMode}"
                            TargetType="Label"
                            Value="1">
                            <Setter Property="Text" Value="FIT" />
                        </DataTrigger>
                    </Label.Triggers>
                </Label>


            </StackLayout>

            <!--  GAMMA  -->
            <StackLayout
                Margin="20,0,0,0"
                HorizontalOptions="Start"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
                Spacing="30"
                VerticalOptions="Center">

                <!--  PLUS  -->
                <controls:TouchFrame
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Plus"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.Plus}" />
                </controls:TouchFrame>

                <!--  MINUS  -->
                <controls:TouchFrame
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Minus"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.Minus}" />
                </controls:TouchFrame>

            </StackLayout>

            <!--  FPS  -->
            <!--  IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"  -->

            <!--<Label
                Margin="0,30,0,0"
                FontAttributes="Bold"
                FontSize="12"
                HorizontalOptions="Center"
                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                Text="{Binding Source={x:Reference Camera}, Path=FPS}"
                TextColor="Aqua" />-->

            <!--  PRESETS BTNS  -->
            <StackLayout
                Margin="0,0,20,0"
                HorizontalOptions="End"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowPresets}"
                Spacing="20"
                VerticalOptions="Center">

                <!--  1 - 2  -->
                <controls:TouchFrame
                    IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowPresets}"
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Preset"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.One}">
                        <xam:FontIconLabel.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference Camera}, Path=ColorPreset, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=0}"
                                TargetType="xam:FontIconLabel"
                                Value="true">
                                <Setter Property="Text" Value="{x:Static xam:FaPro.One}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference Camera}, Path=ColorPreset, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=1}"
                                TargetType="xam:FontIconLabel"
                                Value="true">
                                <Setter Property="Text" Value="{x:Static xam:FaPro.Two}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference Camera}, Path=ColorPreset, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=2}"
                                TargetType="xam:FontIconLabel"
                                Value="true">
                                <Setter Property="Text" Value="{x:Static xam:FaPro.Three}" />
                            </DataTrigger>
                        </xam:FontIconLabel.Triggers>
                    </xam:FontIconLabel>
                </controls:TouchFrame>

                <Label
                    FontAttributes="Bold"
                    FontSize="12"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Text="AUTO"
                    TextColor="#99FFFFFF">
                    <Label.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=ColorPreset, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=0}"
                            TargetType="Label"
                            Value="true">
                            <Setter Property="Text" Value="AUTO" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=ColorPreset, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=1}"
                            TargetType="Label"
                            Value="true">
                            <Setter Property="Text" Value="FLAT" />
                        </DataTrigger>
                    </Label.Triggers>
                </Label>


            </StackLayout>

            <!--  BTN CLOSE  -->
            <controls:TouchFrame
                x:Name="BtnClose"
                Margin="0,0,20,30"
                HorizontalOptions="End"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Close"
                TimeLockDownMs="1000"
                VerticalOptions="End">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Xmark}" />
            </controls:TouchFrame>

            <!--  PICKER MODE BLACK  -->
            <!--<StackLayout
                        x:Name="PickerStack"
                        Margin="30,0,30,0"
                        BackgroundColor="#66000000"
                        IsVisible="{Binding Source={x:Reference Camera}, Path=PickerMode, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=1}"
                        VerticalOptions="End">

                        <Label
                            Margin="30,20,30,20"
                            FontAttributes="Bold"
                            FontSize="14"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            Text="Нажите на область экрана с черным базовым цветом негатива"
                            TextColor="White"
                            VerticalOptions="Center" />

                        <controls:TouchFrame
                            Margin="30,0,30,20"
                            Padding="0"
                            BackgroundColor="Transparent"
                            BorderColor="White"
                            CornerRadius="18"
                            HasShadow="False"
                            HeightRequest="36"
                            Tapped="OnTapped_ResetBlack"
                            VerticalOptions="Start">

                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Text="Закрыть"
                                TextColor="White"
                                VerticalOptions="Center" />

                        </controls:TouchFrame>

                    </StackLayout>-->

            <!--  PICKER MODE WHITE  -->
            <!--<StackLayout
                        x:Name="PickerStackWhite"
                        Margin="30,0,30,0"
                        BackgroundColor="#66000000"
                        IsVisible="{Binding Source={x:Reference Camera}, Path=PickerMode, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=3}"
                        VerticalOptions="End">

                        <Label
                            Margin="30,20,30,20"
                            FontAttributes="Bold"
                            FontSize="14"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            Text="Нажите на область экрана с белым базовым цветом негатива"
                            TextColor="White"
                            VerticalOptions="Center" />

                        <controls:TouchFrame
                            Margin="30,0,30,20"
                            Padding="0"
                            BackgroundColor="Transparent"
                            BorderColor="White"
                            CornerRadius="18"
                            HasShadow="False"
                            HeightRequest="36"
                            Tapped="OnTapped_ResetWhite"
                            VerticalOptions="Start">

                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Text="Закрыть"
                                TextColor="White"
                                VerticalOptions="Center" />

                        </controls:TouchFrame>

                    </StackLayout>-->

            <!--  BTN RESET  -->
            <controls:TouchFrame
                x:Name="BtnReset"
                Margin="20,30,0,0"
                HorizontalOptions="Start"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowReset}"
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Reset"
                TimeLockDownMs="1000"
                VerticalOptions="Start">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.RotateLeft}" />
            </controls:TouchFrame>

            <!--  CAMERA BTNS  -->
            <StackLayout
                x:Name="ButtonsStack"
                Margin="0,0,0,30"
                HorizontalOptions="Center"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings, Converter={x:StaticResource NotConverter}}"
                Orientation="Horizontal"
                Spacing="30"
                VerticalOptions="End">

                <!--<controls:TouchFrame Style="{StaticResource CameraButton}" Tapped="OnTapped_Switch">
                        <xam:FontIconLabel Style="{StaticResource CameraIcon}" Text="{x:Static xam:FaPro.CodeCommit}" />
                    </controls:TouchFrame>-->


                <!--<controls:TouchFrame Style="{StaticResource CameraButton}" Tapped="OnTapped_Pick">
                        <xam:FontIconLabel
                            Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={camera:OrientationToRotationConverter}}"
                            Style="{StaticResource CameraIcon}"
                            Text="{x:Static xam:FaPro.EyeDropper}" />
                    </controls:TouchFrame>-->

                <!--  BTN SETTINGS  -->
                <controls:TouchFrame
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Settings"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.Gear}" />
                </controls:TouchFrame>


                <!--  BTN FILTER EFFECT  -->
                <!--  SvgIcon  -->
                <draw:Canvas
                    Gestures="Lock"
                    HeightRequest="44"
                    HorizontalOptions="Center"
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    VerticalOptions="Center"
                    WidthRequest="44">

                    <camera:TouchDrawnShape
                        BackgroundColor="#99FFFFFF"
                        HorizontalOptions="Fill"
                        StrokeColor="#33000000"
                        StrokeWidth="1"
                        Tapped="OnTapped_Filter"
                        Type="Circle"
                        UseCache="Image"
                        VerticalOptions="Fill">

                        <draw:SkiaSvg
                            HeightRequest="24"
                            HorizontalOptions="Center"
                            Opacity="0.525"
                            VerticalOptions="Center"
                            WidthRequest="24">

                            <draw:SkiaSvg.Triggers>
                                <DataTrigger
                                    Binding="{Binding Source={x:Reference Camera}, Path=Filter, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=4}"
                                    TargetType="draw:SkiaSvg"
                                    Value="true">
                                    <Setter Property="SvgString" Value="{x:StaticResource SvgCamPresetMono}" />
                                </DataTrigger>
                                <DataTrigger
                                    Binding="{Binding Source={x:Reference Camera}, Path=Filter, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=2}"
                                    TargetType="draw:SkiaSvg"
                                    Value="true">
                                    <Setter Property="SvgString" Value="{x:StaticResource SvgCamPresetColor}" />
                                </DataTrigger>
                                <DataTrigger
                                    Binding="{Binding Source={x:Reference Camera}, Path=Filter, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=3}"
                                    TargetType="draw:SkiaSvg"
                                    Value="true">
                                    <Setter Property="SvgString" Value="{x:StaticResource SvgCamPresetColor}" />
                                </DataTrigger>
                            </draw:SkiaSvg.Triggers>

                        </draw:SkiaSvg>

                    </camera:TouchDrawnShape>

                </draw:Canvas>


                <!--<controls:TouchFrame Style="{StaticResource CameraButton}" Tapped="OnTapped_Capture">
                            <xam:FontIconLabel
                            Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={camera:OrientationToRotationConverter}}"
                            Style="{StaticResource CameraIcon}"
                            Text="{x:Static xam:FaPro.CameraRetro}" />
                        </controls:TouchFrame>-->

                <!--  BTN TAKE PHOTO  -->
                <controls:TouchFrame
                    Style="{StaticResource CameraButton}"
                    Tapped="OnTapped_Capture2"
                    TimeLockDownMs="1000">
                    <xam:FontIconLabel
                        Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                        Style="{StaticResource CameraIcon}"
                        Text="{x:Static xam:FaPro.Camera}">
                        <xam:FontIconLabel.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                                TargetType="xam:FontIconLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="#88883333" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                                TargetType="xam:FontIconLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="#88000000" />
                            </DataTrigger>
                        </xam:FontIconLabel.Triggers>
                    </xam:FontIconLabel>
                    <controls:TouchFrame.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                            TargetType="controls:TouchFrame"
                            Value="True">
                            <Setter Property="InputTransparent" Value="True" />
                            <!--<Setter Property="Opacity" Value="0.5" />-->
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                            TargetType="controls:TouchFrame"
                            Value="False">
                            <Setter Property="InputTransparent" Value="False" />
                            <!--<Setter Property="Opacity" Value="1.0" />-->
                        </DataTrigger>
                    </controls:TouchFrame.Triggers>
                </controls:TouchFrame>


            </StackLayout>

            <!--  FPS  -->
            <Label
                Margin="0,80,0,0"
                BackgroundColor="Black"
                FontAttributes="Bold"
                FontSize="12"
                HorizontalOptions="Center"
                IsVisible="{Binding Source={x:Reference ThisPage}, Path=IsDebug}"
                Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                Text="{Binding Source={x:Reference Camera}, Path=FPS}"
                TextColor="Red"
                VerticalOptions="Start" />

            <!--  PICKER BTNS  -->
            <!--<ContentView
                        x:Name="GridPickerBtns1"
                        Margin="30,0,0,0"
                        HorizontalOptions="Start"
                        IsVisible="{Binding Source={x:Reference Camera}, Path=Filter, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=3}"
                        VerticalOptions="End">

            -->
            <!--  BTN PICK BLACK  -->
            <!--
                        <controls:TouchFrame
                            BackgroundColor="#99000000"
                            HorizontalOptions="Start"
                            IsVisible="{Binding Source={x:Reference Camera}, Path=PickerMode, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=0}"
                            Reaction="Tint"
                            Scale="0.75"
                            Style="{StaticResource CameraButton}"
                            Tapped="OnTapped_PickBlack"
                            VerticalOptions="Start">
                            <xam:FontIconLabel
                                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={camera:OrientationToRotationConverter}}"
                                Style="{StaticResource CameraIcon}"
                                Text="{x:Static xam:FaPro.EyeDropper}"
                                TextColor="White" />
                        </controls:TouchFrame>

                    </ContentView>

                    <ContentView
                        x:Name="GridPickerBtns2"
                        Margin="0,0,30,0"
                        HorizontalOptions="End"
                        IsVisible="{Binding Source={x:Reference Camera}, Path=Filter, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=3}"
                        VerticalOptions="End">

            -->
            <!--  BTN PICK WHITE  -->
            <!--
                        <controls:TouchFrame
                            HorizontalOptions="End"
                            IsVisible="{Binding Source={x:Reference Camera}, Path=PickerMode, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter=0}"
                            Reaction="Tint"
                            Scale="0.75"
                            Style="{StaticResource CameraButton}"
                            Tapped="OnTapped_PickWhite"
                            VerticalOptions="Start">
                            <xam:FontIconLabel
                                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={camera:OrientationToRotationConverter}}"
                                Style="{StaticResource CameraIcon}"
                                Text="{x:Static xam:FaPro.EyeDropper}"
                                TextColor="Black" />
                        </controls:TouchFrame>

                    </ContentView>-->


            <!--<Label
                        Margin="-20"
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Reference ThisPage}, Path=ModeDesc}"
                        TextColor="Red"
                        VerticalOptions="End" />-->

        </Grid>

        <Grid IsVisible="{Binding Source={x:Reference ThisPage}, Path=PermissionsWarning}" VerticalOptions="FillAndExpand">

            <Label
                FontSize="15"
                HorizontalOptions="Center"
                Text="{x:Static resX:ResStrings.NoPermissions}"
                TextColor="White"
                VerticalOptions="Center" />

        </Grid>

    </Grid>


</pages:IncludedContent>