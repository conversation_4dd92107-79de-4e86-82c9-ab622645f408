﻿using System;
using System.Threading.Tasks;
using AppoMobi.Touch;
using Microsoft.Maui.Controls.Shapes;



namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class GsButton
    {

        
	    protected override void OnSizeAllocated(double width, double height)
        
	    {
	        base.OnSizeAllocated(width, height);


//            if (width>0) RendererNeedUpdate?.Invoke(this, null);
        }

	    
        public GsButton()
        
		{
			InitializeComponent ();

            cFrame.StrokeThickness = 1;

            HasShadow = false;

            GradientOrientation = StackOrientation.Vertical;

            if (DeviceInfo.Platform != DevicePlatform.Android)
            {
                cLabel.VerticalTextAlignment = TextAlignment.Center;
            }


		    SetupIndicator();
		    SetupStyle(StyleOptions);
		}

        
	    private void SetupIndicator()
        
	    {
	      

        }

        
	    private void SetDisable(bool on)
        
	    {
	        if (!on)
	        {
	            Opacity = 1;
	        }
	        else
	        {
	            Opacity = 0.5;
	        }
	    }

        
	    private void SetupStyle(ButtonStyle style)
        
	    {
	        switch (style)
	        {
	            case ButtonStyle.Normal:

                    if (DeviceInfo.Platform != DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#20000000");
                        StartColor = Colors.White;//FromHex("#ff2727");
                        EndColor = Colors.White;//("#ff2727");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#20000000");
                        StartColor = Colors.Transparent;//FromHex("#ff2727");
                        EndColor = Colors.Transparent;//("#ff2727");
                    }


//                    ShadowColor = Colors.FromHex("#20000000");

	                TextColor = Colors.Black;
	                break;

	            case ButtonStyle.Active:
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#a0000000");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#8182f7");
                    }

	                StartColor = BackColors.GradientStart;
	                EndColor = BackColors.GradientEnd;
	                TextColor = Colors.White;
	                break;

	            case ButtonStyle.Error:

	                break;

	            case ButtonStyle.Success:
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#a0000000");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#51be37");
                    }

                    TextColor = Colors.White;
	                break;

	        }

	        //cGradient.RendererNeedUpdate?.Invoke(this, null);
        }

	    
	    // Tag
	    
	    private const string nameTag = "Tag";
	    public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(GsButton), string.Empty);
	    public string Tag
	    {
	        get { return (string)GetValue(TagProperty); }
	        set { SetValue(TagProperty, value); }
	    }


        //-------------------------------------------------------------
        // Index
        //-------------------------------------------------------------
        private const string nameIndex = "Index";
        public static readonly BindableProperty IndexProperty = BindableProperty.Create(nameIndex, typeof(int), typeof(GsButton), 0); //, BindingMode.TwoWay
        public int Index
        {
            get { return (int)GetValue(IndexProperty); }
            set { SetValue(IndexProperty, value); }
        }	


        
        // Text
        
        private const string nameText = "Text";
	    public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(GsButton), string.Empty);
	    public string Text
	    {
	        get { return (string)GetValue(TextProperty); }
	        set { SetValue(TextProperty, value); }
	    }

	    //-------------------------------------------------------------
	    // Selected
	    //-------------------------------------------------------------
	    private const string nameSelected = "Selected";
	    public static readonly BindableProperty SelectedProperty = BindableProperty.Create(nameSelected, typeof(bool), typeof(GsButton), false); //, BindingMode.TwoWay
	    public bool Selected
	    {
	        get { return (bool)GetValue(SelectedProperty); }
	        set { SetValue(SelectedProperty, value); }
	    }

        //-------------------------------------------------------------
        // TextColor
        //-------------------------------------------------------------
        private const string nameTextColor = "TextColor";
	    public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameTextColor, typeof(Color), typeof(GsButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color TextColor
	    {
	        get { return (Color)GetValue(TextColorProperty); }
	        set { SetValue(TextColorProperty, value); }
	    }



        //-------------------------------------------------------------
        // StyleOptions
        //-------------------------------------------------------------
        private const string nameStyleOptions = "StyleOptions";
        public static readonly BindableProperty StyleOptionsProperty = BindableProperty.Create(nameStyleOptions, typeof(ButtonStyle), typeof(GsButton), ButtonStyle.Normal); //, BindingMode.TwoWay
        public ButtonStyle StyleOptions
        {
            get { return (ButtonStyle)GetValue(StyleOptionsProperty); }
            set { SetValue(StyleOptionsProperty, value); }
        }

	    
	    public enum ButtonStyle
	    
	    {
	        Normal,
	        Active,
	        Error,
            Success
	    }



        //-------------------------------------------------------------
        // Disabled
        //-------------------------------------------------------------
        private const string nameDisabled = "Disabled";
        public static readonly BindableProperty DisabledProperty = BindableProperty.Create(nameDisabled, typeof(bool), typeof(GsButton), false); //, BindingMode.TwoWay
        public bool Disabled
        {
            get { return (bool)GetValue(DisabledProperty); }
            set { SetValue(DisabledProperty, value); }
        }	

        //-------------------------------------------------------------
        // ShadowColor
        //-------------------------------------------------------------
        private const string nameShadowColor = "ShadowColor";
	    public static readonly BindableProperty ShadowColorProperty = BindableProperty.Create(nameShadowColor, typeof(Color), typeof(GsButton), Color.Parse("#28000000")); //, BindingMode.TwoWay
	    public Color ShadowColor
	    {
	        get { return (Color)GetValue(ShadowColorProperty); }
	        set { SetValue(ShadowColorProperty, value); }
	    }

	

        //-------------------------------------------------------------
        // StartColor
        //-------------------------------------------------------------
        private const string nameStartColor = "StartColor";
	    public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(GsButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color StartColor
	    {
	        get { return (Color)GetValue(StartColorProperty); }
	        set { SetValue(StartColorProperty, value); }
	    }

        //-------------------------------------------------------------
        // EndColor
        //-------------------------------------------------------------
        private const string nameEndColor = "EndColor";
	    public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(GsButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color EndColor
	    {
	        get { return (Color)GetValue(EndColorProperty); }
	        set { SetValue(EndColorProperty, value); }
	    }


        //-------------------------------------------------------------
        // GradientOrientation
        //-------------------------------------------------------------
        private const string nameGradientOrientation = "GradientOrientation";
        public static readonly BindableProperty GradientOrientationProperty = BindableProperty.Create(nameGradientOrientation, typeof(StackOrientation), typeof(GsButton), StackOrientation.Horizontal); //, BindingMode.TwoWay
        public StackOrientation GradientOrientation
        {
            get { return (StackOrientation)GetValue(GradientOrientationProperty); }
            set { SetValue(GradientOrientationProperty, value); }
        }


        //-------------------------------------------------------------
        // FontSize
        //-------------------------------------------------------------
        private const string nameFontSize = "FontSize";
        public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize, typeof(double), typeof(GsButton), 12.0); //, BindingMode.TwoWay
        public double FontSize
        {
            get { return (double)GetValue(FontSizeProperty); }
            set { SetValue(FontSizeProperty, value); }
        }

        //property changed
        //case nameFontSize:
        //             ControlName.FontSize = FontSize;
        //             break;		




       //property changed
       //case nameBorderRadius:
       //             ControlName.BorderRadius = BorderRadius;
       //             break;		


        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {
	            //property changed
	            case nameDisabled:
	                SetDisable(Disabled);
	                break;

                case nameText:
	                cLabel.Text = Text;
	                break;

	            case nameFontSize:
	                cLabel.FontSize = FontSize;
	                break;

                case nameTextColor:
	                cLabel.TextColor = TextColor;
	                break;

	            case nameBackgroundColor:	       
                    StartColor = BackgroundColor;
	                EndColor = BackgroundColor.MakeDarker(33);
	                Update();
                    break;

                    case nameStartColor:
                        cGradient.StartColor = StartColor;
                        break;

	            case nameEndColor:
	                cGradient.EndColor = EndColor;
	                break;

                    case nameBorderColor:
                        cFrame.BackgroundColor = BorderColor;
                        break;

                case nameStyleOptions:
                    SetupStyle(StyleOptions);
                    Update();
                    break;

                case nameCornerRadius:
                    cGradient.CornerRadius =(float)CornerRadius;
                    cFrame.StrokeShape = new RoundRectangle
                    {
                        CornerRadius = new CornerRadius(CornerRadius)
                    };
                    Update();
                    break;
            }
	    }

        public void Update()
        {
            //cGradient.RendererNeedUpdate?.Invoke(this, null);
        }


        //-------------------------------------------------------------
        // CornerRadius
        //-------------------------------------------------------------
        private const string nameCornerRadius = "CornerRadius";
        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(nameCornerRadius, typeof(float), typeof(GsButton), 8.0f); //, BindingMode.TwoWay
        public float CornerRadius
        {
            get { return (float)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }	


        //-------------------------------------------------------------
        // HasShadow
        //-------------------------------------------------------------
        private const string nameHasShadow = "HasShadow";
        public static readonly BindableProperty HasShadowProperty = BindableProperty.Create(nameHasShadow, typeof(bool), typeof(GsButton), false); //, BindingMode.TwoWay
        public bool HasShadow
        {
            get { return (bool)GetValue(HasShadowProperty); }
            set { SetValue(HasShadowProperty, value); }
        }



        //-------------------------------------------------------------
        // BorderColor
        //-------------------------------------------------------------
        private const string nameBorderColor = "BorderColor";
        public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(nameBorderColor, typeof(Color), typeof(GsButton), Colors.Black); //, BindingMode.TwoWay
        public Color BorderColor
        {
            get { return (Color)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }	

       



        //-------------------------------------------------------------
        // BackgroundColor
        //-------------------------------------------------------------
        private const string nameBackgroundColor = "BackgroundColor";
        public new static readonly BindableProperty BackgroundColorProperty = BindableProperty.Create(nameBackgroundColor, typeof(Color), typeof(GsButton), Colors.Red); //, BindingMode.TwoWay
        public new Color BackgroundColor
        {
            get { return (Color)GetValue(BackgroundColorProperty); }
            set { SetValue(BackgroundColorProperty, value); }
        }

        
        private void CGrid_OnUp(object sender, DownUpEventArgs e)
	    
	    {
	        if (Disabled) return;
                // Update the UI
             //   if (DeviceInfo.Current.Platform != DevicePlatform.Android)
	                Scale = 1.0;
	            BackgroundColor = _backgroundColor;
	        Task.Delay(1);
        }

        private Color _backgroundColor;

        
        private void CGrid_OnDown(object sender, DownUpEventArgs e)
        
        {
            if (Disabled) return;

            //  Core.Native.ExecuteTask("playClickSound");

                // Update the UI
                _backgroundColor = BackgroundColor;
               // if (DeviceInfo.Current.Platform != DevicePlatform.Android)
                    Scale = 0.98;
                
                BackgroundColor = BackgroundColor.MakeLighter(25);
            Task.Delay(1);
            //StartColor = BackgroundColor.MakeDarker(20);
            //EndColor = BackgroundColor.MakeDarker(35);
        }



        public event EventHandler Tapped = null;



	    
	    public void Select(ButtonStyle style)
	    
	    {
	        StyleOptions = style;
	        Selected = true;
	    }

        
        public void Deselect(ButtonStyle style)
        
	    {
	        StyleOptions = style; 
	        Selected = false;
	    }

        private void CFrame_OnTapped(object sender, TapEventArgs e)
        {
            Tapped?.Invoke(this, e);
        }
    }
}