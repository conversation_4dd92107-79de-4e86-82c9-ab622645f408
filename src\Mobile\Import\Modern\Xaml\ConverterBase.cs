﻿using System;
using System.Globalization;
using System.Text;
using AppoMobi.Specials;



namespace AppoMobi.Forms.Framework.Xaml
{
    public class ConverterBase : IValueConverter//, IMarkupExtension
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return OnValueReceived(value, targetType, parameter, culture);
        }

        public virtual object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        //public object ProvideValue(IServiceProvider serviceProvider)
        //{

        //    return null;
        //}
    }

    /// <summary>
    /// Ex: "{xaml:SetColor Color={x:StaticResource ColorPaperCard},Darker=0.0}"
    /// </summary>
    public class SetColorExtension : IMarkupExtension<Color>
    {
        public double Darker { set; get; } = 0.0;

        public double Lighter { set; get; } = 0.0;

        public double MultiplyAlpha { set; get; } = 1.0;


        public double Hue { set; get; } = 0.0;

        public double Saturation { set; get; } = 0.0;

        public double Lightness { set; get; } = 0.0;


        public Color Color { set; get; } = Colors.White;

        public Color ProvideValue(IServiceProvider serviceProvider)
        {
            var tmp = Color;

            if (Darker > 0)
                tmp = tmp.MakeDarker(Darker);

            if (Lighter > 0)
                tmp = tmp.MakeLighter(Lighter);

            if (MultiplyAlpha < 1.0)
                tmp = tmp.MultiplyAlpha((float)MultiplyAlpha);

            if (Hue != 0.0)
            {
                tmp = tmp.WithHue((float)(tmp.GetHue() + tmp.GetHue() / 100.0 * Hue));
            }

            if (Saturation != 0.0)
            {
                tmp = tmp.WithSaturation((float)(tmp.GetSaturation() + tmp.GetSaturation() / 100.0 * Saturation));
            }

            if (Lightness != 0.0)
            {
                tmp = tmp.WithLuminosity((float)(tmp.GetLuminosity() + tmp.GetLuminosity() / 100.0 * Lightness));
            }



            return tmp;
        }

        object IMarkupExtension.ProvideValue(IServiceProvider serviceProvider)
        {
            return (this as IMarkupExtension<Color>).ProvideValue(serviceProvider);
        }
    }

    //***************************************************************************
    public static partial class ColorExtensions
    //***************************************************************************
    {

        
        public static Color ColorFromHex(this string hex)
        
        {
            if (!hex.HasContent())
                return Colors.Transparent;

            return Color.Parse(hex);
        }


        
        public static string SetAlpha(this string hex, int percent)
        
        {
            //todo check if alpha exists!!!!!!

            // strip the leading # if it's there
            hex = hex.Replace("#", "");

            UInt16 a = 255;
            var aa = (a * percent / 100).ToString("X2");

            return '#' + aa + hex;
        }
        /**
         * ('#000000', 50) --> #808080
         * ('#EEEEEE', 25) --> #F2F2F2
         * ('EEE     , 25) --> #F2F2F2
         **/

        
        public static Color MakeDarker(this Color color, double percent)
        
        {
            var rc = color.Red - color.Red / 100 * percent;
            if (rc > 1) rc = 1;
            var gc = color.Green - color.Green / 100 * percent;
            if (gc > 1) gc = 1;
            var bc = color.Blue - color.Blue / 100 * percent;
            if (bc > 1) bc = 1;
            var ret = new Color((float)rc, (float)gc, (float)bc, color.Alpha);
            return ret;
        }

        
        public static Color MakeLighter(this Color color, double percent)
        
        {
            return color.AddLuminosity((float)(color.GetLuminosity() / 100.0 * percent));

            var rc = color.Red + color.Red / 100 * percent;
            if (rc > 1) rc = 1;
            var gc = color.Green + color.Green / 100 * percent;
            if (gc > 1) gc = 1;
            var bc = color.Blue + color.Blue / 100 * percent;
            if (bc > 1) bc = 1;
            var ret = new Color((float)rc, (float)gc, (float)bc, color.Alpha);
            return ret;
        }

        /*
                
                public static Color MakeLighter(this Color color, double percent)
                
                {
                    // strip the leading # if it's there
                    var hex = color.ToHex();
                    hex = hex.Replace("#", "");

                    var r = Convert.ToUInt16(hex.Substring(0, 2), 16);
                    var g = Convert.ToUInt16(hex.Substring(2, 2), 16);
                    var b = Convert.ToUInt16(hex.Substring(4, 2), 16);

                    var rr = ((0 | (1 << 8) + r + (int)((256 - r) * percent / 100)).ToString("X")).Substring(1);
                    var gg = ((0 | (1 << 8) + g + (int)((256 - g) * percent / 100)).ToString("X")).Substring(1);
                    var bb = ((0 | (1 << 8) + b + (int)((256 - b) * percent / 100)).ToString("X")).Substring(1);

                    var new_hex = '#' + rr + gg + bb;

                    return ToColorFromHex(new_hex); //889fao

                }
        */
        
        public static string ToHex(this Color color)
        
        {
            var r = (UInt16)(color.Red * 255);
            var g = (UInt16)(color.Green * 255);
            var b = (UInt16)(color.Blue * 255);
            var a = (UInt16)(color.Alpha * 255);

            var rr = r.ToString("X2");
            var gg = g.ToString("X2");
            var bb = b.ToString("X2");
            var aa = a.ToString("X2");

            if (a < 255) return '#' + aa + rr + gg + bb;
            return '#' + rr + gg + bb;
        }
        //
        /**
         * ('#000000', 50) --> #808080
         * ('#EEEEEE', 25) --> #F2F2F2
         * ('EEE     , 25) --> #F2F2F2
         **/
        
        public static Color ToColorFromHex(this string color)
        
        {
            return Color.Parse(color);
        }

        
        public static string GetHexString(this Color color)
        
        {
            var red = (int)(color.Red * 255);
            var green = (int)(color.Green * 255);
            var blue = (int)(color.Blue * 255);
            var alpha = (int)(color.Alpha * 255);
            var hex = $"#{alpha:X2}{red:X2}{green:X2}{blue:X2}";

            return hex;
        }
        
        public static string GetHexDesc(this Color color)
        
        {
            var red = (int)(color.Red * 255);
            var green = (int)(color.Green * 255);
            var blue = (int)(color.Blue * 255);
            var alpha = (int)(color.Alpha * 255);
            var hex = $"#{red:X2}{green:X2}{blue:X2}";

            return hex;
        }

    }

}
