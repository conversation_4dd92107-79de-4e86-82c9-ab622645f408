﻿

namespace AppoMobi.Nifty
{
    public class CircleView : BoxView
    {

        public CircleView()
        {
            

        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameCircleSize:
                    WidthRequest = CircleSize;
                    HeightRequest = CircleSize;
                    //CornerRadius = CircleSize / 2.0;//RedrawList();//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;

                //                case nameLabelIcon:
                //                    ControlLabelIcon.Source = LabelIcon;
                //                    break;
            }

        }


        
        // 
        
        private const string nameCircleSize = "CircleSize";
        public static readonly BindableProperty CircleSizeProperty = BindableProperty.Create(nameCircleSize, typeof(int), typeof(CircleView), 14);
        public int CircleSize
        {
            get { return (int)GetValue(CircleSizeProperty); }
            set { SetValue(CircleSizeProperty, value); }
        }
        /*
        
        // 
        
        private const string nameCornerRadius = "CornerRadius";
        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(nameCornerRadius, typeof(int), typeof(CircleView), 7);
        public int CornerRadius
        {
            get { return (int)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }
        */
    }



}
