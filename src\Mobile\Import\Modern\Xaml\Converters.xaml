﻿<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary
    x:Class="AppoMobi.Forms.Framework.Xaml.Converters"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:xaml="clr-namespace:AppoMobi.Forms.Framework.Xaml"
    xmlns:xaml1="clr-namespace:AppoMobi.Forms.Framework.Xaml">



    <!--#region CONVERTERS-->

    <xaml1:ValueOverZeroConverter x:Key="ValueOverZeroConverter" />
    <xaml1:StringEmptyConverter x:Key="StringEmptyConverter" />
    <xaml1:StringIsEqual x:Key="StringIsEqual" />
    <xaml1:StringIsNotEqual x:Key="StringIsNotEqual" />
    <xaml1:StringNotEmptyConverter x:Key="StringNotEmptyConverter" />
    <xaml1:NotConverter x:Key="NotConverter" />
    <xaml1:IsNotNullConverter x:Key="IsNotNullConverter" />
    <xaml1:ZeroGridLengthIfStringEmptyConverter x:Key="ZeroGridLengthIfStringEmptyConverter" />
    <xaml1:CompareIntegersConverter x:Key="CompareIntegersConverter" />
    <xaml1:CompareIntegersInvertConverter x:Key="CompareIntegersInvertConverter" />

    <xaml1:ListOfStringsToTagsConverter x:Key="ListOfStringsToTagsConverter" />

    <xaml1:AddValueConverter x:Key="AddValueConverter" />
    <xaml1:AddValueIfBaseOverZeroConverter x:Key="AddValueIfBaseOverZeroConverter" />
    <xaml1:AddValueResultOverZeroConverter x:Key="AddValueResultOverZeroConverter" />

    <xaml1:DivideValueConverter x:Key="DivideValueConverter" />

    <xaml1:CleanTextConverter x:Key="CleanTextConverter" />

    <!--#endregion-->


</ResourceDictionary>