﻿using AppoMobi.Framework.Api;

namespace AppoMobi.Common.Dto.Required
{
    public class MainCategoryDto : BaseFrameworkDto
    {

        public string Title { get; set; }
        public string Desc { get; set; }
        public string ParentId { get; set; } //uid

        public int Priority { get; set; }

        public string ImageId { get; set; }
        public string ImageColor { get; set; }

        public bool HasSubCats { get; set; }
    }
}