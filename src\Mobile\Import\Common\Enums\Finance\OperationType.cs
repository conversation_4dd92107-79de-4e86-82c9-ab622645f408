﻿using System;
using System.ComponentModel;
using AppoMobi.Specials.Localization;
using AppoMobi.Forms.Common.ResX;

namespace AppoMobi.Common.Enums.Finance
{
    [Flags]
    [TypeConverter(typeof(LocalizedEnumConverter))]
    [FromResources(Type = typeof(ResStrings))]
    public enum      OperationType
    {
        Unset = 0,
        Deposit = 1,
        Withdrawal = 2,
        IncomingCall = 4,
        OutcomingCall = 8,
        //TestOriginal = 16,
        //TestBack = 32,
    }
}