﻿<?xml version="1.0" encoding="UTF-8" ?>
<Grid HorizontalOptions="FillAndExpand"
      VerticalOptions="FillAndExpand"
    x:Class="AppoMobi.Xam.TabButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    IsClippedToBounds="True">
    

    <Button
        x:Name="TabButtonView"
        Margin="-2,-3,-2,0"
        Clicked="TabButton_OnClicked" />


    <!--  Horizontal indicator for Android  -->

    <BoxView
        x:Name="HorizontalIndicator"
        HeightRequest="2"
        InputTransparent="True"
        IsVisible="False"
        VerticalOptions="End" />
    <!--  Vertical separator for iOS  -->

    <BoxView
        x:Name="VerticalSeparator"
        HorizontalOptions="Start"
        InputTransparent="True"
        IsVisible="False"
        VerticalOptions="FillAndExpand"
        WidthRequest="1" />

    <Label
    
        x:Name="TabLabelView"
        FontAttributes="Bold"
        HorizontalOptions="FillAndExpand"
        InputTransparent="True"
        Text="Tab Text"
        HorizontalTextAlignment="Center"
        VerticalTextAlignment="Center"
        VerticalOptions="FillAndExpand" />

</Grid>