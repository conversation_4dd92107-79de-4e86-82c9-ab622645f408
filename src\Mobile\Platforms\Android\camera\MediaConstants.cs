﻿using System;
using Android.Graphics;
using Point = Android.Graphics.Point;

namespace AppoMobi.Droid.Camera
{
    public class MediaConstants
    {

        /**
         * The name of the Intent-extra used to define the artist
         */
        public static String EXTRA_MEDIA_ARTIST = "android.intent.extra.artist";
        /**
         * The name of the Intent-extra used to define the album
         */
        public static String EXTRA_MEDIA_ALBUM = "android.intent.extra.album";
        /**
         * The name of the Intent-extra used to define the song title
         */
        public static String EXTRA_MEDIA_TITLE = "android.intent.extra.title";
        /**
         * The name of the Intent-extra used to define the genre.
         */
        public static String EXTRA_MEDIA_GENRE = "android.intent.extra.genre";
        /**
         * The name of the Intent-extra used to define the playlist.
         */
        public static String EXTRA_MEDIA_PLAYLIST = "android.intent.extra.playlist";
        /**
         * The name of the Intent-extra used to define the radio channel.
         */
        public static String EXTRA_MEDIA_RADIO_CHANNEL = "android.intent.extra.radio_channel";
        /**
         * The name of the Intent-extra used to define the search focus. The search focus
         * indicates whether the search should be for things related to the artist, album
         * or song that is identified by the other extras.
         */
        public static String EXTRA_MEDIA_FOCUS = "android.intent.extra.focus";

        /**
         * The name of the Intent-extra used to control the orientation of a ViewImage or a MovieView.
         * This is an int property that overrides the activity's requestedOrientation.
         * @see android.content.pm.ActivityInfo#SCREEN_ORIENTATION_UNSPECIFIED
         */
        public static String EXTRA_SCREEN_ORIENTATION = "android.intent.extra.screenOrientation";

        /**
         * The name of an Intent-extra used to control the UI of a ViewImage.
         * This is a boolean property that overrides the activity's default fullscreen state.
         */
        public static String EXTRA_FULL_SCREEN = "android.intent.extra.fullScreen";

        /**
         * The name of an Intent-extra used to control the UI of a ViewImage.
         * This is a boolean property that specifies whether or not to show action icons.
         */
        public static String EXTRA_SHOW_ACTION_ICONS = "android.intent.extra.showActionIcons";

        /**
         * The name of the Intent-extra used to control the onCompletion behavior of a MovieView.
         * This is a boolean property that specifies whether or not to finish the MovieView activity
         * when the movie completes playing. The default value is true, which means to automatically
         * exit the movie player activity when the movie completes playing.
         */
        public static String EXTRA_FINISH_ON_COMPLETION = "android.intent.extra.finishOnCompletion";

        /**
         * The name of the Intent action used to launch a camera in still image mode.
         */
    public static String INTENT_ACTION_STILL_IMAGE_CAMERA = "android.media.action.STILL_IMAGE_CAMERA";

        /**
         * Name under which an activity handling {@link #INTENT_ACTION_STILL_IMAGE_CAMERA} or
         * {@link #INTENT_ACTION_STILL_IMAGE_CAMERA_SECURE} publishes the service name for its prewarm
         * service.
         * <p>
         * This meta-data should reference the fully qualified class name of the prewarm service
         * extending {@link CameraPrewarmService}.
         * <p>
         * The prewarm service will get bound and receive a prewarm signal
         * {@link CameraPrewarmService#onPrewarm()} when a camera launch intent fire might be imminent.
         * An application implementing a prewarm service should do the absolute minimum amount of work
         * to initialize the camera in order to reduce startup time in likely case that shortly after a
         * camera launch intent would be sent.
         */
        public static String META_DATA_STILL_IMAGE_CAMERA_PREWARM_SERVICE =
                "android.media.still_image_camera_preview_service";

        /**
         * The name of the Intent action used to launch a camera in still image mode
         * for use when the device is secured (e.g. with a pin, password, pattern,
         * or face unlock). Applications responding to this intent must not expose
         * any personal content like existing photos or videos on the device. The
         * applications should be careful not to share any photo or video with other
         * applications or internet. The activity should use {@link
         * Activity#setShowWhenLocked} to display
         * on top of the lock screen while secured. There is no activity stack when
         * this flag is used, so launching more than one activity is strongly
         * discouraged.
         */
 
    public static String INTENT_ACTION_STILL_IMAGE_CAMERA_SECURE =
            "android.media.action.STILL_IMAGE_CAMERA_SECURE";

        /**
         * The name of the Intent action used to launch a camera in video mode.
         */
 
    public static String INTENT_ACTION_VIDEO_CAMERA = "android.media.action.VIDEO_CAMERA";

        /**
         * Standard Intent action that can be sent to have the camera application
         * capture an image and return it.
         * <p>
         * The caller may pass an extra EXTRA_OUTPUT to control where this image will be written.
         * If the EXTRA_OUTPUT is not present, then a small sized image is returned as a Bitmap
         * object in the extra field. This is useful for applications that only need a small image.
         * If the EXTRA_OUTPUT is present, then the full-sized image will be written to the Uri
         * value of EXTRA_OUTPUT.
         * As of {@link android.os.Build.VERSION_CODES#LOLLIPOP}, this uri can also be supplied through
         * {@link android.content.Intent#setClipData(ClipData)}. If using this approach, you still must
         * supply the uri through the EXTRA_OUTPUT field for compatibility with old applications.
         * If you don't set a ClipData, it will be copied there for you when calling
         * {@link Context#startActivity(Intent)}.
         *
         * <p>Note: if you app targets {@link android.os.Build.VERSION_CODES#M M} and above
         * and declares as using the {@link android.Manifest.permission#CAMERA} permission which
         * is not granted, then attempting to use this action will result in a {@link
         * java.lang.SecurityException}.
         *
         *  @see #EXTRA_OUTPUT
         */
 
    public static String ACTION_IMAGE_CAPTURE = "android.media.action.IMAGE_CAPTURE";

        /**
         * Intent action that can be sent to have the camera application capture an image and return
         * it when the device is secured (e.g. with a pin, password, pattern, or face unlock).
         * Applications responding to this intent must not expose any personal content like existing
         * photos or videos on the device. The applications should be careful not to share any photo
         * or video with other applications or Internet. The activity should use {@link
         * Activity#setShowWhenLocked} to display on top of the
         * lock screen while secured. There is no activity stack when this flag is used, so
         * launching more than one activity is strongly discouraged.
         * <p>
         * The caller may pass an extra EXTRA_OUTPUT to control where this image will be written.
         * If the EXTRA_OUTPUT is not present, then a small sized image is returned as a Bitmap
         * object in the extra field. This is useful for applications that only need a small image.
         * If the EXTRA_OUTPUT is present, then the full-sized image will be written to the Uri
         * value of EXTRA_OUTPUT.
         * As of {@link android.os.Build.VERSION_CODES#LOLLIPOP}, this uri can also be supplied through
         * {@link android.content.Intent#setClipData(ClipData)}. If using this approach, you still must
         * supply the uri through the EXTRA_OUTPUT field for compatibility with old applications.
         * If you don't set a ClipData, it will be copied there for you when calling
         * {@link Context#startActivity(Intent)}.
         *
         * @see #ACTION_IMAGE_CAPTURE
         * @see #EXTRA_OUTPUT
         */
 
    public static String ACTION_IMAGE_CAPTURE_SECURE =
            "android.media.action.IMAGE_CAPTURE_SECURE";

        /**
         * Standard Intent action that can be sent to have the camera application
         * capture a video and return it.
         * <p>
         * The caller may pass in an extra EXTRA_VIDEO_QUALITY to control the video quality.
         * <p>
         * The caller may pass in an extra EXTRA_OUTPUT to control
         * where the video is written. If EXTRA_OUTPUT is not present the video will be
         * written to the standard location for videos, and the Uri of that location will be
         * returned in the data field of the Uri.
         * As of {@link android.os.Build.VERSION_CODES#LOLLIPOP}, this uri can also be supplied through
         * {@link android.content.Intent#setClipData(ClipData)}. If using this approach, you still must
         * supply the uri through the EXTRA_OUTPUT field for compatibility with old applications.
         * If you don't set a ClipData, it will be copied there for you when calling
         * {@link Context#startActivity(Intent)}.
         *
         * <p>Note: if you app targets {@link android.os.Build.VERSION_CODES#M M} and above
         * and declares as using the {@link android.Manifest.permission#CAMERA} permission which
         * is not granted, then atempting to use this action will result in a {@link
         * java.lang.SecurityException}.
         *
         * @see #EXTRA_OUTPUT
         * @see #EXTRA_VIDEO_QUALITY
         * @see #EXTRA_SIZE_LIMIT
         * @see #EXTRA_DURATION_LIMIT
         */
 
    public static String ACTION_VIDEO_CAPTURE = "android.media.action.VIDEO_CAPTURE";

        /**
         * Standard action that can be sent to review the given media file.
         * <p>
         * The launched application is expected to provide a large-scale view of the
         * given media file, while allowing the user to quickly access other
         * recently captured media files.
         * <p>
         * Input: {@link Intent#getData} is URI of the primary media item to
         * initially display.
         *
         * @see #ACTION_REVIEW_SECURE
         * @see #EXTRA_BRIGHTNESS
         */
 
    public static String ACTION_REVIEW = "android.provider.action.REVIEW";

        /**
         * Standard action that can be sent to review the given media file when the
         * device is secured (e.g. with a pin, password, pattern, or face unlock).
         * The applications should be careful not to share any media with other
         * applications or Internet. The activity should use
         * {@link Activity#setShowWhenLocked} to display on top of the lock screen
         * while secured. There is no activity stack when this flag is used, so
         * launching more than one activity is strongly discouraged.
         * <p>
         * The launched application is expected to provide a large-scale view of the
         * given primary media file, while only allowing the user to quickly access
         * other media from an explicit secondary list.
         * <p>
         * Input: {@link Intent#getData} is URI of the primary media item to
         * initially display. {@link Intent#getClipData} is the limited list of
         * secondary media items that the user is allowed to review. If
         * {@link Intent#getClipData} is undefined, then no other media access
         * should be allowed.
         *
         * @see #EXTRA_BRIGHTNESS
         */
 
    public static String ACTION_REVIEW_SECURE = "android.provider.action.REVIEW_SECURE";

        /**
         * When defined, the launched application is requested to set the given
         * brightness value via
         * {@link android.view.WindowManager.LayoutParams#screenBrightness} to help
         * ensure a smooth transition when launching {@link #ACTION_REVIEW} or
         * {@link #ACTION_REVIEW_SECURE} intents.
         */
        public static String EXTRA_BRIGHTNESS = "android.provider.extra.BRIGHTNESS";

        /**
         * The name of the Intent-extra used to control the quality of a recorded video. This is an
         * integer property. Currently value 0 means low quality, suitable for MMS messages, and
         * value 1 means high quality. In the future other quality levels may be added.
         */
        public static String EXTRA_VIDEO_QUALITY = "android.intent.extra.videoQuality";

        /**
         * Specify the maximum allowed size.
         */
        public static String EXTRA_SIZE_LIMIT = "android.intent.extra.sizeLimit";

        /**
         * Specify the maximum allowed recording duration in seconds.
         */
        public static String EXTRA_DURATION_LIMIT = "android.intent.extra.durationLimit";

        /**
         * The name of the Intent-extra used to indicate a content resolver Uri to be used to
         * store the requested image or video.
         */
        public static String EXTRA_OUTPUT = "output";

        /**
          * The string that is used when a media attribute is not known. For example,
          * if an audio file does not have any meta data, the artist and album columns
          * will be set to this value.
          */
        public static String UNKNOWN_STRING = "<unknown>";

       
 
   
/**
 * Common media metadata columns.
 */
public class MediaColumns 
{
    /**
     * Absolute filesystem path to the media item on disk.
     * <p>
     * Note that apps may not have filesystem permissions to directly access
     * this path. Instead of trying to open this path directly, apps should
     * use {@link ContentResolver#openFileDescriptor(Uri, String)} to gain
     * access.
     *
     * @deprecated Apps may not have filesystem permissions to directly
     *             access this path. Instead of trying to open this path
     *             directly, apps should use
     *             {@link ContentResolver#openFileDescriptor(Uri, String)}
     *             to gain access.
     */
 
        public static String DATA = "_data";

/**
 * Hash of the media item on disk.
 * <p>
 * Contains a 20-byte binary blob which is the SHA-1 hash of the file as
 * persisted on disk. For performance reasons, the hash may not be
 * immediately available, in which case a {@code NULL} value will be
 * returned. If the underlying file is modified, this value will be
 * cleared and recalculated.
 * <p>
 * If you require the hash of a specific item, you can call
 * {@link ContentResolver#canonicalize(Uri)}, which will block until the
 * hash is calculated.
 *
 * @removed
 */
 
        public static String HASH = "_hash";

/**
 * The size of the media item.
 */
 
        public static String SIZE = "_size";

/**
 * The display name of the media item.
 * <p>
 * For example, an item stored at
 * {@code /storage/0000-0000/DCIM/Vacation/IMG1024.JPG} would have a
 * display name of {@code IMG1024.JPG}.
 */
 
        public static String DISPLAY_NAME = "_display_name";

/**
 * The title of the media item.
 */
 
        public static String TITLE = "title";

/**
 * The time the media item was first added.
 */
 
        public static String DATE_ADDED = "date_added";

/**
 * The time the media item was last modified.
 */
 
        public static String DATE_MODIFIED = "date_modified";

/**
 * The time the media item was taken.
 */
 
        public static String DATE_TAKEN = "datetaken";

/**
 * The MIME type of the media item.
 * <p>
 * This is typically defined based on the file extension of the media
 * item. However, it may be the value of the {@code format} attribute
 * defined by the <em>Dublin Core Media Initiative</em> standard,
 * extracted from any XMP metadata contained within this media item.
 * <p class="note">
 * Note: the {@code format} attribute may be ignored if the top-level
 * MIME type disagrees with the file extension. For example, it's
 * reasonable for an {@code image/jpeg} file to declare a {@code format}
 * of {@code image/vnd.google.panorama360+jpg}, but declaring a
 * {@code format} of {@code audio/ogg} would be ignored.
 * <p>
 * This is a read-only column that is automatically computed.
 */
 
        public static String MIME_TYPE = "mime_type";

/**
 * The MTP object handle of a newly transfered file.
 * Used to pass the new file's object handle through the media scanner
 * from MTP to the media provider
 * For internal use only by MTP, media scanner and media provider.
 * @hide
 */
 
        public static String MEDIA_SCANNER_NEW_OBJECT_ID = "media_scanner_new_object_id";

/**
 * Non-zero if the media file is drm-protected
 * @hide
 */
 
        public static String IS_DRM = "is_drm";

/**
 * Flag indicating if a media item is pending, and still being inserted
 * by its owner. While this flag is set, only the owner of the item can
 * open the underlying file; requests from other apps will be rejected.
 *
 * @see MediaStore#setIncludePending(Uri)
 */
 
        public static String IS_PENDING = "is_pending";

/**
 * Flag indicating if a media item is trashed.
 *
 * @see MediaColumns#IS_TRASHED
 * @see MediaStore#setIncludeTrashed(Uri)
 * @see MediaStore#trash(Context, Uri)
 * @see MediaStore#untrash(Context, Uri)
 * @removed
 */
 
        public static String IS_TRASHED = "is_trashed";

/**
 * The time the media item should be considered expired. Typically only
 * meaningful in the context of {@link #IS_PENDING}.
 */
 
        public static String DATE_EXPIRES = "date_expires";

/**
 * The width of the media item, in pixels.
 */
 
        public static String WIDTH = "width";

/**
 * The height of the media item, in pixels.
 */
 
        public static String HEIGHT = "height";

/**
 * Package name that contributed this media. The value may be
 * {@code NULL} if ownership cannot be reliably determined.
 */
 
        public static String OWNER_PACKAGE_NAME = "owner_package_name";

/**
 * Volume name of the specific storage device where this media item is
 * persisted. The value is typically one of the volume names returned
 * from {@link MediaStore#getExternalVolumeNames(Context)}.
 * <p>
 * This is a read-only column that is automatically computed.
 */
 
        public static String VOLUME_NAME = "volume_name";

/**
 * Relative path of this media item within the storage device where it
 * is persisted. For example, an item stored at
 * {@code /storage/0000-0000/DCIM/Vacation/IMG1024.JPG} would have a
 * path of {@code DCIM/Vacation/}.
 * <p>
 * This value should only be used for organizational purposes, and you
 * should not attempt to construct or access a raw filesystem path using
 * this value. If you need to open a media item, use an API like
 * {@link ContentResolver#openFileDescriptor(Uri, String)}.
 * <p>
 * When this value is set to {@code NULL} during an
 * {@link ContentResolver#insert} operation, the newly created item will
 * be placed in a relevant default location based on the type of media
 * being inserted. For example, a {@code image/jpeg} item will be placed
 * under {@link Environment#DIRECTORY_PICTURES}.
 * <p>
 * You can modify this column during an {@link ContentResolver#update}
 * call, which will move the underlying file on disk.
 * <p>
 * In both cases above, content must be placed under a top-level
 * directory that is relevant to the media type. For example, attempting
 * to place a {@code audio/mpeg} file under
 * {@link Environment#DIRECTORY_PICTURES} will be rejected.
 */
 
        public static String RELATIVE_PATH = "relative_path";

/**
 * The primary directory name this media exists under. The value may be
 * {@code NULL} if the media doesn't have a primary directory name.
 *
 * @removed
 * @deprecated Replaced by {@link #RELATIVE_PATH}.
 */
 
 
        public static String PRIMARY_DIRECTORY = "primary_directory";

/**
 * The secondary directory name this media exists under. The value may
 * be {@code NULL} if the media doesn't have a secondary directory name.
 *
 * @removed
 * @deprecated Replaced by {@link #RELATIVE_PATH}.
 */
 
       
        public static String SECONDARY_DIRECTORY = "secondary_directory";

/**
 * The primary bucket ID of this media item. This can be useful to
 * present the user a first-level clustering of related media items.
 * This is a read-only column that is automatically computed.
 */
 
        public static String BUCKET_ID = "bucket_id";

/**
 * The primary bucket display name of this media item. This can be
 * useful to present the user a first-level clustering of related
 * media items. This is a read-only column that is automatically
 * computed.
 */
 
        public static String BUCKET_DISPLAY_NAME = "bucket_display_name";

/**
 * The group ID of this media item. This can be useful to present
 * the user a grouping of related media items, such a burst of
 * images, or a {@code JPG} and {@code DNG} version of the same
 * image.
 * <p>
 * This is a read-only column that is automatically computed based
 * on the first portion of the filename. For example,
 * {@code IMG1024.BURST001.JPG} and {@code IMG1024.BURST002.JPG}
 * will have the same {@link #GROUP_ID} because the first portion of
 * their filenames is identical.
 *
 * @removed
 */
 
       
        public static String GROUP_ID = "group_id";

/**
 * The "document ID" GUID as defined by the <em>XMP Media
 * Management</em> standard, extracted from any XMP metadata contained
 * within this media item. The value is {@code null} when no metadata
 * was found.
 * <p>
 * Each "document ID" is created once for each new resource. Different
 * renditions of that resource are expected to have different IDs.
 */
 
        public static String DOCUMENT_ID = "document_id";

/**
 * The "instance ID" GUID as defined by the <em>XMP Media
 * Management</em> standard, extracted from any XMP metadata contained
 * within this media item. The value is {@code null} when no metadata
 * was found.
 * <p>
 * This "instance ID" changes with each save operation of a specific
 * "document ID".
 */
 
        public static String INSTANCE_ID = "instance_id";

/**
 * The "original document ID" GUID as defined by the <em>XMP Media
 * Management</em> standard, extracted from any XMP metadata contained
 * within this media item.
 * <p>
 * This "original document ID" links a resource to its original source.
 * For example, when you save a PSD document as a JPEG, then convert the
 * JPEG to GIF format, the "original document ID" of both the JPEG and
 * GIF files is the "document ID" of the original PSD file.
 */
 
        public static String ORIGINAL_DOCUMENT_ID = "original_document_id";

/**
 * The duration of the media item.
 */
 
 
        public static String DURATION = "duration";

/**
 * The orientation for the media item, expressed in degrees. For
 * example, 0, 90, 180, or 270 degrees.
 */
 
        public static String ORIENTATION = "orientation";
    }

 

    /** @hide */
    public static class ThumbnailConstants
{
    public static int MINI_KIND = 1;
    public static int FULL_SCREEN_KIND = 2;
    public static int MICRO_KIND = 3;

    public static Point MINI_SIZE = new Point(512, 384);
    public static Point FULL_SCREEN_SIZE = new Point(1024, 786);
    public static Point MICRO_SIZE = new Point(96, 96);
}

/**
 * Download metadata columns.
 */
public class DownloadColumns
{
    /**
     * Uri indicating where the item has been downloaded from.
     */
     
        String DOWNLOAD_URI = "download_uri";

    /**
     * Uri indicating HTTP referer of {@link #DOWNLOAD_URI}.
     */
     
        String REFERER_URI = "referer_uri";

    /**
     * The description of the download.
     *
     * @removed
     */
   
     
        String DESCRIPTION = "description";
}

 
  

    /**
     * Collection of all media with MIME type of {@code image/*}.
     */
    public static class Images
{
    /**
     * Image metadata columns.
     */
    public interface ImageColumns extends MediaColumns
    {
        /**
         * The description of the image
         */
         
            public static String DESCRIPTION = "description";

    /**
     * The picasa id of the image
     *
     * @deprecated this value was only relevant for images hosted on
     *             Picasa, which are no longer supported.
     */
   
     
            public static String PICASA_ID = "picasa_id";

    /**
     * Whether the video should be published as public or private
     */
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String IS_PRIVATE = "isprivate";

    /**
     * The latitude where the image was captured.
     *
     * @deprecated location details are no longer indexed for privacy
     *             reasons, and this value is now always {@code null}.
     *             You can still manually obtain location metadata using
     *             {@link ExifInterface#getLatLong(float[])}.
     */
   
    @Column(value = Cursor.FIELD_TYPE_FLOAT, readOnly = true)
            public static String LATITUDE = "latitude";

    /**
     * The longitude where the image was captured.
     *
     * @deprecated location details are no longer indexed for privacy
     *             reasons, and this value is now always {@code null}.
     *             You can still manually obtain location metadata using
     *             {@link ExifInterface#getLatLong(float[])}.
     */
   
    @Column(value = Cursor.FIELD_TYPE_FLOAT, readOnly = true)
            public static String LONGITUDE = "longitude";

    /** @removed promoted to parent interface */
    public static String DATE_TAKEN = "datetaken";
    /** @removed promoted to parent interface */
    public static String ORIENTATION = "orientation";

    /**
     * The mini thumb id.
     *
     * @deprecated all thumbnails should be obtained via
     *             {@link MediaStore.Images.Thumbnails#getThumbnail}, as this
     *             value is no longer supported.
     */
   
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String MINI_THUMB_MAGIC = "mini_thumb_magic";

    /** @removed promoted to parent interface */
    public static String BUCKET_ID = "bucket_id";
    /** @removed promoted to parent interface */
    public static String BUCKET_DISPLAY_NAME = "bucket_display_name";
    /** @removed promoted to parent interface */
    public static String GROUP_ID = "group_id";
}

public static class Media 
{
   
 
/**
 * The MIME type of of this directory of
 * images.  Note that each entry in this directory will have a standard
 * image MIME type as appropriate -- for example, image/jpeg.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/image";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = ImageColumns.BUCKET_DISPLAY_NAME;
        }

        /**
         * This class provides utility methods to obtain thumbnails for various
         * {@link Images} items.
         *
         * @deprecated Callers should migrate to using
         *             {@link ContentResolver#loadThumbnail}, since it offers
         *             richer control over requested thumbnail sizes and
         *             cancellation behavior.
         */
       
        public static class Thumbnails implements BaseColumns
{
    /**
     * @deprecated all queries should be performed through
     *             {@link ContentResolver} directly, which offers modern
     *             features like {@link CancellationSignal}.
     */
   
            public static Cursor query(ContentResolver cr, Uri uri, String[] projection)
{
    return cr.query(uri, projection, null, null, DEFAULT_SORT_ORDER);
}

/**
 * @deprecated all queries should be performed through
 *             {@link ContentResolver} directly, which offers modern
 *             features like {@link CancellationSignal}.
 */
@Deprecated
            public static Cursor queryMiniThumbnails(ContentResolver cr, Uri uri, int kind,
                    String[] projection)
{
    return cr.query(uri, projection, "kind = " + kind, null, DEFAULT_SORT_ORDER);
}

/**
 * @deprecated all queries should be performed through
 *             {@link ContentResolver} directly, which offers modern
 *             features like {@link CancellationSignal}.
 */
@Deprecated
            public static Cursor queryMiniThumbnail(ContentResolver cr, long origId, int kind,
                    String[] projection)
{
    return cr.query(EXTERNAL_CONTENT_URI, projection,
            IMAGE_ID + " = " + origId + " AND " + KIND + " = " +
            kind, null, null);
}

/**
 * Cancel any outstanding {@link #getThumbnail} requests, causing
 * them to return by throwing a {@link OperationCanceledException}.
 * <p>
 * This method has no effect on
 * {@link ContentResolver#loadThumbnail} calls, since they provide
 * their own {@link CancellationSignal}.
 *
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static void cancelThumbnailRequest(ContentResolver cr, long origId)
{
    Uri uri = ContentUris.withAppendedId(
          Images.Media.EXTERNAL_CONTENT_URI, origId);
    InternalThumbnails.cancelThumbnail(cr, uri);
}

/**
 * Return thumbnail representing a specific image item. If a
 * thumbnail doesn't exist, this method will block until it's
 * generated. Callers are responsible for their own in-memory
 * caching of returned values.
 *
 * @param imageId the image item to obtain a thumbnail for.
 * @param kind optimal thumbnail size desired.
 * @return decoded thumbnail, or {@code null} if problem was
 *         encountered.
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static Bitmap getThumbnail(ContentResolver cr, long imageId, int kind,
                    BitmapFactory.Options options)
{
    Uri uri = ContentUris.withAppendedId(
          Images.Media.EXTERNAL_CONTENT_URI, imageId);
    return InternalThumbnails.getThumbnail(cr, uri, kind, options);
}

/**
 * Cancel any outstanding {@link #getThumbnail} requests, causing
 * them to return by throwing a {@link OperationCanceledException}.
 * <p>
 * This method has no effect on
 * {@link ContentResolver#loadThumbnail} calls, since they provide
 * their own {@link CancellationSignal}.
 *
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static void cancelThumbnailRequest(ContentResolver cr, long origId,
                    long groupId)
{
    cancelThumbnailRequest(cr, origId);
}

/**
 * Return thumbnail representing a specific image item. If a
 * thumbnail doesn't exist, this method will block until it's
 * generated. Callers are responsible for their own in-memory
 * caching of returned values.
 *
 * @param imageId the image item to obtain a thumbnail for.
 * @param kind optimal thumbnail size desired.
 * @return decoded thumbnail, or {@code null} if problem was
 *         encountered.
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static Bitmap getThumbnail(ContentResolver cr, long imageId, long groupId,
                    int kind, BitmapFactory.Options options)
{
    return getThumbnail(cr, imageId, kind, options);
}

/**
 * Get the content:// style URI for the image media table on the
 * given volume.
 *
 * @param volumeName the name of the volume to get the URI for
 * @return the URI to the image media table on the given volume
 */
public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("images")
            .appendPath("thumbnails").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = "image_id ASC";

/**
 * Path to the thumbnail file on disk.
 * <p>
 * Note that apps may not have filesystem permissions to directly
 * access this path. Instead of trying to open this path directly,
 * apps should use
 * {@link ContentResolver#openFileDescriptor(Uri, String)} to gain
 * access.
 *
 * @deprecated Apps may not have filesystem permissions to directly
 *             access this path. Instead of trying to open this path
 *             directly, apps should use
 *             {@link ContentResolver#loadThumbnail}
 *             to gain access.
 */
@Deprecated
 
            public static String DATA = "_data";

/**
 * The original image for the thumbnal
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
            public static String IMAGE_ID = "image_id";

/**
 * The kind of the thumbnail
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
            public static String KIND = "kind";

public static int MINI_KIND = ThumbnailConstants.MINI_KIND;
public static int FULL_SCREEN_KIND = ThumbnailConstants.FULL_SCREEN_KIND;
public static int MICRO_KIND = ThumbnailConstants.MICRO_KIND;

/**
 * The blob raw data of thumbnail
 *
 * @deprecated this column never existed internally, and could never
 *             have returned valid data.
 */
@Deprecated
@Column(Cursor.FIELD_TYPE_BLOB)
            public static String THUMB_DATA = "thumb_data";

/**
 * The width of the thumbnal
 */
 
            public static String WIDTH = "width";

/**
 * The height of the thumbnail
 */
 
            public static String HEIGHT = "height";
        }
    }

    /**
     * Collection of all media with MIME type of {@code audio/*}.
     */
    public static class Audio
{
    /**
     * Audio metadata columns.
     */
    public interface AudioColumns extends MediaColumns
    {

        /**
         * A non human readable key calculated from the TITLE, used for
         * searching, sorting and grouping
         */
         
            public static String TITLE_KEY = "title_key";

    /** @removed promoted to parent interface */
    public static String DURATION = "duration";

    /**
     * The position within the audio item at which playback should be
     * resumed.
     */
    @DurationMillisLong
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String BOOKMARK = "bookmark";

    /**
     * The id of the artist who created the audio file, if any
     */
     
            public static String ARTIST_ID = "artist_id";

    /**
     * The artist who created the audio file, if any
     */
     
            public static String ARTIST = "artist";

    /**
     * The artist credited for the album that contains the audio file
     * @hide
     */
     
            public static String ALBUM_ARTIST = "album_artist";

    /**
     * Whether the song is part of a compilation
     * @hide
     */
   
            //  
            public static String COMPILATION = "compilation";

    /**
     * A non human readable key calculated from the ARTIST, used for
     * searching, sorting and grouping
     */
     
            public static String ARTIST_KEY = "artist_key";

    /**
     * The composer of the audio file, if any
     */
     
            public static String COMPOSER = "composer";

    /**
     * The id of the album the audio file is from, if any
     */
     
            public static String ALBUM_ID = "album_id";

    /**
     * The album the audio file is from, if any
     */
     
            public static String ALBUM = "album";

    /**
     * A non human readable key calculated from the ALBUM, used for
     * searching, sorting and grouping
     */
     
            public static String ALBUM_KEY = "album_key";

    /**
     * The track number of this song on the album, if any.
     * This number encodes both the track number and the
     * disc number. For multi-disc sets, this number will
     * be 1xxx for tracks on the first disc, 2xxx for tracks
     * on the second disc, etc.
     */
     
            public static String TRACK = "track";

    /**
     * The year the audio file was recorded, if any
     */
     
            public static String YEAR = "year";

    /**
     * Non-zero if the audio file is music
     */
     
            public static String IS_MUSIC = "is_music";

    /**
     * Non-zero if the audio file is a podcast
     */
     
            public static String IS_PODCAST = "is_podcast";

    /**
     * Non-zero if the audio file may be a ringtone
     */
     
            public static String IS_RINGTONE = "is_ringtone";

    /**
     * Non-zero if the audio file may be an alarm
     */
     
            public static String IS_ALARM = "is_alarm";

    /**
     * Non-zero if the audio file may be a notification sound
     */
     
            public static String IS_NOTIFICATION = "is_notification";

    /**
     * Non-zero if the audio file is an audiobook
     */
     
            public static String IS_AUDIOBOOK = "is_audiobook";

    /**
     * The genre of the audio file, if any
     * Does not exist in the database - only used by the media scanner for inserts.
     * @hide
     */
   
            //  
            public static String GENRE = "genre";

    /**
     * The resource URI of a localized title, if any
     * Conforms to this pattern:
     *   Scheme: {@link ContentResolver.SCHEME_ANDROID_RESOURCE}
     *   Authority: Package Name of ringtone title provider
     *   First Path Segment: Type of resource (must be "string")
     *   Second Path Segment: Resource ID of title
     * @hide
     */
     
            public static String TITLE_RESOURCE_URI = "title_resource_uri";
}

/**
 * Converts a name to a "key" that can be used for grouping, sorting
 * and searching.
 * The rules that govern this conversion are:
 * - remove 'special' characters like ()[]'!?.,
 * - remove leading/trailing spaces
 * - convert everything to lowercase
 * - remove leading "the ", "an " and "a "
 * - remove trailing ", the|an|a"
 * - remove accents. This step leaves us with CollationKey data,
 *   which is not human readable
 *
 * @param name The artist or album name to convert
 * @return The "key" for the given name.
 */
public static String keyFor(String name)
{
    if (name != null)
    {
        boolean sortfirst = false;
        if (name.equals(UNKNOWN_STRING))
        {
            return "\001";
        }
        // Check if the first character is \001. We use this to
        // force sorting of certain special files, like the silent ringtone.
        if (name.startsWith("\001"))
        {
            sortfirst = true;
        }
        name = name.trim().toLowerCase();
        if (name.startsWith("the "))
        {
            name = name.substring(4);
        }
        if (name.startsWith("an "))
        {
            name = name.substring(3);
        }
        if (name.startsWith("a "))
        {
            name = name.substring(2);
        }
        if (name.endsWith(", the") || name.endsWith(",the") ||
            name.endsWith(", an") || name.endsWith(",an") ||
            name.endsWith(", a") || name.endsWith(",a"))
        {
            name = name.substring(0, name.lastIndexOf(','));
        }
        name = name.replaceAll("[\\[\\]\\(\\)\"'.,?!]", "").trim();
        if (name.length() > 0)
        {
            // Insert a separator between the characters to avoid
            // matches on a partial character. If we ever change
            // to start-of-word-only matches, this can be removed.
            StringBuilder b = new StringBuilder();
            b.append('.');
            int nl = name.length();
            for (int i = 0; i < nl; i++)
            {
                b.append(name.charAt(i));
                b.append('.');
            }
            name = b.toString();
            String key = DatabaseUtils.getCollationKey(name);
            if (sortfirst)
            {
                key = "\001" + key;
            }
            return key;
        }
        else
        {
            return "";
        }
    }
    return null;
}

public static class Media implements AudioColumns
{
            /**
             * Get the content:// style URI for the audio media table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the audio media table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("audio")
            .appendPath("media").build();
}

/** @hide */
public static @NonNull Uri getContentUri(@NonNull String volumeName, long id) {
    return ContentUris.withAppendedId(getContentUri(volumeName), id);
}

/**
 * Get the content:// style URI for the given audio media file.
 *
 * @deprecated Apps may not have filesystem permissions to directly
 *             access this path.
 */
@Deprecated
            public static @Nullable Uri getContentUriForPath(@NonNull String path) {
    return getContentUri(getVolumeName(new File(path)));
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/audio";

/**
 * The MIME type for an audio track.
 */
public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/audio";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = TITLE_KEY;

/**
 * Activity Action: Start SoundRecorder application.
 * <p>Input: nothing.
 * <p>Output: An uri to the recorded sound stored in the Media Library
 * if the recording was successful.
 * May also contain the extra EXTRA_MAX_BYTES.
 * @see #EXTRA_MAX_BYTES
 */
@SdkConstant(SdkConstantType.ACTIVITY_INTENT_ACTION)
            public static String RECORD_SOUND_ACTION =
                    "android.provider.MediaStore.RECORD_SOUND";

/**
 * The name of the Intent-extra used to define a maximum file size for
 * a recording made by the SoundRecorder application.
 *
 * @see #RECORD_SOUND_ACTION
 */
public static String EXTRA_MAX_BYTES =
       "android.provider.MediaStore.extra.MAX_BYTES";
        }

        /**
         * Audio genre metadata columns.
         */
        public interface GenresColumns
{
    /**
     * The name of the genre
     */
     
            public static String NAME = "name";
}

/**
 * Contains all genres for audio files
 */
public static class Genres implements BaseColumns, GenresColumns {
            /**
             * Get the content:// style URI for the audio genres table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the audio genres table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("audio")
            .appendPath("genres").build();
}

/**
 * Get the content:// style URI for querying the genres of an audio file.
 *
 * @param volumeName the name of the volume to get the URI for
 * @param audioId the ID of the audio file for which to retrieve the genres
 * @return the URI to for querying the genres for the audio file
 * with the given the volume and audioID
 */
public static Uri getContentUriForAudioId(String volumeName, int audioId)
{
    return ContentUris.withAppendedId(Audio.Media.getContentUri(volumeName), audioId)
            .buildUpon().appendPath("genres").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/genre";

/**
 * The MIME type for entries in this table.
 */
public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/genre";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = NAME;

/**
 * Sub-directory of each genre containing all members.
 */
public static class Members implements AudioColumns
{

                public static Uri getContentUri(String volumeName, long genreId)
{
    return ContentUris
            .withAppendedId(Audio.Genres.getContentUri(volumeName), genreId)
            .buildUpon().appendPath("members").build();
}

/**
 * A subdirectory of each genre containing all member audio files.
 */
public static String CONTENT_DIRECTORY = "members";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = TITLE_KEY;

/**
 * The ID of the audio file
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String AUDIO_ID = "audio_id";

/**
 * The ID of the genre
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String GENRE_ID = "genre_id";
            }
        }

        /**
         * Audio playlist metadata columns.
         */
        public interface PlaylistsColumns
{
    /**
     * The name of the playlist
     */
     
            public static String NAME = "name";

    /**
     * Path to the playlist file on disk.
     * <p>
     * Note that apps may not have filesystem permissions to directly
     * access this path. Instead of trying to open this path directly,
     * apps should use
     * {@link ContentResolver#openFileDescriptor(Uri, String)} to gain
     * access.
     *
     * @deprecated Apps may not have filesystem permissions to directly
     *             access this path. Instead of trying to open this path
     *             directly, apps should use
     *             {@link ContentResolver#openFileDescriptor(Uri, String)}
     *             to gain access.
     */
   
     
            public static String DATA = "_data";

    /**
     * The time the media item was first added.
     */
    @CurrentTimeSecondsLong
     
            public static String DATE_ADDED = "date_added";

    /**
     * The time the media item was last modified.
     */
    @CurrentTimeSecondsLong
     
            public static String DATE_MODIFIED = "date_modified";
}

/**
 * Contains playlists for audio files
 */
public static class Playlists implements BaseColumns,
      PlaylistsColumns {
            /**
             * Get the content:// style URI for the audio playlists table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the audio playlists table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("audio")
            .appendPath("playlists").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/playlist";

/**
 * The MIME type for entries in this table.
 */
public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/playlist";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = NAME;

/**
 * Sub-directory of each playlist containing all members.
 */
public static class Members implements AudioColumns
{
                public static Uri getContentUri(String volumeName, long playlistId)
{
    return ContentUris
            .withAppendedId(Audio.Playlists.getContentUri(volumeName), playlistId)
            .buildUpon().appendPath("members").build();
}

/**
 * Convenience method to move a playlist item to a new location
 * @param res The content resolver to use
 * @param playlistId The numeric id of the playlist
 * @param from The position of the item to move
 * @param to The position to move the item to
 * @return true on success
 */
public static boolean moveItem(ContentResolver res,
        long playlistId, int from, int to)
{
    Uri uri = MediaStore.Audio.Playlists.Members.getContentUri("external",
            playlistId)
            .buildUpon()
            .appendEncodedPath(String.valueOf(from))
            .appendQueryParameter("move", "true")
            .build();
    ContentValues values = new ContentValues();
    values.put(MediaStore.Audio.Playlists.Members.PLAY_ORDER, to);
    return res.update(uri, values, null, null) != 0;
}

/**
 * The ID within the playlist.
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String _ID = "_id";

/**
 * A subdirectory of each playlist containing all member audio
 * files.
 */
public static String CONTENT_DIRECTORY = "members";

/**
 * The ID of the audio file
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String AUDIO_ID = "audio_id";

/**
 * The ID of the playlist
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String PLAYLIST_ID = "playlist_id";

/**
 * The order of the songs in the playlist
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
                public static String PLAY_ORDER = "play_order";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = PLAY_ORDER;
            }
        }

        /**
         * Audio artist metadata columns.
         */
        public interface ArtistColumns
{
    /**
     * The artist who created the audio file, if any
     */
     
            public static String ARTIST = "artist";

    /**
     * A non human readable key calculated from the ARTIST, used for
     * searching, sorting and grouping
     */
     
            public static String ARTIST_KEY = "artist_key";

    /**
     * The number of albums in the database for this artist
     */
     
            public static String NUMBER_OF_ALBUMS = "number_of_albums";

    /**
     * The number of albums in the database for this artist
     */
     
            public static String NUMBER_OF_TRACKS = "number_of_tracks";
}

/**
 * Contains artists for audio files
 */
public static class Artists implements BaseColumns, ArtistColumns {
            /**
             * Get the content:// style URI for the artists table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the audio artists table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("audio")
            .appendPath("artists").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/artists";

/**
 * The MIME type for entries in this table.
 */
public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/artist";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = ARTIST_KEY;

/**
 * Sub-directory of each artist containing all albums on which
 * a song by the artist appears.
 */
public static class Albums implements AlbumColumns
{
                public static Uri getContentUri(String volumeName, long artistId)
{
    return ContentUris
            .withAppendedId(Audio.Artists.getContentUri(volumeName), artistId)
            .buildUpon().appendPath("albums").build();
}
            }
        }

        /**
         * Audio album metadata columns.
         */
        public interface AlbumColumns
{

    /**
     * The id for the album
     */
     
            public static String ALBUM_ID = "album_id";

    /**
     * The album on which the audio file appears, if any
     */
     
            public static String ALBUM = "album";

    /**
     * The ID of the artist whose songs appear on this album.
     */
     
            public static String ARTIST_ID = "artist_id";

    /**
     * The name of the artist whose songs appear on this album.
     */
     
            public static String ARTIST = "artist";

    /**
     * The number of songs on this album
     */
     
            public static String NUMBER_OF_SONGS = "numsongs";

    /**
     * This column is available when getting album info via artist,
     * and indicates the number of songs on the album by the given
     * artist.
     */
     
            public static String NUMBER_OF_SONGS_FOR_ARTIST = "numsongs_by_artist";

    /**
     * The year in which the earliest songs
     * on this album were released. This will often
     * be the same as {@link #LAST_YEAR}, but for compilation albums
     * they might differ.
     */
     
            public static String FIRST_YEAR = "minyear";

    /**
     * The year in which the latest songs
     * on this album were released. This will often
     * be the same as {@link #FIRST_YEAR}, but for compilation albums
     * they might differ.
     */
     
            public static String LAST_YEAR = "maxyear";

    /**
     * A non human readable key calculated from the ALBUM, used for
     * searching, sorting and grouping
     */
     
            public static String ALBUM_KEY = "album_key";

    /**
     * Cached album art.
     *
     * @deprecated Apps may not have filesystem permissions to directly
     *             access this path. Instead of trying to open this path
     *             directly, apps should use
     *             {@link ContentResolver#loadThumbnail}
     *             to gain access.
     */
   
     
            public static String ALBUM_ART = "album_art";
}

/**
 * Contains artists for audio files
 */
public static class Albums implements BaseColumns, AlbumColumns {
            /**
             * Get the content:// style URI for the albums table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the audio albums table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("audio")
            .appendPath("albums").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/albums";

/**
 * The MIME type for entries in this table.
 */
public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/album";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = ALBUM_KEY;
        }

        public static class Radio
{
    /**
     * The MIME type for entries in this table.
     */
    public static String ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/radio";

    // Not instantiable.
    private Radio() { }
}

/**
 * This class provides utility methods to obtain thumbnails for various
 * {@link Audio} items.
 *
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it offers
 *             richer control over requested thumbnail sizes and
 *             cancellation behavior.
 * @hide
 */
@Deprecated
        public static class Thumbnails implements BaseColumns
{
    /**
     * Path to the thumbnail file on disk.
     * <p>
     * Note that apps may not have filesystem permissions to directly
     * access this path. Instead of trying to open this path directly,
     * apps should use
     * {@link ContentResolver#openFileDescriptor(Uri, String)} to gain
     * access.
     *
     * @deprecated Apps may not have filesystem permissions to directly
     *             access this path. Instead of trying to open this path
     *             directly, apps should use
     *             {@link ContentResolver#loadThumbnail}
     *             to gain access.
     */
   
             
            public static String DATA = "_data";

@Column(Cursor.FIELD_TYPE_INTEGER)
            public static String ALBUM_ID = "album_id";
        }
    }

    /**
     * Collection of all media with MIME type of {@code video/*}.
     */
    public static class Video
{

    /**
     * The default sort order for this table.
     */
    public static String DEFAULT_SORT_ORDER = MediaColumns.DISPLAY_NAME;

    /**
     * @deprecated all queries should be performed through
     *             {@link ContentResolver} directly, which offers modern
     *             features like {@link CancellationSignal}.
     */
   
        public static Cursor query(ContentResolver cr, Uri uri, String[] projection)
    {
        return cr.query(uri, projection, null, null, DEFAULT_SORT_ORDER);
    }

    /**
     * Video metadata columns.
     */
    public interface VideoColumns extends MediaColumns
    {
            /** @removed promoted to parent interface */
            public static String DURATION = "duration";

    /**
     * The artist who created the video file, if any
     */
     
            public static String ARTIST = "artist";

    /**
     * The album the video file is from, if any
     */
     
            public static String ALBUM = "album";

    /**
     * The resolution of the video file, formatted as "XxY"
     */
     
            public static String RESOLUTION = "resolution";

    /**
     * The description of the video recording
     */
     
            public static String DESCRIPTION = "description";

    /**
     * Whether the video should be published as public or private
     */
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String IS_PRIVATE = "isprivate";

    /**
     * The user-added tags associated with a video
     */
     
            public static String TAGS = "tags";

    /**
     * The YouTube category of the video
     */
     
            public static String CATEGORY = "category";

    /**
     * The language of the video
     */
     
            public static String LANGUAGE = "language";

    /**
     * The latitude where the video was captured.
     *
     * @deprecated location details are no longer indexed for privacy
     *             reasons, and this value is now always {@code null}.
     *             You can still manually obtain location metadata using
     *             {@link ExifInterface#getLatLong(float[])}.
     */
   
    @Column(value = Cursor.FIELD_TYPE_FLOAT, readOnly = true)
            public static String LATITUDE = "latitude";

    /**
     * The longitude where the video was captured.
     *
     * @deprecated location details are no longer indexed for privacy
     *             reasons, and this value is now always {@code null}.
     *             You can still manually obtain location metadata using
     *             {@link ExifInterface#getLatLong(float[])}.
     */
   
    @Column(value = Cursor.FIELD_TYPE_FLOAT, readOnly = true)
            public static String LONGITUDE = "longitude";

    /** @removed promoted to parent interface */
    public static String DATE_TAKEN = "datetaken";

    /**
     * The mini thumb id.
     *
     * @deprecated all thumbnails should be obtained via
     *             {@link MediaStore.Images.Thumbnails#getThumbnail}, as this
     *             value is no longer supported.
     */
   
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String MINI_THUMB_MAGIC = "mini_thumb_magic";

    /** @removed promoted to parent interface */
    public static String BUCKET_ID = "bucket_id";
    /** @removed promoted to parent interface */
    public static String BUCKET_DISPLAY_NAME = "bucket_display_name";
    /** @removed promoted to parent interface */
    public static String GROUP_ID = "group_id";

    /**
     * The position within the video item at which playback should be
     * resumed.
     */
    @DurationMillisLong
    @Column(Cursor.FIELD_TYPE_INTEGER)
            public static String BOOKMARK = "bookmark";

    /**
     * The standard of color aspects
     * @hide
     */
     
            public static String COLOR_STANDARD = "color_standard";

    /**
     * The transfer of color aspects
     * @hide
     */
     
            public static String COLOR_TRANSFER = "color_transfer";

    /**
     * The range of color aspects
     * @hide
     */
     
            public static String COLOR_RANGE = "color_range";
}

public static class Media implements VideoColumns
{
            /**
             * Get the content:// style URI for the video media table on the
             * given volume.
             *
             * @param volumeName the name of the volume to get the URI for
             * @return the URI to the video media table on the given volume
             */
            public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("video")
            .appendPath("media").build();
}

/** @hide */
public static @NonNull Uri getContentUri(@NonNull String volumeName, long id) {
    return ContentUris.withAppendedId(getContentUri(volumeName), id);
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The MIME type for this table.
 */
public static String CONTENT_TYPE = "vnd.android.cursor.dir/video";

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = TITLE;
        }

        /**
         * This class provides utility methods to obtain thumbnails for various
         * {@link Video} items.
         *
         * @deprecated Callers should migrate to using
         *             {@link ContentResolver#loadThumbnail}, since it offers
         *             richer control over requested thumbnail sizes and
         *             cancellation behavior.
         */
       
        public static class Thumbnails implements BaseColumns
{
    /**
     * Cancel any outstanding {@link #getThumbnail} requests, causing
     * them to return by throwing a {@link OperationCanceledException}.
     * <p>
     * This method has no effect on
     * {@link ContentResolver#loadThumbnail} calls, since they provide
     * their own {@link CancellationSignal}.
     *
     * @deprecated Callers should migrate to using
     *             {@link ContentResolver#loadThumbnail}, since it
     *             offers richer control over requested thumbnail sizes
     *             and cancellation behavior.
     */
   
            public static void cancelThumbnailRequest(ContentResolver cr, long origId)
{
    Uri uri = ContentUris.withAppendedId(
          Video.Media.EXTERNAL_CONTENT_URI, origId);
    InternalThumbnails.cancelThumbnail(cr, uri);
}

/**
 * Return thumbnail representing a specific video item. If a
 * thumbnail doesn't exist, this method will block until it's
 * generated. Callers are responsible for their own in-memory
 * caching of returned values.
 *
 * @param videoId the video item to obtain a thumbnail for.
 * @param kind optimal thumbnail size desired.
 * @return decoded thumbnail, or {@code null} if problem was
 *         encountered.
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static Bitmap getThumbnail(ContentResolver cr, long videoId, int kind,
                    BitmapFactory.Options options)
{
    Uri uri = ContentUris.withAppendedId(
          Video.Media.EXTERNAL_CONTENT_URI, videoId);
    return InternalThumbnails.getThumbnail(cr, uri, kind, options);
}

/**
 * Cancel any outstanding {@link #getThumbnail} requests, causing
 * them to return by throwing a {@link OperationCanceledException}.
 * <p>
 * This method has no effect on
 * {@link ContentResolver#loadThumbnail} calls, since they provide
 * their own {@link CancellationSignal}.
 *
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static void cancelThumbnailRequest(ContentResolver cr, long videoId,
                    long groupId)
{
    cancelThumbnailRequest(cr, videoId);
}

/**
 * Return thumbnail representing a specific video item. If a
 * thumbnail doesn't exist, this method will block until it's
 * generated. Callers are responsible for their own in-memory
 * caching of returned values.
 *
 * @param videoId the video item to obtain a thumbnail for.
 * @param kind optimal thumbnail size desired.
 * @return decoded thumbnail, or {@code null} if problem was
 *         encountered.
 * @deprecated Callers should migrate to using
 *             {@link ContentResolver#loadThumbnail}, since it
 *             offers richer control over requested thumbnail sizes
 *             and cancellation behavior.
 */
@Deprecated
            public static Bitmap getThumbnail(ContentResolver cr, long videoId, long groupId,
                    int kind, BitmapFactory.Options options)
{
    return getThumbnail(cr, videoId, kind, options);
}

/**
 * Get the content:// style URI for the image media table on the
 * given volume.
 *
 * @param volumeName the name of the volume to get the URI for
 * @return the URI to the image media table on the given volume
 */
public static Uri getContentUri(String volumeName)
{
    return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath("video")
            .appendPath("thumbnails").build();
}

/**
 * The content:// style URI for the internal storage.
 */
public static Uri INTERNAL_CONTENT_URI =
        getContentUri("internal");

/**
 * The content:// style URI for the "primary" external storage
 * volume.
 */
public static Uri EXTERNAL_CONTENT_URI =
        getContentUri("external");

/**
 * The default sort order for this table
 */
public static String DEFAULT_SORT_ORDER = "video_id ASC";

/**
 * Path to the thumbnail file on disk.
 *
 * @deprecated Apps may not have filesystem permissions to directly
 *             access this path. Instead of trying to open this path
 *             directly, apps should use
 *             {@link ContentResolver#openFileDescriptor(Uri, String)}
 *             to gain access.
 */
@Deprecated
 
            public static String DATA = "_data";

/**
 * The original image for the thumbnal
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
            public static String VIDEO_ID = "video_id";

/**
 * The kind of the thumbnail
 */
@Column(Cursor.FIELD_TYPE_INTEGER)
            public static String KIND = "kind";

public static int MINI_KIND = ThumbnailConstants.MINI_KIND;
public static int FULL_SCREEN_KIND = ThumbnailConstants.FULL_SCREEN_KIND;
public static int MICRO_KIND = ThumbnailConstants.MICRO_KIND;

/**
 * The width of the thumbnal
 */
 
            public static String WIDTH = "width";

/**
 * The height of the thumbnail
 */
 
            public static String HEIGHT = "height";
        }
    }

    /** @removed */
   
    public static @NonNull Set<String> getAllVolumeNames(@NonNull Context context)
{
    return getExternalVolumeNames(context);
}

/**
 * Return list of all specific volume names that make up
 * {@link #VOLUME_EXTERNAL}. This includes a unique volume name for each
 * shared storage device that is currently attached, which typically
 * includes {@link MediaStore#VOLUME_EXTERNAL_PRIMARY}.
 * <p>
 * Each specific volume name can be passed to APIs like
 * {@link MediaStore.Images.Media#getContentUri(String)} to interact with
 * media on that storage device.
 */
public static @NonNull Set<String> getExternalVolumeNames(@NonNull Context context)
{
    StorageManager sm = context.getSystemService(StorageManager.class);
Set<String> volumeNames = new ArraySet<>();
for (VolumeInfo vi : sm.getVolumes())
{
    if (vi.isVisibleForUser(UserHandle.myUserId()) && vi.isMountedReadable())
    {
        if (vi.isPrimary())
        {
            volumeNames.add(VOLUME_EXTERNAL_PRIMARY);
        }
        else
        {
            volumeNames.add(vi.getNormalizedFsUuid());
        }
    }
}
return volumeNames;
    }

    /**
     * Return the volume name that the given {@link Uri} references.
     */
    public static @NonNull String getVolumeName(@NonNull Uri uri) {
    List<String> segments = uri.getPathSegments();
    if (uri.getAuthority().equals(AUTHORITY) && segments != null && segments.size() > 0)
    {
        return segments.get(0);
    }
    else
    {
        throw new IllegalArgumentException("Missing volume name: " + uri);
    }
}

/** {@hide} */
public static @NonNull String checkArgumentVolumeName(@NonNull String volumeName) {
    if (TextUtils.isEmpty(volumeName))
    {
        throw new IllegalArgumentException();
    }

    if (VOLUME_INTERNAL.equals(volumeName))
    {
        return volumeName;
    }
    else if (VOLUME_EXTERNAL.equals(volumeName))
    {
        return volumeName;
    }
    else if (VOLUME_EXTERNAL_PRIMARY.equals(volumeName))
    {
        return volumeName;
    }

    // When not one of the well-known values above, it must be a hex UUID
    for (int i = 0; i < volumeName.length(); i++)
    {
        char c = volumeName.charAt(i);
        if (('a' <= c && c <= 'f') || ('0' <= c && c <= '9') || (c == '-'))
        {
            continue;
        }
        else
        {
            throw new IllegalArgumentException("Invalid volume name: " + volumeName);
        }
    }
    return volumeName;
}

/**
 * Return path where the given specific volume is mounted. Not valid for
 * {@link #VOLUME_INTERNAL} or {@link #VOLUME_EXTERNAL}, since those are
 * broad collections that cover many paths.
 *
 * @hide
 */
@TestApi
    public static @NonNull File getVolumePath(@NonNull String volumeName)
            throws FileNotFoundException
{
    StorageManager sm = AppGlobals.getInitialApplication()
                .getSystemService(StorageManager.class);
return getVolumePath(sm.getVolumes(), volumeName);
    }

    /** {@hide} */
    public static @NonNull File getVolumePath(@NonNull List < VolumeInfo > volumes,
            @NonNull String volumeName) throws FileNotFoundException
{
        if (TextUtils.isEmpty(volumeName)) {
        throw new IllegalArgumentException();
    }

        switch (volumeName) {
            case VOLUME_INTERNAL:
            case VOLUME_EXTERNAL:
        throw new FileNotFoundException(volumeName + " has no associated path");
    }

    boolean wantPrimary = VOLUME_EXTERNAL_PRIMARY.equals(volumeName);
        for (VolumeInfo volume : volumes) {
        boolean matchPrimary = wantPrimary
              && volume.isPrimary();
        boolean matchSecondary = !wantPrimary
              && Objects.equals(volume.getNormalizedFsUuid(), volumeName);
        if (matchPrimary || matchSecondary)
        {
            File path = volume.getPathForUser(UserHandle.myUserId());
            if (path != null)
            {
                return path;
            }
        }
    }
        throw new FileNotFoundException("Failed to find path for " + volumeName);
    }

    /**
     * Return paths that should be scanned for the given volume.
     *
     * @hide
     */
    @TestApi
    public static @NonNull Collection<File> getVolumeScanPaths(@NonNull String volumeName)
            throws FileNotFoundException
{
        if (TextUtils.isEmpty(volumeName)) {
        throw new IllegalArgumentException();
    }

    Context context = AppGlobals.getInitialApplication();
    UserManager um = context.getSystemService(UserManager.class);

ArrayList<File> res = new ArrayList<>();
if (VOLUME_INTERNAL.equals(volumeName))
{
    addCanonicalFile(res, new File(Environment.getRootDirectory(), "media"));
    addCanonicalFile(res, new File(Environment.getOemDirectory(), "media"));
    addCanonicalFile(res, new File(Environment.getProductDirectory(), "media"));
}
else if (VOLUME_EXTERNAL.equals(volumeName))
{
    for (String exactVolume : getExternalVolumeNames(context))
    {
        addCanonicalFile(res, getVolumePath(exactVolume));
    }
    if (um.isDemoUser())
    {
        addCanonicalFile(res, Environment.getDataPreloadsMediaDirectory());
    }
}
else
{
    addCanonicalFile(res, getVolumePath(volumeName));
    if (VOLUME_EXTERNAL_PRIMARY.equals(volumeName) && um.isDemoUser())
    {
        addCanonicalFile(res, Environment.getDataPreloadsMediaDirectory());
    }
}
return res;
    }

    private static void addCanonicalFile(List<File> list, File file)
{
    try
    {
        list.add(file.getCanonicalFile());
    }
    catch (IOException e)
    {
        Log.w(TAG, "Failed to resolve " + file + ": " + e);
        list.add(file);
    }
}

/**
 * Uri for querying the state of the media scanner.
 */
public static Uri getMediaScannerUri()
{
    return AUTHORITY_URI.buildUpon().appendPath("none").appendPath("media_scanner").build();
}

/**
 * Name of current volume being scanned by the media scanner.
 */
public static String MEDIA_SCANNER_VOLUME = "volume";

    /**
     * Name of the file signaling the media scanner to ignore media in the containing directory
     * and its subdirectories. Developers should use this to avoid application graphics showing
     * up in the Gallery and likewise prevent application sounds and music from showing up in
     * the Music app.
     */


 


    }
}