﻿<?xml version="1.0" encoding="utf-8" ?>
<pages1:PageEnhancedNav
    x:Class="AppoMobi.PageFonts"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:appoMobi1="clr-namespace:AppoMobi"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:pages1="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    Title="Разработка"
    BackgroundColor="{x:Static appoMobi1:AppColors.Primary}"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}">

    <pages1:PageEnhancedNav.InsertContent>
        <pages1:IncludedContent
            x:Name="Stack"
            Margin="3"
            Padding="8,7,8,0"
            BackgroundColor="WhiteSmoke"
            HorizontalOptions="FillAndExpand" />
    </pages1:PageEnhancedNav.InsertContent>

</pages1:PageEnhancedNav>















