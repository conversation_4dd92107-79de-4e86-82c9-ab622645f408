﻿<?xml version="1.0" encoding="UTF-8"?>

<Grid
    x:Class="AppoMobi.Xam.FramedSelector"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Center"
    WidthRequest="300">

    <!--  placeholder  -->


    <!--  entry and line  -->
    <StackLayout Margin="0,0,0,15" Spacing="0">

        <Label
            x:Name="cEntry"
            FontSize="{x:Static xam:FontSizes.Entry}"
            HorizontalOptions="Center"
            TextColor="{x:Static xam:TextColors.Placeholder}" />

        <!--  line  -->
        <svg:GradientBox
            x:Name="cGradientLine"
            EndColor="{x:Static xam:BackColors.GradientEndLine}"
            GradientOrientation="Horizontal"
            HeightRequest="1"
            HorizontalOptions="FillAndExpand"
            StartColor="{x:Static xam:BackColors.GradientStartLine}" />


    </StackLayout>

    <Label
        x:Name="cPlaceholder"
        Margin="0,0,0,0"
        FontSize="{x:Static xam:FontSizes.EntrySmallPlaceholder}"
        HorizontalOptions="Fill"
        HorizontalTextAlignment="Center"
        TextColor="{x:Static xam:TextColors.Placeholder}"
        VerticalOptions="End" />

    <ContentView
        x:Name="PickerContainer"
        HeightRequest="1"
        IsClippedToBounds="True"
        Opacity="0" />

    <!--  hotspot  -->
    <gestures:LegacyGesturesBoxView
        Margin="0,4,0,0"
        HeightRequest="20"
        Tapped="OnTapped_Selector"
        VerticalOptions="Start"
        WidthRequest="150" />


</Grid>