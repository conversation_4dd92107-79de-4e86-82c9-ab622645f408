﻿

namespace AppoMobi
{
    public class CardView : Microsoft.Maui.Controls.Frame
    {
        public CardView()
        {
            Padding = new Thickness(0, 0, 0, 9);
            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                Padding = new Thickness(0, 0, 0, 3);
                HasShadow = false;
                BorderColor = Colors.Transparent;
                //BackgroundColor = Colors.Transparent;
            }
            else
            {
                CornerRadius = 5;
            }
        }
    }

    public class CFrame : Microsoft.Maui.Controls.Frame
    {
        public CFrame()
        {
            BorderColor = Colors.Gray;
            BackgroundColor = AppColors.BwHighlight;//.White;

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
//                Padding = new Thickness(0, 0, 0, 3);
                HasShadow = false; 
                BorderColor = Colors.Transparent;
            }
            else
            {
                CornerRadius = 5;
            }
        }
    }

}