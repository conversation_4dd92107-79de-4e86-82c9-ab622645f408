﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyLoading"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX">
    <ContentView.Content>

        <Grid
            x:Name="frameTransition"
            IsVisible="{Binding IsLoading}"
            VerticalOptions="FillAndExpand">
            <ActivityIndicator
                x:Name="Spinner"
                HorizontalOptions="Center"
                IsRunning="{Binding IsLoading}"
                VerticalOptions="Center" />
            <Label
                FontSize="12"
                BackgroundColor="{x:Static appoMobi:AppColors.LoadingBack}"
                TextColor="{x:Static appoMobi:AppColors.BwHighlight}"
                HorizontalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                Text="{x:Static resX:ResStrings.Loading}"
                VerticalOptions="End" />
        </Grid>


    </ContentView.Content>
</ContentView>