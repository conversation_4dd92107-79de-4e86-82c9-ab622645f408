using Android.Views;
using Android.Runtime;
using System;

namespace AppoMobi.Touch.Droid
{
	internal class DownUpGestureDetector
	{
		protected readonly DownUpGestureListener Listener;

		internal DownUpGestureDetector(DownUpGestureListener listener)
		{
			this.Listener = listener;
		}

		public bool OnTouchEvent(MotionEvent e)
		{
			bool flag = false;
			switch (e.ActionMasked)
			{
				case MotionEventActions.Down:
                case (MotionEventActions)5:
				{
					flag = this.Listener.OnDown(e);
					return flag;
				}
				case (MotionEventActions)1:
				case (MotionEventActions)3:
				case (MotionEventActions)6:
				{
					flag = this.Listener.OnUp(e);
					return flag;
				}
				case (MotionEventActions)2:
				case (MotionEventActions)4:
				{
					return flag;
				}
				default:
				{
					return flag;
				}
			}
		}
	}
}