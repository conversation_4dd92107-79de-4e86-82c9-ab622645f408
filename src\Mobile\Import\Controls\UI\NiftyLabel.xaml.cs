﻿using System;
using System.Diagnostics;
using System.Threading.Tasks;
using FFImageLoading.Maui;
using FFImageLoading.Transformations;
using AppoMobi.Touch;


using ContentView = Microsoft.Maui.Controls.ContentView;

namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    //===================================================================
    public partial class NiftyLabel : ContentView
    //===================================================================
    {
        public bool IsPressed { get; set; } = false;
        public string TintColor { get; set; } = AppColors.controls_dark;

        public Color OriginalBackColor = Colors.Transparent;
        public Color ColorHighlight = AppColors.AccentHighlight;
        public string HexColorBlink1 = AppColors.controls_light;
        public string HexColorBlink2 = AppColors.controls;
 

        //public Color ColorHighlight = AppColors.PrimaryHighlight;
   //     public string HexColorBlink1 = AppColors.primary_dark;
     //   public string HexColorBlink2 = AppColors.primary_darkest;








        
        // Constructor
        
        public NiftyLabel()
        {
            InitializeComponent();

//            BackImage.Transformations.Add(new TintTransformation
  //              { HexColor = "#0A"+AppColors.bw_black.Replace("#",""), EnableSolidColor = true });
            //BackImage.WidthRequest = Globals.Values.MaxNewsWidth;
            

        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);
            switch (propertyName)
            {
                case nameLabelText:
                    ControlLabelText.Text =
                        LabelText; //DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    sel.BackgroundColor = OriginalBackColor;
                    break;
                case nameDesc:
                    ControlLabelDesc.Text = Desc;
                    if (!string.IsNullOrEmpty(Desc))
                    {
                        MyDivider.IsVisible = true;
                        ControlLabelDesc.IsVisible = true;
                    }
                    else
                    {
                        MyDivider.IsVisible = false;
                        ControlLabelDesc.IsVisible = false;
                    }
                    break;
                case nameLabelIcon:
                    ControlLabelIcon.Source = LabelIcon;
                    break;
            }

        }

        
        // LabelText
        
        private const string nameLabelText = "LabelText";
        public static readonly BindableProperty LabelTextProperty = BindableProperty.Create(nameLabelText, typeof(string), typeof(NiftyLabel), string.Empty);
        public string LabelText
        {
            get { return (string)GetValue(LabelTextProperty); }
            set { SetValue(LabelTextProperty, value); OnPropertyChanged(); }
        }
        
        // LabelDesc
        
        private const string nameDesc = "Desc";
        public static readonly BindableProperty DescProperty = BindableProperty.Create(nameDesc, typeof(string), typeof(NiftyLabel), string.Empty);
        public string Desc
        {
            get { return (string)GetValue(DescProperty); }
            set { SetValue(DescProperty, value); OnPropertyChanged(); }
        }

        
        // LabelIcon
        
        private const string nameLabelIcon = "LabelIcon";
        public static readonly BindableProperty LabelIconProperty = BindableProperty.Create(nameLabelIcon, typeof(string), typeof(NiftyLabel), string.Empty);
        public string LabelIcon
        {
            get { return (string)GetValue(LabelIconProperty); }
            set { SetValue(LabelIconProperty, value); OnPropertyChanged(); }
        }

        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(NiftyLabel), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); OnPropertyChanged(); }
        }

        
        // Tapped
        
        public event EventHandler Tapped = null;
        private async void Grid_OnTapped(object sender, EventArgs e)
        {
            await Task.Delay(300);
            IsPressed = false;                
            Tapped?.Invoke(this, EventArgs.Empty);
        }
        
        // Shared
        
        public event EventHandler Shared = null;
        private async void OnShared(object sender, EventArgs e)
        {
            await Task.Delay(300);
            IsPressed = false;
            Shared?.Invoke(this, EventArgs.Empty);
        }

        private void ControlContainer_OnUp(object sender, DownUpEventArgs e)
        {
            Debug.WriteLine("*** GRID UP");
            IsPressed = false;

        }
        
        private void ControlContainer_OnDown(object sender, DownUpEventArgs e)
        
        {
            Debug.WriteLine("*** GRID DOWN");
            if (this != Globals.Values.NiftyLabelSelected) Globals.Values.NiftyLabelSelected?.ClearSelection();
            Globals.Values.NiftyLabelSelected = this;
            IsPressed = true;
            sel.BackgroundColor = ColorHighlight;
            MainThread.BeginInvokeOnMainThread(() => Grow(ControlLabelIcon));


        }

        public void ClearSelection()
        {
            sel.BackgroundColor = OriginalBackColor;

        }

        bool _growbusy = false;
        public async Task Grow(CachedImage myobject)
        {
            
            if (_growbusy) return;

            _growbusy = true;
            try
            {
                while (IsPressed)
                {
                        myobject.Transformations.Clear();
                        myobject.Transformations.Add(new TintTransformation
                            { HexColor = HexColorBlink1, EnableSolidColor = true });
                        myobject.ReloadImage();
                    await myobject.ScaleTo(1.25, 75);
                    await myobject.ScaleTo(1.0, 75);
                        myobject.Transformations.Clear();
                        myobject.Transformations.Add(new TintTransformation
                            { HexColor = HexColorBlink2, EnableSolidColor = true });
                        myobject.ReloadImage();
                    await myobject.ScaleTo(1.20, 75);
                    await myobject.ScaleTo(1.0, 75);
                }
            }
            catch
            {

            }
            myobject.Transformations.Clear();
            myobject.Transformations.Add(new TintTransformation
                { HexColor = TintColor, EnableSolidColor = true });
            myobject.ReloadImage();
            ClearSelection();
            _growbusy = false;
            //myobject.Transformations.Clear();
            //await myobject.ScaleTo(1.4, 75);
            //await myobject.ScaleTo(1.0, 75);

            /*
                        //MainThread.BeginInvokeOnMainThread(() =>
                        //{
                        try
                        {
                                myobject.ScaleTo(1.4, 75).ContinueWith((t) =>
                                    {
                                        try
                                        {
                                            myobject.ScaleTo(1.0, 75);
                                        }
                                        catch
                                        {

                                        }
                                    },
                                    scheduler: TaskScheduler.FromCurrentSynchronizationContext());
                            }
                            catch
                            {

                            }
                            _growbusy = false;
                    //    }
                    //);
              */
        }

        
        private void StackLayout_OnPanning(object sender, PanEventArgs e)
        
        {
            Debug.WriteLine("*** GRID PANNING");
            //DrawBackground();
        }
        /*
        
        public void DrawBackground()
        
        {
            BackImage.HeightRequest = ControlContainer.Height-20;
            BackImage.ReloadImage();
            BackImage.IsVisible = true;
        }
        */
    }
}