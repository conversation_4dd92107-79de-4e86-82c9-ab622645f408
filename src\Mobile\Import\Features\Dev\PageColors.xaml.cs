﻿using System;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PageColors : ContentPage
    {
        private const int BoxHeight = 30;

        public PageColors()
        {
            InitializeComponent();

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS) imgClose.Source = "close";

            var row = 0;

            AddColor(row, "primary highlight", AppColors.PrimaryHighlight); row++;
            AddColor(row, "primary lightest", AppColors.PrimaryLightest); row++;
            AddColor(row, "primary transparent", AppColors.PrimaryTransparent); row++;
            AddColor(row, "primary light", AppColors.PrimaryLight); row++;
            AddColor(row, "primary (set)", AppColors.Primary); row++;
            AddColor(row, "primary dark", AppColors.PrimaryDark); row++;
            AddColor(row, "primary icons", AppColors.PrimaryIcons); row++;
            AddColor(row, "primary darkest", AppColors.PrimaryDarkest); row++;


            //AddColor(row, "accent special light", AppColors.AccentSpecialLight); row++;
            //AddColor(row, "accent special (set)", AppColors.AccentSpecial); row++;
            //AddColor(row, "accent special dark", AppColors.AccentSpecialDark); row++;
            AddColor(row, "accent highlight", AppColors.AccentHighlight); row++;
            AddColor(row, "accent lightest", AppColors.AccentLightest); row++;
            AddColor(row, "accent light", AppColors.AccentLight); row++;
            AddColor(row, "accent lighter", AppColors.AccentLighter); row++;
            AddColor(row, "accent (set)", AppColors.Accent); row++;
            AddColor(row, "accent dark", AppColors.AccentDark); row++;
            AddColor(row, "accent darkest", AppColors.AccentDarkest); row++;

            AddColor(row, "bw highlight (set)", AppColors.BwHighlight); row++;
            AddColor(row, "bw grey light", AppColors.BwGreyLight); row++;
            AddColor(row, "bw grey", AppColors.BwGrey); row++;
            AddColor(row, "bw grey dark", AppColors.BwGreyDark); row++;
            AddColor(row, "bw darkest", AppColors.BwDarkest); row++;
            AddColor(row, "bw black", AppColors.BwBlack); row++;


        }

        
        void AddColor(int row, string name, Color color)
        
        {
            var box = new Microsoft.Maui.Controls.BoxView();
            box.HeightRequest = BoxHeight;
            box.HorizontalOptions = LayoutOptions.FillAndExpand;
            box.BackgroundColor = color;

            var lab = new Microsoft.Maui.Controls.Label();
            lab.VerticalOptions = LayoutOptions.Center;
            lab.Margin = new Thickness(2,0,0,0);
            lab.Text = name + " " + color.GetHexDesc();//+" l:"+ color.Luminosity.ToString();
            if (color.GetLuminosity() > 0.8) lab.TextColor = Colors.Black; else lab.TextColor = Colors.White;
            MyGrid.Add(box, 0, row);
            MyGrid.Add(lab, 0, row);
        }//        Microsoft.Maui.Controls.BoxView NewBox()

        private bool _nocrash { get; set; } = true;
        
        private async void Image_OnTapped(object sender, EventArgs e)
        
        {
            if (!_nocrash) return;
            _nocrash = false;
            await Navigation.PopModalAsync();
            _nocrash = true;
        }
    }
}