﻿using Android.Content;
using Android.Graphics;
using Android.Graphics.Drawables;
using Android.Media;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;
using Build = Android.OS.Build;
using Matrix = Android.Graphics.Matrix;
using Microsoft.Maui.Platform;

namespace AppoMobi.Droid  
{
    public class VideoRenderer : ViewRenderer<VideoPlayer.VideoView, FrameLayout>,
        TextureView.ISurfaceTextureListener,
        ISurfaceHolderCallback
    {
        
        public VideoRenderer(Context context) : base(context)
        
        {
            
        }

        private bool _isCompletionSubscribed = false;

        private FrameLayout _mainFrameLayout = null;

        private Android.Views.View _mainVideoView = null;
        //private Android.Views.View _placeholder = null;

        private MediaController mediaController = null;//

        private Android.Content.Res.AssetFileDescriptor afd = null;

        private MediaPlayer _videoPlayer { get; set; } = null;
        internal MediaPlayer VideoPlayer
        {
            get
            {
                if (_videoPlayer == null)
                {
                    _videoPlayer = new MediaPlayer();
                    Console.WriteLine("*** Video: Created NEW MediaPlayer");

                    if (!_isCompletionSubscribed)
                    {
                        _isCompletionSubscribed = true;
                        _videoPlayer.Completion += Player_Completion;
                    }

                    _videoPlayer.VideoSizeChanged += Player_VideoSizeChanged;
                    _videoPlayer.Info += Player_Info;
                    _videoPlayer.Prepared += Player_Prepared;

                    if (Element.Source.Length > 0)
                    {
                        Console.WriteLine("*** Video: RESTARTING!!!");
                        VideoPlayer.Looping = Element.Loop;
                        RestartVideo();
                    }
                }
                return _videoPlayer;
            }
        }
        
        protected override void OnElementChanged(ElementChangedEventArgs<VideoPlayer.VideoView> e)
        
           
        {
            base.OnElementChanged(e);
            
Console.WriteLine(@"*** Video: ++++++++++++++++++++++++++++++++++++++++++++");

            var hack = VideoPlayer; //create if disposed

            if (Control == null)
            {
                _mainFrameLayout = new FrameLayout(Context);

                /*
                _placeholder = new Android.Views.View(Context)
                {
                    Background = new ColorDrawable(Xamarin.Forms.Color.Transparent.ToAndroid()),
                    LayoutParameters = new LayoutParams(
                        ViewGroup.LayoutParams.MatchParent,
                        ViewGroup.LayoutParams.MatchParent),
                };
                */
                if (Build.VERSION.SdkInt < BuildVersionCodes.IceCreamSandwich)
                {
                    Console.WriteLine("*** Using VideoView");

                    var videoView = new Android.Widget.VideoView(Context)
                    {
                        Background = new ColorDrawable(Colors.Transparent.ToPlatform()),
                        Visibility = ViewStates.Gone,
                        LayoutParameters = new LayoutParams(
                            ViewGroup.LayoutParams.MatchParent,
                            ViewGroup.LayoutParams.MatchParent),
                    };

                    ISurfaceHolder holder = videoView.Holder;
                    if (Build.VERSION.SdkInt < BuildVersionCodes.Honeycomb)
                    {
                        holder.SetType(SurfaceType.PushBuffers);
                    }
                    holder.AddCallback(this);

                    _mainVideoView = videoView;
                }
                else
                {
                    Console.WriteLine("*** Using TextureView");

                    var textureView = new TextureView(Context)
                    {
                        Background = new ColorDrawable(Colors.Transparent.ToPlatform()),
                        Visibility = ViewStates.Gone,
                        LayoutParameters = new LayoutParams(
                            ViewGroup.LayoutParams.MatchParent,
                            ViewGroup.LayoutParams.MatchParent),
                    };

                    textureView.SurfaceTextureListener = this;

                    _mainVideoView = textureView;
                }



                _mainFrameLayout.AddView(_mainVideoView);
                //_mainFrameLayout.AddView(_placeholder);


                SetNativeControl(_mainFrameLayout);

                /*
                //MediaController
                mediaController = new MediaController(this.Context);
                mediaController.Visibility=ViewStates.Gone;
                mediaController.SetAnchorView(_mainVideoView);
                */

                PlayVideo(Element.Source);
            }
            if (e.OldElement != null)
            {
                // Unsubscribe
                if (_videoPlayer != null && _isCompletionSubscribed)
                {
                    _isCompletionSubscribed = false;
                    _videoPlayer.Completion -= Player_Completion;
                }
            }
            if (e.NewElement != null)
            {
                // Subscribe
                if (_videoPlayer != null && !_isCompletionSubscribed)
                {
                    _isCompletionSubscribed = true;
                    _videoPlayer.Completion += Player_Completion;
                }
            }
        }
        
        protected override void OnElementPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        
        {
            base.OnElementPropertyChanged(sender, e);
            if (Element == null || Control == null)
                return;

            Console.WriteLine("*** Video: OnElementPropertyChanged " + e.PropertyName);


            var hack = VideoPlayer; //create if disposed

            if (e.PropertyName == AppoMobi.VideoPlayer.VideoView.SourceProperty.PropertyName)
            {
                RestartVideo();
            }
            else if (e.PropertyName == AppoMobi.VideoPlayer.VideoView.LoopProperty.PropertyName)
            {
                Console.WriteLine("*** Is Looping? " + Element.Loop);
                VideoPlayer.Looping = Element.Loop;
            }
        }

        private void RestartVideo()
        {
            Console.WriteLine("*** Play video: " + Element.Source);
            PlayVideo(Element.Source);

        }

        //HOOKS
        
        private void Player_Completion(object sender, EventArgs e)
        
        {
            Console.WriteLine("*** Video: Finished playing");
            Element?.OnFinishedPlaying?.Invoke();
        }

        public int PlayerWidth { get; set; }
        public int PlayerHeight { get; set; }

        
        private void Player_VideoSizeChanged(object sender, MediaPlayer.VideoSizeChangedEventArgs args)
        
        {
            Console.WriteLine("*** Video: VideoSizeChanged");
            PlayerWidth = args.Width;
            PlayerHeight = args.Height;
            AdjustTextureViewAspect(PlayerWidth, PlayerHeight);
        }
        
        private void Player_Info(object sender, MediaPlayer.InfoEventArgs args)
        
        {
            Console.WriteLine("*** Video: Info");
            Console.WriteLine("*** onInfo what={0}, extra={1}", args.What, args.Extra);
            if (args.What == MediaInfo.VideoRenderingStart)
            {
                Console.WriteLine("*** [MEDIA_INFO_VIDEO_RENDERING_START] placeholder GONE");
                //_placeholder.Visibility = ViewStates.Gone;
            }
        }
        
        private void Player_Prepared(object sender, EventArgs args)
            
        {
            Console.WriteLine("*** Video: Prepared, Starting..");
            _mainVideoView.Visibility = ViewStates.Visible;
            _videoPlayer.Start();
            if (Element != null) _videoPlayer.Looping = Element.Loop;
        }

        
        
        private void RemoveVideo()
        
        
        {
            Console.WriteLine("*** Video: Removed MediaPlayer");
            Console.WriteLine("*** Video: -------------------------------------------------------------");
            //_placeholder.Visibility = ViewStates.Visible;
            _videoPlayer.Completion -= Player_Completion;
            _videoPlayer.VideoSizeChanged -= Player_VideoSizeChanged;
            _videoPlayer.Info -= Player_Info;
            _videoPlayer.Prepared -= Player_Prepared;
            VideoPlayer.Reset();
            VideoPlayer.Release();
            //VideoPlayer.Release();
            _videoPlayer = null;
            afd.Close();
            _isCompletionSubscribed = false;
        }


        
        private void PlayVideo(string fullPath)
        
        {
            

            try
            {
                afd = Context.Assets.OpenFd(fullPath);
            }
            catch (Java.IO.IOException ex)
            {
                Console.WriteLine("*** Play video: " + Element.Source + " not found because " + ex);
                _mainVideoView.Visibility = ViewStates.Gone;
            }
            catch (Exception ex)
            {
                Console.WriteLine("*** Error openfd: " + ex);
                _mainVideoView.Visibility = ViewStates.Gone;
            }

            if (afd != null)
            {
                Console.WriteLine("*** Lenght " + afd.Length);
                VideoPlayer.Reset();
                VideoPlayer.SetDataSource(afd.FileDescriptor, afd.StartOffset, afd.Length);
                VideoPlayer.PrepareAsync();
            }
        }
        
        private void AdjustTextureViewAspect(int videoWidth, int videoHeight)
        
        {
            if (!(_mainVideoView is TextureView))
                return;

            if (Control == null)
                return;

            Console.WriteLine("*** AdjustTextureViewAspect");

            var hack = VideoPlayer; //create if disposed

            var control = Control;

            var textureView = _mainVideoView as TextureView;

            var controlWidth = control.Width;
            var controlHeight = control.Height;
            var aspectRatio = (double)videoHeight / videoWidth;

            int newWidth, newHeight;

            if (controlHeight <= (int)(controlWidth * aspectRatio))
            {
                // limited by narrow width; restrict height
                newWidth = controlWidth;
                newHeight = (int)(controlWidth * aspectRatio);
            }
            else
            {
                // limited by short height; restrict width
                newWidth = (int)(controlHeight / aspectRatio);
                newHeight = controlHeight;
            }

            int xoff = (controlWidth - newWidth) / 2;
            int yoff = (controlHeight - newHeight) / 2;

            Console.WriteLine("*** video=" + videoWidth + "x" + videoHeight +
                              " view=" + controlWidth + "x" + controlHeight +
                              " newView=" + newWidth + "x" + newHeight +
                              " off=" + xoff + "," + yoff);

            var txform = new Matrix();
            textureView.GetTransform(txform);
            txform.SetScale((float)newWidth / controlWidth, (float)newHeight / controlHeight);
            txform.PostTranslate(xoff, yoff);
            textureView.SetTransform(txform);
        }

        #region Surface Texture Listener

        public void OnSurfaceTextureAvailable(SurfaceTexture surface, int width, int height)
        {
            Console.WriteLine("*** Surface.TextureAvailable");
            VideoPlayer.SetSurface(new Surface(surface));
        }
        
        public bool OnSurfaceTextureDestroyed(SurfaceTexture surface)
        
        {
            Console.WriteLine("*** Surface.TextureDestroyed");
            RemoveVideo();
            return false;
        }
        
        public void OnSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height)
        
        {
            Console.WriteLine("*** Surface.TextureSizeChanged");
        }
        
        public void OnSurfaceTextureUpdated(SurfaceTexture surface)
        
        {
            //Console.WriteLine("*** Surface.TextureUpdated");
        }

        #endregion

        #region Surface Holder Callback

        public void SurfaceChanged(ISurfaceHolder holder, [GeneratedEnum] Format format, int width, int height)
        {
            Console.WriteLine("*** Surface.Changed");
        }

        public void SurfaceCreated(ISurfaceHolder holder)
        {
            Console.WriteLine("*** Surface.Created");
            VideoPlayer.SetDisplay(holder);
        }

        public void SurfaceDestroyed(ISurfaceHolder holder)
        {
            Console.WriteLine("*** Surface.Destroyed");
            RemoveVideo();
        }

        #endregion
    }
}
