﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using AppoMobi.Tenant;
using AppoMobi.Xam;
using FFImageLoading.Maui;
using FFImageLoading.Transformations;



namespace AppoMobi.Nifty
{


    public class NiftyIconedList : AppoMobi.Touch.LegacyGesturesContentView
    {
        Microsoft.Maui.Controls.Grid MyGrid = new Microsoft.Maui.Controls.Grid();
        private int SelectedRow = -1;
        private AppoMobi.Touch.LegacyGesturesBoxView SelectionBox = null;
        private Color OriginalBackColor = Colors.Transparent;
        private object SelectedIcon = null;
        private double VertMargin = 10;

        public bool IsPressed { get; set; } = false;
        public bool IsPanned { get; set; } = false;


        private float alpha = 0.15f;

        
        public NiftyIconedList()
        
        {


            MyGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Auto) });
            MyGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            MyGrid.RowSpacing = 0;
            MyGrid.ColumnSpacing = 16.0;//(double)Application.Current.Resources["StandardSpacing"];


           // this.Down += (s, e) => { Debug.WriteLine("*** GRID DOWN"); };
           // this.Up += (s, e) => { Debug.WriteLine("*** GRID UP"); };
           // this.Panning += (s, e) => { Debug.WriteLine("*** GRID PANNING"); };


            /*
            var th = (Microsoft.Maui.Controls.OnPlatform<Microsoft.Maui.Controls.Thickness>)Application.Current.Resources["ContactPadding"];

            switch (Device.OS)
            {
                case TargetPlatform.Android:
                    MyGrid.Padding = th.Android;
                    break;
                case TargetPlatform.iOS:
                    MyGrid.Padding = th.iOS;
                    break;
                default:
                    MyGrid.Padding = th.WinPhone;
                    break;
            }
            */

            //var _vm = 14.0;//(Microsoft.Maui.Controls.OnPlatform<double>)Application.Current.Resources["NiftyListVerticalPadding"];
            //switch (Device.OS)
            //{
            //    case TargetPlatform.Android:
            //        VertMargin = _vm.Android;
            //        break;
            //    case TargetPlatform.iOS:
            //        VertMargin = _vm.iOS;
            //        break;
            //    case TargetPlatform.WinPhone:
            //        VertMargin = _vm.WinPhone;
            //        break;
            //}

            VertMargin = 14.0;


            RedrawList();

        }




        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case nameList:
                    RedrawList();//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;

                    //                case nameLabelIcon:
                    //                    ControlLabelIcon.Source = LabelIcon;
                    //                    break;
            }

        }


        //-------------------------------------------------------------
        // KeepHighlight
        //-------------------------------------------------------------
        private const string nameKeepHighlight = "KeepHighlight";
        public static readonly BindableProperty KeepHighlightProperty = BindableProperty.Create(nameKeepHighlight, typeof(bool), typeof(NiftyIconedList), true); //, BindingMode.TwoWay
        public bool KeepHighlight
        {
            get { return (bool)GetValue(KeepHighlightProperty); }
            set { SetValue(KeepHighlightProperty, value); }
        }




        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(NiftyIconedList), string.Empty);
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }

        
        // SelectedParams
        
        private const string nameSelectedParams = "SelectedParams";
        public static readonly BindableProperty SelectedParamsProperty = BindableProperty.Create(nameSelectedParams, typeof(string), typeof(NiftyIconedList), string.Empty);
        public string SelectedParams
        {
            get { return (string)GetValue(SelectedParamsProperty); }
            set { SetValue(SelectedParamsProperty, value); }
        }


        
        // List
        
        private const string nameList = "List";
        public static readonly BindableProperty ListProperty = BindableProperty.Create(nameList, typeof(IEnumerable), typeof(NiftyIconedList), null);
        public IEnumerable List
        {
            get { return (IEnumerable)GetValue(ListProperty); }
            set { SetValue(ListProperty, value); }
        }


        
        // Tapped
        
        public new event EventHandler LongPressed = null;


        
        // Tapped
        
        public new event EventHandler Tapped = null;
        //private async void OnTapped(object sender, EventArgs e)
        //{
        //  Tapped?.Invoke(this, EventArgs.Empty);
        //}


        //---------------------------------------------------------
        public class MySel : AppoMobi.Touch.LegacyGesturesBoxView
        //---------------------------------------------------------
        {
            public string Tag { get; set; }
            public int Position { get; set; }
            public string Params { get; set; }

            //public NiftyIconedList Daddy { get; set; } = null;




            public AppoMobi.Touch.LegacyGesturesBoxView SelectionBox { get; set; }
            public CachedImage SelectedIcon { get; set; }

            public MySel()
            {
                Tag = "";

                Position = -1;
                SelectionBox = null;
            }

        }
        //This is for accessing the passed listview item object        
        public class MySelEventArgs : EventArgs
        {
            public string Tag { get; set; }
            public string Params { get; set; }
        }



        //---------------------------------------------------------
        private void RedrawList()
        //---------------------------------------------------------
        {
            if (List == null) return;
            //cleanup
            MyGrid.Children.Clear();

            CachedImage img;
            Microsoft.Maui.Controls.Label lab;

            FontIconLabel fontIcon;

            Microsoft.Maui.Controls.BoxView sep;
            MySel sel;


            //setup new template table
            //   var MyTable = new Microsoft.Maui.Controls.Grid();



            var SelMargin = new Thickness(4, 0, 0, 0);


            List<CMyListItem> items = (List<CMyListItem>)List;

            int row = -1;
            for (int a = 0; a < items.Count; a++)
            {
                row++;
                //separator

                fontIcon = new FontIconLabel();
                //fontIcon.Margin = new Thickness(0.0, 1.0, 0.0, 0.0);

                sel = new MySel();
                img = new CachedImage();
                lab = new Microsoft.Maui.Controls.Label();


                //selection box - passing all params here
                //sel.Daddy = this;
                sel.Tag = items[a].Tag;
                sel.Params = items[a].Parameters;
                sel.HorizontalOptions = LayoutOptions.FillAndExpand;
                sel.VerticalOptions = LayoutOptions.FillAndExpand;
                sel.Margin = SelMargin;
                sel.Color = OriginalBackColor;
                //OldColor = sel.Color;
                sel.Position = row;
                sel.SelectionBox = sel;
                sel.SelectedIcon = img;

                MyGrid.Add(sel, 0, row);
                Microsoft.Maui.Controls.Grid.SetColumnSpan(sel, 2);

                //Font Icon
                if (items[a].FontIcon != null)
                {
                    var subGrid = new Microsoft.Maui.Controls.Grid();
                    subGrid.Margin = new Thickness(0, VertMargin, 0, VertMargin);
                    subGrid.VerticalOptions = LayoutOptions.CenterAndExpand;
                    //   subGrid.BackgroundColor = AppColors.Site_Panel;
                    subGrid.HeightRequest = img.WidthRequest = 24;
                    fontIcon.FontSize = 19;
                    
                    fontIcon.Font = "FaSolid";
                    if (items[a].FontOverride != null)
                    {
                        fontIcon.Font = items[a].FontOverride;
                    }

                    if (TenantOptions.DarkSkin)
                    {
                        fontIcon.TextColor = AppColors.Controls;
                    }
                    else
                    {
                        fontIcon.TextColor = AppColors.PrimaryDarkTransparent;
                    }

                    fontIcon.StyleId = "NiftyIconedList";

                    fontIcon.TextColor = AppColors.ColoredIcons;

                    fontIcon.VerticalOptions = LayoutOptions.Center;
                    fontIcon.HorizontalOptions = LayoutOptions.Center;
             
                    fontIcon.SetIcon(items[a].FontIcon);
                    
                    subGrid.Add(fontIcon);
                    MyGrid.Add(subGrid, 0, row);
                }
                else
                {
                    //image
                    // img.BackgroundColor = AppColors.Site_Panel;
                    img.InputTransparent = true;
                    img.Source = items[a].Image;
                    img.HeightRequest = img.WidthRequest = 23;
                    img.DownsampleToViewSize = true;
                    img.VerticalOptions = LayoutOptions.Center;
                    img.Margin = new Thickness(16, VertMargin, 0, VertMargin);
                    MyGrid.Add(img, 0, row);
                }//image

                //label
                lab.InputTransparent = true;
                
                lab.FontSize = 14.5;
                lab.TextColor = TextColors.Title;

                //lab.TextColor = Color.Parse("747575");
                lab.VerticalOptions = LayoutOptions.Center;
                lab.Margin = new Thickness(0, VertMargin, 17, VertMargin);

                lab.Text = items[a].Desc;
                MyGrid.Add(lab, 1, row);



                MyGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                //hello gestures


                //---------------------------------------------------------
                sel.LongPressing += async (s, e) =>
                //---------------------------------------------------------
                {
                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    //cc = (Color) Application.Current.Resources["accent"]; //.FromHex("#c7ac56").MultiplyAlpha(alpha);
                    cc = AppColors.Controls.MultiplyAlpha(alpha);
                    //cc = cc.MultiplyAlpha(0.05);
                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OriginalBackColor;
                        }
                        //OldColor = sel1.Color;
                        SelectionBox = sel1;
                        //sel1.Color = OriginalBackColor;
                        //sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        SelectedParams = ss.Params;
                        Tag = ss.Tag;
                        //MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        await Task.Delay(1);

                        var ee = new MySelEventArgs();
                        ee.Tag = ss.Tag;
                        ee.Params = ss.Params;
                        LongPressed?.Invoke(this, ee);

                    }
                    ;


                };


                /*
                //---------------------------------------------------------
                sel.Down += (s, e) =>
                //---------------------------------------------------------
                {
                    IsPressed = true;
                    Debug.WriteLine("*** DOWN");

                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    //                    cc = Color.Parse("#c7ac56").MultiplyAlpha(alpha);
                    cc = AppColors.Controls.MultiplyAlpha(alpha);                    //Color.Parse("edf4f4");//(Color)Application.Current.Resources["primary"];//.FromHex("#c7ac56").MultiplyAlpha(alpha);
                    //cc = cc.MultiplyAlpha(0.015);

                    AppoMobi.Touch.BoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OriginalBackColor;
                        }
                        //OldColor = sel1.Color;
                        SelectionBox = sel1;
                        sel1.Color = cc;
                        
                        
                        SelectedIcon = ss.SelectedIcon;

                        MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        //Grow(ss.SelectedIcon);
                    }

                };
                */

                /*
                //---------------------------------------------------------
                sel.Up += (s, e) =>
                //---------------------------------------------------------
                {
                    IsPressed = false;
                    if (SelectionBox != null && DeviceInfo.Current.Platform != DevicePlatform.Android)
                    {
                        SelectionBox.Color = OriginalBackColor;
                    }
                    Debug.WriteLine("*** UP");
                };


                //---------------------------------------------------------
                sel.Panning += (s, e) =>
                //---------------------------------------------------------
                {
                    Debug.WriteLine("*** PANNING");
                };
                //---------------------------------------------------------
                sel.Panned += (s, e) =>
                //---------------------------------------------------------
                {
                    Debug.WriteLine("*** PANNED");
                };
                //---------------------------------------------------------
                sel.Swiped += (s, e) =>
                //---------------------------------------------------------
                {
                    Debug.WriteLine("*** SWIPED");
                };
                */

                //gesture recognizer
                //var tapGestureRecognizer = new TapGestureRecognizer();
                //---------------------------------------------------------
                //tapGestureRecognizer.Tapped += (s, e) => 
                sel.Tapped += async (s, e) =>
                //---------------------------------------------------------
                {
                    // handle the 
                    IsPressed = false;
                    var ss = (MySel)s;
                    SelectedRow = ss.Position;
                    var cc = new Color();
                    cc = AppColors.Controls.MultiplyAlpha(alpha);
                    //cc = cc.MultiplyAlpha(0.025);
                    AppoMobi.Touch.LegacyGesturesBoxView sel1 = ss.SelectionBox;
                    if (sel1 != null)
                    {
                        if (SelectionBox != null)
                        {
                            SelectionBox.Color = OriginalBackColor;
                        }
                        //OldColor = sel1.Color;
                        SelectionBox = sel1;
                        //sel1.Color = OriginalBackColor;
                        //sel1.Color = cc;
                        SelectedIcon = ss.SelectedIcon;
                        SelectedParams = ss.Params;
                        Tag = ss.Tag;
                        //MainThread.BeginInvokeOnMainThread(() => Grow(ss.SelectedIcon));
                        await Task.Delay(1);
                        Tapped?.Invoke(this, EventArgs.Empty);

                    }

                };
                //sel.GestureRecognizers.Add(tapGestureRecognizer);



                //separator
                // 
                if (a == items.Count - 1) //last row
                    continue;

                row++;
                sep = new Microsoft.Maui.Controls.BoxView();
                sep.HorizontalOptions = LayoutOptions.FillAndExpand;
                sep.VerticalOptions = LayoutOptions.Start;
                sep.Color = AppColors.Divider;
                
                sep.Opacity = 0.5;

                if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                {
                    sep.HeightRequest = 0.5;
                    sep.Margin = new Thickness(0, 0, 0, 0);
                }
                else
                {
                    sep.HeightRequest = 0.8;
                    sep.Margin = SelMargin;
                }

                //e3e4e4  //android listview
                //(Color)Application.Current.Resources["ListSeparator"];
                MyGrid.Add(sep, 0, row);
                Microsoft.Maui.Controls.Grid.SetColumnSpan(sep, 2);
                MyGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            Content = MyGrid;

        }
        
        bool _growbusy = false;
        public async Task Grow(CachedImage myobject)
        
        {
            if (_growbusy) return;

            _growbusy = true;
            try
            {
                while (IsPressed)
                {
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                    { HexColor = AppColors.controls_dark, EnableSolidColor = true });
                    myobject.ReloadImage();
                    await myobject.ScaleTo(1.25, 75);
                    await myobject.ScaleTo(1.0, 75);
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                    {
                        HexColor = AppColors.controls_darkest,
                        EnableSolidColor = true
                    });
                    myobject.ReloadImage();
                    await myobject.ScaleTo(1.20, 75);
                    await myobject.ScaleTo(1.0, 75);
                }
            }
            catch
            {

            }
            myobject.Transformations.Clear();
            myobject.ReloadImage();
            _growbusy = false;
            //myobject.Transformations.Clear();
            //await myobject.ScaleTo(1.4, 75);
            //await myobject.ScaleTo(1.0, 75);

            /*
                        //MainThread.BeginInvokeOnMainThread(() =>
                        //{
                        try
                        {
                                myobject.ScaleTo(1.4, 75).ContinueWith((t) =>
                                    {
                                        try
                                        {
                                            myobject.ScaleTo(1.0, 75);
                                        }
                                        catch
                                        {

                                        }
                                    },
                                    scheduler: TaskScheduler.FromCurrentSynchronizationContext());
                            }
                            catch
                            {

                            }
                            _growbusy = false;
                    //    }
                    //);
              */
        }

    }
}
