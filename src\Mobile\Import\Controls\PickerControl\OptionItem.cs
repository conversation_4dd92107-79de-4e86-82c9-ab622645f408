﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AppoMobi.Framework.Abstractions;

namespace AppoMobi.Forms.Models
{

    public class SingleOptionItem : OptionItem
    {
        public SingleOptionItem()
        {

        }

        public SingleOptionItem(string id, string title, ICommand command, bool selected=false)
        {
            Id = id;
            Title = title;
            OnSelected = command;
            Selected = selected;
        }

        public SingleOptionItem(string id, string title, ICommand command, Func<SingleOptionItem, bool>isSelected)
        {
            Id = id;
            Title = title;
            OnSelected = command;
            Selected = isSelected(this);
        }


        public ICommand OnSelected { get; set; }
    }



    public class OptionItem : INotifyPropertyChanged, IHasStringId, ISelectableOption
    {
        
        public string Id { get; set; }
        
        public string Title { get; set; }

        private bool _Selected;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    OnPropertyChanged();
                }
            }
        }

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            var changed = PropertyChanged;
            if (changed == null)
                return;

            changed.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        private bool _IsReadOnly;
        public bool IsReadOnly
        {
            get
            {
                return _IsReadOnly;
            }
            set
            {
                if (_IsReadOnly != value)
                {
                    _IsReadOnly = value;
                    OnPropertyChanged();
                }
            }
        }

    }
}
