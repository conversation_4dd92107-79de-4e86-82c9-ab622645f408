﻿namespace AppoMobi.Xam.Converters
{
    //public class IntIsZeroConverter : ConverterBase
    //{
    //    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        const bool success = true;
 
    //        if (value is int)
    //        {
    //            if ((int)value == 0) return success;
    //        }
    //        return !success;
    //    }

 
    //}
}
