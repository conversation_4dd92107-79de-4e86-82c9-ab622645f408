﻿

namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PagePlaceholder
    {


        public PagePlaceholder()
        {
            InitializeComponent();

            //RightIcon1Source = "resource://AppoMobi.Mobile.Images.Navbar.close.png";

            //ToggleButtonVisibility(ButtonType.Right1, false);


           // imgUC.Source = Core.ImageFromResources("UI", "uc.png");


        }






        //
        //protected override bool OnBackButtonPressed()
        //
        //{


        //    if (Navigation.NavigationStack.Count < 3)
        //    {
        //        Globals.Values.Root?.ShowMenu();
        //        //Globals.Values.AppMenu.ClickMenuItem(Constants.CONST_MENU_HOME);
        //        return true;
        //    }
        //    else
        //        return false;
        //    //    MainThread.BeginInvokeOnMainThread(async () => {
        //    //        var result = await this.DisplayAlert("Thalion", "Вы хотите выйти из программы?", "Да", "Нет");
        //    //        if (result) { _canClose = false; base.OnBackButtonPressed();}
        //    //    });
        //    return true;
        //}


    }
}