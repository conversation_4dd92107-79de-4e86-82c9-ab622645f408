﻿using System.Reflection;

namespace AppoMobi.Xam
{
    //***********************************************************
    public class Fonts
    //***********************************************************
    {
        //public static FontPreset Istok = new FontPreset("Istok-Regular.ttf", -0.05, 3.0);

        public static FontPreset Istok = new FontPreset(
            "Istok-Regular", "Istok-Bold",
            "Istok-Regular.ttf", "Istok-Bold.ttf",
            -0.05, 3.5); //Moved down

        public static FontPreset RalewayUi = new FontPreset(
            "Raleway-SemiBold", "Raleway-Black",
            "Raleway-SemiBold.ttf", "Raleway-Black.ttf",
           0.00, 0.0); //Moved down

        public static FontPreset RalewayFun = new FontPreset(
            "Raleway-Light", "Raleway-SemiBold",
            "Raleway-Light.ttf", "Raleway-SemiBold.ttf",
            0.00, 0.0); //Moved down

        public static FontPreset PTSans = new FontPreset(
            "PTS55F", "PTS75F",
            "PTS55F.ttf", "PTS75F.ttf",
            0.00, 0.0); //Moved down

        public static FontPreset LiberationSansBold = new FontPreset(
            "LiberationSans-Bold", "LiberationSans-Bold",
            "LiberationSans-Bold.ttf", "LiberationSans-Bold.ttf",
            0.00, 0.0);

        public static FontPreset LiberationSans = new FontPreset(
            "LiberationSans", "LiberationSans-Bold",
            "LiberationSans-Regular.ttf", "LiberationSans-Bold.ttf",
            0.00, 0.0);

        public static FontPreset Charis = new FontPreset( //Charis SIL
            "CharisSILR", "CharisSILB",
            "CharisSILR.ttf", "CharisSILB.ttf",
            0.00, 0.0);

        public static FontPreset Nicholson = new FontPreset(
            "FHA Nicholson French NCV", "FHA Nicholson French NCV",
            "FHANicholson.otf", "FHANicholson.otf",
            0.00, 0.0); //Moved down

        public static FontPreset PTSansBold = new FontPreset(
            "PTS75F", "PTS75F",
            "PTS75F.ttf", "PTS75F.ttf",
            0.00, 0.0); //Moved down

        public static FontPreset Alice = new FontPreset(
            "Alice-Regular", "Alice-Regular",
            "Alice-Regular.ttf", "Alice-Regular.ttf",
            3.0, 0.0);

        public static FontPreset Crimson = new FontPreset(
            "Crimson-Roman", "Crimson-Bold",
            "Crimson-Roman.ttf", "Crimson-Bold.ttf", 
            2.1, -2.6);
        
        public static FontPreset RobotoLight = new FontPreset(
            "Roboto-Light", "Roboto-Bold", 
            "RobotoLight.ttf", "RobotoBold.ttf", 
            0.0, 2.0); //Moved down

        public static FontPreset RobotoMedium = new FontPreset(
            "Roboto-Medium", "Roboto-Bold",
            "RobotoMedium.ttf", "RobotoBold.ttf",
            1.0, -1.0); //Moved down

        public static FontPreset AvenirNextCyr = new FontPreset(
            "AvenirNextCyr-Regular", "AvenirNextCyr-Bold", //for iOS
            "AvenirNextCyr-Regular.ttf", "AvenirNextCyr-Bold.ttf", //for Droid
            0.0, 1.8);

        public static FontPreset MavenPro = new FontPreset(
            "MavenProRegular", "MavenProBold",
            "MavenPro-Regular.ttf", "MavenPro-Bold.ttf", 
            1.15, 1.7, 1.1);

        public static FontPreset AppleSDGothicNeo = new FontPreset("AppleSDGothicNeo-Light", "AppleSDGothicNeo-Medium", 0.0, 0.0);


        public static FontPreset DefaultAndroid = new FontPreset("sans-serif", 
            "sans-serif-medium", 0.0, 0.0);


        public static FontPreset AndroidRobotoLight = new FontPreset(
            "sans-serif-light", "sans-serif-medium", 0.0, 0.0);

        public static FontPreset DefaultiOs = new FontPreset("Helvetica", "Helvetica-Bold", 1.0, 0.0);

        //-----------------------------------------------------------------
        public static FontPreset GetPresetByFilename(string filename)
        //-----------------------------------------------------------------
        {
            FontPreset ret = null;
            if (!string.IsNullOrEmpty(filename))
            {
                FieldInfo[] properties = typeof(Fonts).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontPreset))
                    {
                        FontPreset preset = (FontPreset)property.GetValue(null);
                        if (preset.Filename.ToLowerInvariant() == filename.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        //-----------------------------------------------------------------
        public static FontPreset GetPresetByName(string name)
        //-----------------------------------------------------------------
        {
            FontPreset ret = null;
            if (!string.IsNullOrEmpty(name))
            {
                FieldInfo[] properties = typeof(Fonts).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontPreset))
                    {                        
                        FontPreset preset = (FontPreset)property.GetValue(null);
                        if (property.Name.ToLowerInvariant() == name.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        //-----------------------------------------------------------------
        public static FontPreset GetPresetById(string id)
        //-----------------------------------------------------------------
        {
            FontPreset ret = null;
            if (!string.IsNullOrEmpty(id))
            {
                FieldInfo[] properties = typeof(Fonts).GetFields();
                foreach (FieldInfo property in properties)
                {
                    if (property.FieldType == typeof(FontPreset))
                    {
                        FontPreset preset = (FontPreset)property.GetValue(null);
                        if (preset.Id.ToLowerInvariant() == id.ToLowerInvariant())
                        {
                            ret = preset;
                            break;
                        }
                    }
                }
            }
            return ret;
        }

    }

 
}
