﻿using Android.Util;
using Android.Views;
using AndroidX.Navigation.Fragment;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using Activity = Android.App.Activity;

namespace AppoMobi.Droid.Renderers
{
    public class XamPageRenderer : PageHandler
    {
        protected override void ConnectHandler(ContentViewGroup platformView)
        {
            Control = platformView;
            RegisterForKeyboardNotifications();

            var check = Control.Background;

            Control.SetBackgroundColor(Colors.Transparent.ToPlatform());

            check = Control.Background;

            var fm = Platform.CurrentActivity.GetFragmentManager();
            foreach (var fragment in fm.Fragments)
            {
                if (fragment is NavHostFragment navHost)
                {
                    var stop = fragment;
                    fragment.View.Background = null;
                }
            }
            //var navHostFragment = fm.FindFragmentById(_Microsoft.Android.Resource.Designer.Resource.Id.nav_host) as NavHostFragment;

            //var nav = Platform.CurrentActivity.FindViewById(_Microsoft.Android.Resource.Designer.Resource.Id.nav_host);


            //if (nav != null)
            //{
            //    var framework = AppContext.TargetFrameworkName;
            //    nav.Background = null;
            //}

            base.ConnectHandler(platformView);
        }

        protected override void DisconnectHandler(ContentViewGroup platformView)
        {
            UnregisterForKeyboardNotifications();

            base.DisconnectHandler(platformView);

            Control = null;
        }


        private ViewGroup Control { get; set; }


        private int KeyboardSize(global::Android.Views.View rootView)

        {
            int softKeyboardHeight = 100;
            var r = new Android.Graphics.Rect();
            try
            {
                rootView.GetWindowVisibleDisplayFrame(r);
                DisplayMetrics dm = rootView.Resources.DisplayMetrics;
                int heightDiff = rootView.Bottom - r.Bottom;
                if (heightDiff > softKeyboardHeight * dm.Density)
                {
                    return (int)(heightDiff / dm.Density);
                }
                return 0;
                //return heightDiff > softKeyboardHeight * dm.Density;
            }
            catch (Exception e)
            {
                return 0;
            }
        }


        private XamMyLayoutListener _keyboardShowObserver;

        void RegisterForKeyboardNotifications()
        {
            if (Control != null)
            {
                if (_keyboardShowObserver == null)
                {
                    _keyboardShowObserver = new XamMyLayoutListener(Control, (NiftyPage)VirtualView);
                }
            }
        }


        void UnregisterForKeyboardNotifications()
        {

            if (_keyboardShowObserver != null)
            {
                _keyboardShowObserver.Release();
                _keyboardShowObserver = null;
            }

        }


        public bool Rendered { get; private set; }


        public override void PlatformArrange(Microsoft.Maui.Graphics.Rect frame)
        {
            var daddy = VirtualView as NiftyPage;

            if (!Rendered && daddy != null)
            {
                if (Context is Activity activity)
                {
                    if (daddy.HideStatusBar)
                    {
                        activity.Window.AddFlags(WindowManagerFlags.Fullscreen);
                        //var options = activity.Window.DecorView.SystemUiVisibility;
                        //options |= (int)SystemUiFlags.Fullscreen;
                        //options
                        //activity.Window.DecorView.SystemUiVisibility = (StatusBarVisibility) SystemUiFlags.Fullscreen;
                    }
                    else
                    {
                        activity.Window.ClearFlags(WindowManagerFlags.Fullscreen);
                        //activity.Window.DecorView.SystemUiVisibility..ClearFlags(WindowManagerFlags.Fullscreen);
                    }
                }

                Rendered = true;
                System.Diagnostics.Debug.WriteLine($"[RENDERED] Page {daddy.Title}");
                //daddy.PageRendered();
            }

            base.PlatformArrange(frame);
        }

    }


    public class XamMyLayoutListener : Java.Lang.Object, ViewTreeObserver.IOnGlobalLayoutListener
    {
        public void Release()
        {
            _view.ViewTreeObserver.RemoveOnGlobalLayoutListener(this);
            _view = null;
            _control = null;
        }

        public XamMyLayoutListener(global::Android.Views.View view, NiftyPage control)
        {
            _view = view;
            _control = control;
            _view.ViewTreeObserver.AddOnGlobalLayoutListener(this);
        }

        private global::Android.Views.View _view;
        private NiftyPage _control;

        public void OnGlobalLayout()
        {
            if (_control == null)
            {
                return; //check for being disposed.. all fine
            }
            _control.KeyboardResized(KeyboardSize(_view));

        }

        private double KeyboardSize(global::Android.Views.View rootView)
        {
            int softKeyboardHeight = 100;
            var r = new Android.Graphics.Rect();
            try
            {
                rootView.GetWindowVisibleDisplayFrame(r);
                DisplayMetrics dm = rootView.Resources.DisplayMetrics;
                int heightDiff = rootView.Bottom - r.Bottom;
                if (heightDiff > softKeyboardHeight * dm.Density)
                {
                    return heightDiff / dm.Density;
                }
                return 0;
                //return heightDiff > softKeyboardHeight * dm.Density;
            }
            catch (Exception e)
            {
                return 0;
            }
        }

    }


}


