﻿using AppoMobi.Pages;
using AppoMobi.UI;
using System;
using System.Threading.Tasks;


namespace AppoMobi.Xam
{
    //***************************************************************************
    public static partial class CoreExtensions
    //***************************************************************************
    {
        public static async Task InvokeMenuItem(this Core core, string key)
        {
            foreach (var item in TabsAndMenu.MenuList)
            {

                if (!string.IsNullOrEmpty(item.Platform))
                {
                    if (DeviceInfo.Platform.ToString() != item.Platform)
                        continue;
                }


                if (item.Id == key)
                {
                    if (item.OnSelected != null)
                    {
                        item.OnSelected.Invoke();
                    }
                    break;
                }
            }
        }


        public static async Task PopToRoot(this Core core, bool animated = false)

        {
            /*
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    // Update the UI
                    if (core.HasTabs)
                    {
                        var stack = Globals.Values.TabsMain.CurrentPage.Navigation.NavigationStack;
                        var lastPage = stack[stack.Count - 1];
                        int c = 0;
                        foreach (var page in stack)
                        {
                            c++;
                            if (page is EnhancedPage && c != 1)
                            {
                                ((EnhancedPage)page).PageOnDisappearing?.Invoke(null, null);;
                                ((EnhancedPage)page).ReleaseOnClosing();
                            }
                        }
                        //android
                        //await lastPage.Navigation.PopToRootAsync(false);
                        //ios
                        await Globals.Values.TabsMain.CurrentPage.Navigation.PopToRootAsync(false);

                    }

                    else
                    {
                        //todo dispose all enha
                        var stack = Application.Current.MainPage.Navigation.NavigationStack;
                        var lastPage = stack[stack.Count - 1];
                        int c = 0;
                        foreach (var page in stack)
                        {
                            c++;
                            if (page is EnhancedPage && c != 1)
                            {
                                ((EnhancedPage)page).ReleaseOnClosing();
                            }
                        }
                        await lastPage.Navigation.PopToRootAsync(false);
                    }

                });
     */

        }

        //******************************************************
        public static async Task PopPageByType(this Core core, Type page)
        //******************************************************
        {
            foreach (var item in Application.Current.MainPage.Navigation.NavigationStack)
            {
                if (item.GetType() == page)
                {
                    if (item is IPageEnhancedNav)
                    {
                        await ((IPageEnhancedNav)item).GoBack();
                        return;
                    }
                }
            }
        }

        //******************************************************
        public static void InsertPageUnderLast(this Core core, Page page)
        //******************************************************
        {
            var navStack = Application.Current.MainPage.Navigation.NavigationStack;
            Application.Current.MainPage.Navigation.InsertPageBefore(page,
                Application.Current.MainPage.Navigation.NavigationStack[navStack.Count - 1]);
        }

        //******************************************************
        public static async Task PopAllPagesUnderLast(this Core core)
        //******************************************************
        {
            var navStack = Application.Current.MainPage.Navigation.NavigationStack;
            var popCount = navStack.Count;
            for (int m = popCount - 2; m > 0; m--) //todo check for tabs
            {
                var remove = Application.Current.MainPage.Navigation.NavigationStack[m];
                if (remove is IPageEnhancedNav)
                    await ((IPageEnhancedNav)remove).Destroy();
                Application.Current.MainPage.Navigation.RemovePage(remove);
            }
        }

        //******************************************************
        public static async Task PopAllModals(this Core core)
        //******************************************************
        {
            var modalStack = Application.Current.MainPage.Navigation.ModalStack;
            var modalCount = modalStack.Count; //eval payment page
            //close modals
            for (int m = modalCount; m > 0; m--)
            {
                await Application.Current.MainPage.Navigation.PopModalAsync(false);
            }
        }

        //******************************************************
        public static bool PageTagIsInStack(this Core core, Type type)
        //******************************************************
        {
            var navStack = Application.Current.MainPage.Navigation.NavigationStack;
            var popCount = navStack.Count;
            for (int m = popCount; m > 0; m--) //todo check for tabs
            {
                var page = Application.Current.MainPage.Navigation.NavigationStack[m - 1];
                if (page.GetType() == type)
                {
                    return true;
                }
            }
            return false;
        }

        //******************************************************
        public static bool PageTagIsInModalStack(this Core core, Type type)
        //******************************************************
        {
            var navStack = Application.Current.MainPage.Navigation.ModalStack;
            var popCount = navStack.Count;
            for (int m = popCount; m > 0; m--) //todo check for tabs
            {
                var page = Application.Current.MainPage.Navigation.NavigationStack[m - 1];
                if (page.GetType() == type)
                {
                    return true;
                }
            }
            return false;
        }

        //******************************************************
        public static bool PageTagIsInStack(this Core core, string tag)
        //******************************************************
        {
            var navStack = Application.Current.MainPage.Navigation.NavigationStack;
            var popCount = navStack.Count;
            for (int m = popCount; m > 0; m--) //todo check for tabs
            {
                var page = Application.Current.MainPage.Navigation.NavigationStack[m - 1];
                if (page is IPageEnhanced)
                {
                    if (((IPageEnhanced)page).Tag == tag)
                    {
                        return true;
                    }
                }
            }
            return false;
        }




    }
}
