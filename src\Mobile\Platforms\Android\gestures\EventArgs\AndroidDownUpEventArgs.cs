using Android.Views;
using AppoMobi.Touch;
using System;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidDownUpEventArgs : DownUpEventArgs
	{
		public AndroidDownUpEventArgs(MotionEvent current, Android.Views.View view)
		{
			this.Cancelled = current.Action == MotionEventActions.Cancel;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(current);
			this.TriggeringTouches = new int[] { current.ActionIndex };
		}
	}
}