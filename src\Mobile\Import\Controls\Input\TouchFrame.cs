﻿ 
using System;

using AppoMobi.Touch;

namespace AppoMobi.Forms.Controls
{
    public class TouchFrame : AppoMobi.Touch.LegacyGesturesFrame
    {
        public TouchFrame()
        {
            Down += OnDown;
            Up += OnUp;
        }

        public double TimeLockDownMs { get; set; }

        //-------------------------------------------------------------
        // Reaction
        //-------------------------------------------------------------
        private const string nameReaction = "Reaction";
        public static readonly BindableProperty ReactionProperty = BindableProperty.Create(nameReaction, typeof(HotspotReaction), typeof(TouchFrame), HotspotReaction.Minify); //, BindingMode.TwoWay
        public HotspotReaction Reaction
        {
            get { return (HotspotReaction)GetValue(ReactionProperty); }
            set { SetValue(ReactionProperty, value); }
        }


        private bool _TouchDown;
        public bool TouchDown
        {
            get { return _TouchDown; }
            set
            {
                if (_TouchDown != value)
                {
                    _TouchDown = value;
                    OnPropertyChanged();
                }
            }
        }

 


        protected Color _savedBorderColor;

        protected Color _savedColor;

        private double _savedOpacity;

        //-------------------------------------------------------------
        // DownBorderColor
        //-------------------------------------------------------------
        private const string nameDownBorderColor = "DownBorderColor";
        public static readonly BindableProperty DownBorderColorProperty = BindableProperty.Create(nameDownBorderColor, typeof(Color), typeof(TouchFrame), Colors.Transparent); //, BindingMode.TwoWay
        public Color DownBorderColor
        {
            get { return (Color)GetValue(DownBorderColorProperty); }
            set { SetValue(DownBorderColorProperty, value); }
        }


        //-------------------------------------------------------------
        // DownOpacity
        //-------------------------------------------------------------
        private const string nameDownOpacity = "DownOpacity";
        public static readonly BindableProperty DownOpacityProperty = BindableProperty.Create(nameDownOpacity, typeof(double), typeof(TouchFrame), 0.75); //, BindingMode.TwoWay
        public double DownOpacity
        {
            get { return (double)GetValue(DownOpacityProperty); }
            set { SetValue(DownOpacityProperty, value); }
        }

        //-------------------------------------------------------------
        // DownScale
        //-------------------------------------------------------------
        private const string nameDownScale = "DownScale";
        public static readonly BindableProperty DownScaleProperty = BindableProperty.Create(nameDownScale, typeof(double), typeof(TouchFrame), 0.985); //, BindingMode.TwoWay
        public double DownScale
        {
            get { return (double)GetValue(DownScaleProperty); }
            set { SetValue(DownScaleProperty, value); }
        }


        //-------------------------------------------------------------
        // TintColor
        //-------------------------------------------------------------
        private const string nameTintColor = "TintColor";
        public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(TouchFrame), Colors.Transparent); //, BindingMode.TwoWay
        public Color TintColor
        {
            get { return (Color)GetValue(TintColorProperty); }
            set { SetValue(TintColorProperty, value); }
        }


        private void OnUp(object sender, DownUpEventArgs e)
        {

            if (Reaction == HotspotReaction.Minify)
            {
                Scale = 1.0;
            }
            else
            if (Reaction == HotspotReaction.Zoom)
            {
                Scale = 1.0;
            }

            Opacity = _savedOpacity;
            if (_savedBorderColor != null)
            {
                Stroke = new SolidColorBrush(_savedBorderColor);
            }
            else
            {
                Stroke = null;
            }
            BackgroundColor = _savedColor;

            TouchDown = false;
        }

        private bool _lockDown;

        private void OnDown(object sender, DownUpEventArgs e)
        {
            if (_lockDown)
                return;

            if (TimeLockDownMs > 0)
            {
                _lockDown = true;
                Device.StartTimer(TimeSpan.FromMilliseconds(TimeLockDownMs),() =>
                {
                    _lockDown = false;
                    return false;
                });
            }

            if (_savedOpacity != DownOpacity)
                _savedOpacity = Opacity;

            if (_savedBorderColor!= DownBorderColor && Stroke is SolidColorBrush brush)
                _savedBorderColor = brush.Color;

            if (_savedColor != TintColor)
                _savedColor = BackgroundColor;

            if (Reaction == HotspotReaction.Tint)
            {
                Stroke = new SolidColorBrush(DownBorderColor);
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
            }
            else
            if (Reaction == HotspotReaction.Minify)
            {
                Stroke = new SolidColorBrush(DownBorderColor);
                Opacity = DownOpacity;
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
                Scale = DownScale;
            }
            else
            if (Reaction == HotspotReaction.Zoom)
            {
                Stroke = new SolidColorBrush(DownBorderColor);
                Opacity = DownOpacity;
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
                Scale = 1.1;
            }

            TouchDown = true;
        }
    }
}