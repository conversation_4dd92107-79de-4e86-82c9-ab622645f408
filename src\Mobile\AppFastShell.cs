﻿using System;
using System.Diagnostics;
using System.Windows.Input;
using AppoMobi;
using AppoMobi;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;
using AppoMobi.Models;
using AppoMobi.Pages;
using AppoMobi.Touch;
using Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;
using Page = Microsoft.Maui.Controls.Page;

namespace AppoMobi.Mobile.Views
{
    public class AppFastShell : FastShell, IWithFlyout
    {
        void HideTabsSelection()
        {
            Tabs.ShowSelection(false);
        }


        private bool canUnlockOpening;
        public BottomTabs Tabs { get; set; }
        public ViewSwitcher ViewSwitcher { get; set; }

        protected override void OnDisposing()
        {
            App.Instance.Messager.Unsubscribe(this, "MenuClicked");
            App.Instance.Messager.Unsubscribe(this, "WidgetClicked");

            Super.InsetsChanged -= OnInsetsChanged;

            ViewSwitcher?.Dispose();
            ViewSwitcher = null;

            Tabs?.Dispose();
            Tabs = null;

            base.OnDisposing();
        }

        public string Uid = Guid.NewGuid().ToString("N");

        public NavigationViewModel Presentation;

        public AppFastShell(NavigationViewModel vm, IServiceProvider services) : base(services)
        {
            Presentation = vm;

            App.Shell = this;

            this.FlyoutEnabled = false; //byebye

            Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific.Page.SetUseSafeArea(this, false);

            InitMenu();

            //ROUTES
            foreach (var appRoute in AppRoutes.GetRoutes())
            {
                RegisterRoute(appRoute.Item1, appRoute.Item2);
            }

            RegisterActionRoute("home", () => { App.Instance.Messager.All("SelectRootTabExec", "first"); });
            //RegisterActionRoute("reqs", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "1");
            //});
            //RegisterActionRoute("chats", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "2");
            //});
            //RegisterActionRoute("bills", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "3");
            //});
            RegisterActionRoute("settings", () => { App.Instance.Messager.All("SelectRootTabExec", "last"); });

            Super.InsetsChanged += OnInsetsChanged;

            Globals.Values.AppRoot = this;


            App.Instance.Messager.Subscribe<TabbedMenuItem>(this, "WidgetClicked", async (sender, item) =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    Debug.WriteLine($"WidgetClicked at {Uid}");

                    INavigation navigateFrom = this.Navigation;

                    try
                    {
                        CloseMenu();
                    }
                    catch (Exception exception)
                    {
                        Debug.WriteLine(exception);
                    }

                    if (item == null)
                        return;

                    bool pseudoTab = true;
                    bool modal = false; //if true pushes with MAUI 

                        await GetMyPage(item.ContentClass, modal, true, item.NameInHeader, pseudoTab,
                            item.ContentIsFullScreen, false);

                    //overlay navigation
                    //await GetMyPage(item.ContentClass, true, true, item.NameInHeader, true, item.ContentIsFullScreen);
                });
            });


            /*
            App.Instance.Messager.Subscribe<MenuPageContentItem>(this, "MenuClicked", async (sender, item) =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    Debug.WriteLine($"Menu ItemSelected at {Uid}");

                    INavigation navigateFrom = this.Navigation;

                    try
                    {
                        CloseMenu();
                    }
                    catch (Exception exception)
                    {
                        Debug.WriteLine(exception);
                    }

                    if (item == null) return;

                    if (item.OnSelected != null)
                    {
                        item.OnSelected.Invoke();
                        return;
                    }

                    //new
                    if (item.Id == "exit")
                    {
                        Core.Native.CloseApp();
                        return;
                    }

                    if (item.Id == "www")
                    {
                        await Core.Current.PushInstance(typeof(Webpage), "http://www.artoffoto.com", "Art of Foto", "");
                        return;
                    }

                    if (item.Id == "login") return;

                    if (item.Id == "logoff" || item.Id == "logout")
                    {
                        //todo
                        var ok = await Core.Current.ShowPrompt(
                            "Выйти из учетной записи? Вы сможете войти в нее снова позднее на любом устройстве, иcпользуя прежний номер телефона.");
                        if (!ok) return;
                        //    await AuthModel.Instance.LogOff();
                        return;
                    }

                    if (item.Id == "link")
                    {
                        var url = item.Url;
                        await Core.Current.PushInstance(typeof(Webpage), url, item.Title, "");
                        return;
                    }

                    if (item.PseudoTab)
                    {
                        //todo detect if tab exist so we just select tab instead of opening new page:

                        //just scan existing tabbed list
                        var tabIndex = -1;
                        var visibilityIndex = -1;
                        var visibility = Core.Current.GetTabsVisibility();
                        foreach (var tab in TabsAndMenu.TabsList)
                        {
                            if (!string.IsNullOrEmpty(tab.Platform))
                                if (!tab.Platform.Contains(DeviceInfo.Platform.ToString()))
                                    continue;

                            //process tabs from visible in favorites
                            visibilityIndex++;
                            if (visibilityIndex < visibility.Count)
                                if (!visibility[visibilityIndex])
                                    continue;

                            tabIndex++; // index for visible tabs only

                            if ((item.TargetType == tab.PageClass && item.TargetType != null)
                                || (item.ContentType == tab.ContentClass && item.ContentType != null))
                            {
                                //found

                                var log = "Unknown";
                                if (item.TargetType != null)
                                    log = item.TargetType.Name;
                                if (item.ContentType != null)
                                    log = item.ContentType.Name;
                                App.Logger.Info("Menu", log);

                                item.Tab = tabIndex;

                                await navigateFrom.PopToRootAsync();
                                break;
                            }
                        }

                        if (item.Tab < 0)
                            HideTabsSelection();
                    }

                    if (item.Tab >= 0 && Core.Current.HasTabs) //tab requested
                    {
                        SelectTab(item.Tab);
                    }
                    else
                    {
                        //todo

                        //if (item.TargetType == TabsType)
                        //{
                        //    Detail = Tabs;
                        //    return;
                        //}

                        //View lazy = null;
                        //if (item.TargetType != null)
                        //{
                        //    //todo optional  pageTab.TitleImage = item.NavBarImage;
                        //    lazy = new LazyWrapContentType(item.TargetType, item.Title, item.ContentIsFullScreen);
                        //}
                        //else
                        //{
                        //    throw new UnsolvableConstraintsException(
                        //        "PageTabs unimplemented case for item.ContentClass == null");
                        //    //pageTab = Activator.CreateInstance(item.PageClass);
                        //}

                        if (item.ContentType != null)
                        {
                            App.Logger.Info("Menu", item.ContentType.Name);

                            await GetMyPage(item.ContentType, item.Modal, true, item.Title, item.PseudoTab,
                                item.ContentIsFullScreen);
                        }
                        else
                        {
                            //App.Logger.Info("Menu", item.TargetType.Name);
                            await GetMyPage(item.TargetType, item.Modal, false, item.Title, item.PseudoTab);
                        }
                    }
                });
            });
            */

        }

        void OnInsetsChanged(object sender, EventArgs e)
        {
            OnLayoutInvalidated();
        }

        public virtual void OnLayoutInvalidated()
        {
            App.Instance.Messager.All("AppLayoutInvalidated", "shell");
        }

        public void InitMenu()
        {
            Flyout.Title = "...";
            if (Flyout is ContentPage drawer)
            {
                drawer.Content = new DrawerMenuView();
            }

            //FlyoutEnabled = false;
            this.IsGestureEnabled = false;
            FlyoutLayoutBehavior = FlyoutLayoutBehavior.Popover;

            var masterPage = new MenuPageX();

            Globals.Values.AppMenu = masterPage;

            List<MenuPageContentItem> finalMenu = new();
            foreach (var item in TabsAndMenu.MenuList)
            {
                if (!string.IsNullOrEmpty(item.Platform))
                    if (DeviceInfo.Platform.ToString() != item.Platform)
                        continue;

                if (string.IsNullOrEmpty(item.Module)
                    || TabsAndMenu.ModulesList.Contains(item.Module))
                    finalMenu.Add(item);
            }

            masterPage.InitMenu(finalMenu);

            Flyout = masterPage;
        }

        public void SelectTab(int tab)
        {
            //silly check
            if (tab < 0 || !Core.Current.HasTabs) return;

            App.Instance.Messager.All("PopToRoot", $"{tab}");
        }

        private int didMyself { get; set; }
        private bool _opening { get; set; }

        private async Task GetMyPage(Type page, bool modal, bool isContent = false, string title = null,
            bool pseudoTab = false, bool contentFullScreen = false, bool hideNavigation=false)
        {
            Debug.WriteLine("Root GetMyPage");

            if (_opening)
            {
                if (canUnlockOpening)
                    Device.StartTimer(TimeSpan.FromMilliseconds(1500), () =>
                    {
                        _opening = false;
                        return false;
                    });
                return;
            }

            _opening = true;
            canUnlockOpening = true;

            try
            {
                Page tmp;

                INavigation navigateFrom = this;
                //if (Globals.Values.TabsMain != null)
                //    navigateFrom = Globals.Values.TabsMain.CurrentPage.Navigation;
                //else
                //    navigateFrom = Detail.Navigation;

                //didMyself = 1;
                //Globals.Values.AppMenu.ClickMenuItem(0);

                if (!isContent)
                {
                    //if (modal)
                    //{
                    //    await Core.PushInstanceModal(navigateFrom, page);
                    //}
                    //else
                    {
                        if (!pseudoTab)
                            await Core.PushInstance(navigateFrom, page);
                        else
                            await Core.PushInstancePseudoTab(navigateFrom, page);
                    }
                }
                else
                {
                    //avoid opening duplicate contents:
                    //foreach (var existingPage in navigateFrom.NavigationStack)
                    //    if (existingPage is WrapContent)
                    //        if (((WrapContent)existingPage).ContentType == page)
                    //            return;

                    if (ViewSwitcher != null && ViewSwitcher.ActiveView is ILazyView lazyView)
                    {
                        if (lazyView.Content is ViewEnhancedNav en) // WrapTabIncludedContent
                        {
                            if (en.InsertContent.GetType() == page)
                                return;
                        }
                    }

                    await navigateFrom.PopToRootAsync();

                    if (modal)
                    {
                        var pageInst = Activator.CreateInstance(typeof(WrapContent), page, contentFullScreen, hideNavigation);

                        if (title != null)
                            ((WrapContent)pageInst).Title = title;

                        await Core.PushInstance(navigateFrom, (Page)pageInst); //Modal
                    }
                    else
                    {
                        //var wrap = new LazyWrapContentType(page, title, contentFullScreen, hideNavigation);
                        var wrap = new WrapIncludedContent(page, title, contentFullScreen);

                        //if (pseudoTab)
                        //    ((IPageEnhanced)wrap).PseudoTab = true;

                        ViewSwitcher.PushView(wrap, true);

                        //await Core.PushInstance(navigateFrom, (Page)pageInst);
                    }
                }

                if (Globals.Values.bFromModal) Globals.Values.bFromModal = false;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                _opening = false;
            }
        }

        protected override void OnStarted()
        {
            base.OnStarted();

            try
            {
                App.Instance.MainPage = this;
            }
            catch (Exception e)
            {
                // crash: https://github.com/dotnet/maui/blob/7d5aa8ca42896b4056c9315235250be5b7c151f4/src/Core/src/Handlers/View/ViewHandlerOfT.cs#L34-L44
                // someone inside the maui team secretly hates other developers because they can get to 0 open issues
                // so it's like.. ha lemmi throw this random-hard-to-catch shit to them
                Super.Log(e); 
            }
        }

        public override void OnNavBarInvalidated()
        {
            base.OnNavBarInvalidated();

            Presentation.UpdateControls();
        }

        public ICommand CommandMenuItemTapped
        {
            get
            {
                return new Command<object>(async (context) =>
                {
                    var item = context as MenuPageItem;
                    if (item != null)
                    {
                        Presentation.ShowMenu(false);

                        if (item.OnSelected != null)
                        {
                            item.OnSelected.Invoke();
                        }
                    }
                });
            }
        }

        public void ShowMenu()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                this.IsPresented = false;
                await Task.Delay(1);
                this.IsPresented = true;
            });
        }

        public void CloseMenu()
        {
            MainThread.BeginInvokeOnMainThread(() => { this.IsPresented = false; });
        }
    }
}
