﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.Mobile.Views.BottomTabs"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <ContentView.Content>
        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">

            <!--<BoxView
                Grid.Row="1"
                BackgroundColor="White"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="Fill" />-->

            <!--  we dont need shadowed frame for this project, its transparent  -->
            <!--<draw:SkiaFrame
                Margin="-16,0,-16,-16"
                BackgroundColor="Black"
                BorderColor="Transparent"
                CornerRadius="0"
                FrameMargin="5"
                HorizontalOptions="Fill"
                Opacity="1.0"
                Rotation="180"
                ShadowBlur="5"
                ShadowColor="Transparent"
                ShadowY="0"
                TranslationY="-5"
                VerticalOptions="Fill" />-->

            <Grid
                x:Name="_grid"
                Margin="8,0"
                ColumnSpacing="0"
                HorizontalOptions="FillAndExpand"
                RowDefinitions="0, *"
                RowSpacing="0"
                VerticalOptions="Fill" />

        </Grid>

    </ContentView.Content>
</ContentView>