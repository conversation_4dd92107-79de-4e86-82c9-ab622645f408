﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.Xam.NiftyDots"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui">

    <ContentView.Content>
        <xam:NiftyDataStack
            x:Name="ControlLayout"
            ItemsSource="{Binding DotsList}"
            Orientation="Horizontal"
            Spacing="8">
            <xam:NiftyDataStack.ItemTemplate>
                <DataTemplate>
                    <StackLayout VerticalOptions="Start">
                        <Grid>
                            <xam:NiftyDotImage 
                                ColorTint="{Binding ColorPrimary}"
                                DotSize="{Binding DotSize}"/>
                            <xam:NiftyDotImage 
                                ColorTint="{Binding ColorAccent}"
                                DotSize="{Binding DotSize}"
                                IsVisible="{Binding Selected}"/>
                        </Grid>
                    </StackLayout>
                </DataTemplate>
            </xam:NiftyDataStack.ItemTemplate>
        </xam:NiftyDataStack>

    </ContentView.Content>

</ContentView>