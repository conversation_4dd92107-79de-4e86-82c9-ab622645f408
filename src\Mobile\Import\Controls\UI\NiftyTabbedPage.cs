﻿using AppoMobi.Models;
using AppoMobi.Xam;
using Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;
using AppoMobi.Services;
using TabbedPage = Microsoft.Maui.Controls.TabbedPage;

namespace AppoMobi;

public partial class NiftyTabbedPage : TabbedPage
{
    //titles offset
    public float AppleTitlesOffset = -0.5f; // -4.0

    //title
    public float ios_tabs_font_size = 9.0f;
    public float ios_tabs_icon_image_canvas_height = 30f;
    public float ios_tabs_icon_image_canvas_width = 34f;

    //icons 
    public float ios_tabs_yoffset = -0.5f;
    public float ios_tabs_yoffset_notxt = 5f;

    public NiftyTabbedPage()
    {
        On<Microsoft.Maui.Controls.PlatformConfiguration.Android>().SetToolbarPlacement(ToolbarPlacement.Bottom);
        //On<Microsoft.Maui.Controls.PlatformConfiguration.Windows>().Element.
    }

    public NiftyTabbedPage(bool noSwipe = false)
    {
        DisableSwipe = noSwipe;

        //On<Microsoft.Maui.Controls.PlatformConfiguration.Android>().SetToolbarPlacement(ToolbarPlacement.Bottom);
    }

    public bool SelectionOff { get; set; }
    public List<TabbedMenuItem> TabbedMenuItems { get; private set; } = new();
    public EventHandler RendererNeedUpdate { get; set; }
    public ContentView Overlay { get; set; }
    public bool DisableSwipe { get; set; }
    public float AndroidReclaimVerticalSpace => AppUI.AndroidReclaimVerticalSpace;
    public float AppleBarHeight => AppUI.AppleBarHeight;

    /// <summary>
    ///     tabbed bar icons IMAGE size
    /// </summary>
    public float AppleIconsFontSize => AppUI.AppleIconsFontSize;

    /// <summary>
    ///     tabbed bar icons ZOOMED
    /// </summary>
    public float AppleIconsZoomFontSize => AppUI.AppleIconsZoomFontSize;

    public double InactiveIconsTransparency => AppUI.InactiveIconsTransparency;

    public void HideSelection()
    {
        SelectionOff = true;
        RendererNeedUpdate?.Invoke(this, null);

        //var t = ResStrings.call
    }

    public void ShowSelection()
    {
        SelectionOff = false;
        RendererNeedUpdate?.Invoke(this, null);
    }

    public void UpdateSkin()
    {
        RendererNeedUpdate?.Invoke(this, null);
    }

    public void OnRendered()
    {
        App.Instance.Messager.All("TabBarRendered", "");
    }
}

