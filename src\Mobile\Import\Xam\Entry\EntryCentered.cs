﻿using System;
using System.Diagnostics;
using System.Windows.Input;


namespace AppoMobi.Forms.Controls.Input
{
    public class EntryCentered : Microsoft.Maui.Controls.Entry, IDisposable
    {

        //private void EntryOnFocused(object sender, EventArgs eventArgs)
        //{
        //    App.Current.NavbarModel.FocusedElement = (VisualElement)sender;
        //}

        //private void EntryOnUnfocused(object sender, EventArgs eventArgs)
        //{
        //    if (App.Current.NavbarModel.FocusedElement == (VisualElement)sender)
        //        App.Current.NavbarModel.FocusedElement = null;
        //}

        public EntryCentered()
        {
            //FocusedEvent += EntryOnFocused;
            //UnfocusedEvent += EntryOnUnfocused;
        }

        public void Dispose()
        {
            //FocusedEvent -= EntryOnFocused;
            //UnfocusedEvent -= EntryOnUnfocused;

            OnDisposing();

        }



        protected virtual void OnDisposing()
        {

        }
        /// <summary>
        /// Focus left the field
        /// </summary>
        public event EventHandler UnfocusedEvent;

        //focus left the entry
        /// <summary>
        /// Focus entered field
        /// </summary>
        public event EventHandler FocusedEvent;

        /// <summary>
        /// Fires when text is typed so you can react to changes in entry
        /// </summary>
        public event EventHandler EditingEvent;



        //-------------------------------------------------------------
        // FocusedEventCommand
        //-------------------------------------------------------------
        private const string nameFocusedEventCommand = "FocusedEventCommand";
        public static readonly BindableProperty FocusedEventCommandProperty = BindableProperty.Create(nameFocusedEventCommand, typeof(ICommand), typeof(EntryCentered), null, BindingMode.OneTime); //, BindingMode.TwoWay
        public ICommand FocusedEventCommand
        {
            get { return (ICommand)GetValue(FocusedEventCommandProperty); }
            set { SetValue(FocusedEventCommandProperty, value); }
        }

        //-------------------------------------------------------------
        // UnfocusedEventCommand
        //-------------------------------------------------------------
        private const string nameUnfocusedEventCommand = "UnfocusedEventCommand";
        public static readonly BindableProperty UnfocusedEventCommandProperty = BindableProperty.Create(nameUnfocusedEventCommand, typeof(ICommand), typeof(EntryCentered), null, BindingMode.OneTime); //, BindingMode.TwoWay
        public ICommand UnfocusedEventCommand
        {
            get { return (ICommand)GetValue(UnfocusedEventCommandProperty); }
            set { SetValue(UnfocusedEventCommandProperty, value); }
        }


        public void OnEditing()
        {
            EditingEvent?.Invoke(this, null);
        }


        //-------------------------------------------------------------
        // IsFocused
        //-------------------------------------------------------------
        private const string nameIsFocused = "IsFocused";
        public new static readonly BindableProperty IsFocusedProperty = BindableProperty.Create(nameIsFocused, typeof(bool), typeof(EntryCentered), false); //, BindingMode.TwoWay
        public new bool IsFocused
        {
            get
            {
                return (bool)GetValue(IsFocusedProperty);
            }
            set
            {
                SetValue(IsFocusedProperty, value);
            }
        }



        //-------------------------------------------------------------
        // MaxLines
        //-------------------------------------------------------------
        private const string nameMaxLines = "MaxLines";
        public static readonly BindableProperty MaxLinesProperty = BindableProperty.Create(nameMaxLines, typeof(int), typeof(EntryCentered), 1); //, BindingMode.TwoWay
        public int MaxLines
        {
            get { return (int)GetValue(MaxLinesProperty); }
            set { SetValue(MaxLinesProperty, value); }
        }

        public void OnFocused()
        {
            IsFocused = true;
            FocusedEvent?.Invoke(this, null);
            FocusedEventCommand?.Execute(null);
        }

        //public Color BackgroundColor { get; set; }
        public void OnUnfocused()
        {
            IsFocused = false;
            UnfocusedEvent?.Invoke(this, null);
            UnfocusedEventCommand?.Execute(null);
        }


        //-------------------------------------------------------------
        // UnfocusLocked
        //-------------------------------------------------------------
        private const string nameUnfocusLocked = "UnfocusLocked";
        public static readonly BindableProperty UnfocusLockedProperty = BindableProperty.Create(nameUnfocusLocked, typeof(bool), typeof(EntryCentered), false); //, BindingMode.TwoWay
        public bool UnfocusLocked
        {
            get { return (bool)GetValue(UnfocusLockedProperty); }
            set { SetValue(UnfocusLockedProperty, value); }
        }


        //-------------------------------------------------------------
        // SelectOnFocus
        //-------------------------------------------------------------
        private const string nameSelectOnFocus = "SelectOnFocus";
        public static readonly BindableProperty SelectOnFocusProperty = BindableProperty.Create(nameSelectOnFocus, typeof(bool), typeof(EntryCentered), true); //, BindingMode.TwoWay
        public bool SelectOnFocus
        {
            get { return (bool)GetValue(SelectOnFocusProperty); }
            set { SetValue(SelectOnFocusProperty, value); }
        }



        public EventHandler RendererNeedUpdate { get; set; }

        
        protected override void OnSizeAllocated(double width, double height)
        
        {
            base.OnSizeAllocated(width, height);
            if (width > 0)
                RendererNeedUpdate?.Invoke(this, null);
        }


        //-------------------------------------------------------------
        // Centered
        //-------------------------------------------------------------
        private const string nameCentered = "Centered";
        public static readonly BindableProperty CenteredProperty = BindableProperty.Create(nameCentered, typeof(bool), typeof(EntryCentered), true); //, BindingMode.TwoWay
        public bool Centered
        {
            get { return (bool)GetValue(CenteredProperty); }
            set { SetValue(CenteredProperty, value); }
        }

        //-------------------------------------------------------------
        // PlaceHolderCentered
        //-------------------------------------------------------------
        private const string namePlaceHolderCentered = "PlaceHolderCentered";
        public static readonly BindableProperty PlaceHolderCenteredProperty = BindableProperty.Create(namePlaceHolderCentered, typeof(bool), typeof(EntryCentered), true); //, BindingMode.TwoWay
        public bool PlaceHolderCentered
        {
            get { return (bool)GetValue(PlaceHolderCenteredProperty); }
            set { SetValue(PlaceHolderCenteredProperty, value); }
        }

        //-------------------------------------------------------------
        // PlaceHolderFontFamily
        //-------------------------------------------------------------
        private const string namePlaceHolderFontFamily = "PlaceHolderFontFamily";
        public static readonly BindableProperty PlaceHolderFontFamilyProperty = BindableProperty.Create(namePlaceHolderFontFamily, typeof(string), typeof(EntryCentered), string.Empty);
        public string PlaceHolderFontFamily
        {
            get { return (string)GetValue(PlaceHolderFontFamilyProperty); }
            set { SetValue(PlaceHolderFontFamilyProperty, value); }
        }

        /*
        //-------------------------------------------------------------
        // LetterSpacing
        //-------------------------------------------------------------
        private const string nameLetterSpacing = "LetterSpacing";
        public static readonly BindableProperty LetterSpacingProperty = BindableProperty.Create(nameLetterSpacing, typeof(float), typeof(EntryCentered), FontSizes.LetterSpacing); //, BindingMode.TwoWay
        public float LetterSpacing
        {
            get { return (float)GetValue(LetterSpacingProperty); }
            set { SetValue(LetterSpacingProperty, value); }
        }


        //-------------------------------------------------------------
        // LineSpacing
        //-------------------------------------------------------------
        private const string nameLineSpacing = "LineSpacing";
        public static readonly BindableProperty LineSpacingProperty = BindableProperty.Create(nameLineSpacing, typeof(float), typeof(EntryCentered), 1.0f); //, BindingMode.TwoWay
        public float LineSpacing
        {
            get { return (float)GetValue(LineSpacingProperty); }
            set { SetValue(LineSpacingProperty, value); }
        }	
        */

        //-------------------------------------------------------------
        // PlaceholderColor
        //-------------------------------------------------------------
        private const string namePlaceholderColor = "PlaceholderColor";
        public new static readonly BindableProperty PlaceholderColorProperty = BindableProperty.Create(namePlaceholderColor, typeof(Color), typeof(EntryCentered), Colors.Gray); //, BindingMode.TwoWay
        public new Color PlaceholderColor
        {
            get { return (Color)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }

        //-------------------------------------------------------------
        // CursorColor
        //-------------------------------------------------------------
        private const string nameCursorColor = "CursorColor";
        public new static readonly BindableProperty CursorColorProperty = BindableProperty.Create(nameCursorColor, typeof(Color), typeof(EntryCentered), Colors.Black); //, BindingMode.TwoWay
        public new Color CursorColor
        {
            get { return (Color)GetValue(CursorColorProperty); }
            set { SetValue(CursorColorProperty, value); }
        }

        //-------------------------------------------------------------
        // PlaceholderTextSize
        //-------------------------------------------------------------
        private const string namePlaceholderTextSize = "PlaceholderTextSize";
        public static readonly BindableProperty PlaceholderTextSizeProperty = BindableProperty.Create(namePlaceholderTextSize, typeof(double), typeof(EntryCentered), 12.0); //, BindingMode.TwoWay
        public double PlaceholderTextSize
        {
            get { return (double)GetValue(PlaceholderTextSizeProperty); }
            set { SetValue(PlaceholderTextSizeProperty, value); }
        }

        
        protected string FilterDefault(string input)
        
        {
            return input;
        }

        public Func<string, int, int, string> InputFilter { get; set; }

        
        public void SetInputFilter(Func<string, int, int, string> filter)
        
        {

            InputFilter = filter;
            RendererNeedUpdate?.Invoke(this, null);
        }

        //-------------------------------------------------------------
        // TextSize
        //-------------------------------------------------------------
        /// <summary>
        /// Use this instead of FontSize or renderer won't work as expected
        /// </summary>
        private const string nameTextSize = "TextSize";
        public static readonly BindableProperty TextSizeProperty = BindableProperty.Create(nameTextSize, typeof(double), typeof(EntryCentered), 12.0); //, BindingMode.TwoWay
        private bool _isRendererSet;

        public double TextSize
        {
            get { return (double)GetValue(TextSizeProperty); }
            set { SetValue(TextSizeProperty, value); }
        }

        ////-------------------------------------------------------------
        //// FontSize
        ////-------------------------------------------------------------
        ///// <summary>
        ///// Use this instead of FontSize or renderer won't work as expected
        ///// </summary>
        //private const string nameFontSize = "FontSize";
        //public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize, typeof(double), typeof(EntryCentered), FontSizes.Medium); //, BindingMode.TwoWay
        //public new double FontSize
        //{
        //    get { return (double)GetValue(FontSizeProperty); }
        //    set { SetValue(FontSizeProperty, value); }
        //}


        
        public void UpdateControl()
        {
            RendererNeedUpdate?.Invoke(this, null);
        }

        public void Set(string text)
        {
            Text = "";
            Text = text;
            //UpdateControl();
            if (Handler!=null)
                RendererNeedUpdate?.Invoke(this, null);
        }

        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        
        {
            base.OnPropertyChanged(propertyName);


            if (propertyName == "Renderer")
            {
                _isRendererSet = !_isRendererSet;
                if (!_isRendererSet)
                {
                    Dispose();
                    Debug.WriteLine("EntryCentered disposed!");
                }
            }

            switch (propertyName)
            {
                //Ask renderer to redraw stuff using new values
                //case nameLetterSpacing:
                case nameMaxLines:
                case namePlaceholderColor:
                //case nameLineSpacing:
                case nameCentered:
                    UpdateControl();
                    break;


                case nameTextSize:
                case namePlaceholderTextSize:
                    FontSize = TextSize;
                    //PlaceholderTextSize = FontSize;
                    UpdateControl();
                    break;

            }

        }



    }
}
