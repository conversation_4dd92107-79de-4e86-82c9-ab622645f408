﻿<?xml version="1.0" encoding="UTF-8" ?>
<AbsoluteLayout
    x:Class="AppoMobi.Xam.GsButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    BackgroundColor="Transparent"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="Start"
    WidthRequest="270">

    <!--  layer frame outline  -->

    <!--  layer gradient  -->
    <svg:GradientBox
        x:Name="cGradient"
        Margin="0.5"
        AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
        AbsoluteLayout.LayoutFlags="All" />

    <gestures:LegacyGesturesFrame
        x:Name="cFrame"
        AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
        AbsoluteLayout.LayoutFlags="All"
        BackgroundColor="Transparent"
        Down="CGrid_OnDown"
        HorizontalOptions="FillAndExpand"
        Tapped="CFrame_OnTapped"
        Up="CGrid_OnUp"
        VerticalOptions="FillAndExpand" />

    <!--  caption  -->
    <Label
        x:Name="cLabel"
        Margin="0,0,0,2"
        AbsoluteLayout.LayoutBounds="0.5, 0.5, -1, -1"
        AbsoluteLayout.LayoutFlags="PositionProportional"
        FontSize="28"
        HorizontalOptions="Fill"
        HorizontalTextAlignment="Center"
        InputTransparent="True"
        TextColor="{x:Static xam:TextColors.StandartOnDark}"
        VerticalOptions="CenterAndExpand"
        VerticalTextAlignment="Center" />

</AbsoluteLayout>