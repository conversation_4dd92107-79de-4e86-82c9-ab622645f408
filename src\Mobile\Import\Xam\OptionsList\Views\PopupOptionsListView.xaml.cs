﻿using System;
using System.Collections.Generic;
using System.Linq;
using AppoMobi.Touch;



namespace AppoMobi.Xam
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PopupOptionsListView
    {

        public List<OptionsListItem> MenuList;


        
        public PopupOptionsListView(Action<int> callback, string title, List<OptionsListItem> list, string cancel, bool disableBackgroundclick = false, bool quitOnBackPressed = false)
        
        {
            InitializeComponent();

            MenuList = list;

            DisableBackgroundClick = disableBackgroundclick;
            QuitOnBackPressed = quitOnBackPressed;

            Message = title;
            CallbackList = callback;

            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                //cTitle.TranslationY = 1;
            }


            
            //submit =)
            BindingContext = this;

            if (list!=null && list.Any())
            {

                foreach (var item in list)
                {


                }

            }
            else
                Grid_OnDown(null, null);
            
            DataStack.ItemsSource = MenuList;
        }

        
        // Invoced when background is clicked
        protected override bool OnBackgroundClicked()
        
        {
            return true;

            if (DisableBackgroundClick)
                // Return default value - CloseWhenBackgroundIsClicked
                return base.OnBackgroundClicked();

            return false;
        }


        private bool _tapped = false;
        //-------------------------------------------------------------
        private async void OnTapped_Item(object sender, TapEventArgs e)
        //-------------------------------------------------------------
        {
            if (_tapped) return;
            _tapped = true;

            _gridOnDownFired = false;

            var item = (OptionsListItem)e.Sender.TappedCommandParameter;
            if (item == null) return;

            var index = MenuList.IndexOf(item);

            await DismissDialog(index);
            _tapped = false;
        }

        private bool _gridOnDownLock;
        private bool _gridOnDownFired;
        private bool _gridOnDownTracked;


        
        private void Grid_OnDown(object? sender, TapEventArgs tapEventArgs)
        
	    {
	        if (_gridOnDownLock) return;
	        _gridOnDownLock = true;

            _gridOnDownFired = true;
	        if (!_gridOnDownTracked)
	        {
	            _gridOnDownTracked = true;
	            Device.StartTimer(TimeSpan.FromMilliseconds(250),  () =>
	            {
	                if (_gridOnDownFired)
	                {
	                    try
	                    {
	                        DismissDialog(-1);
                        }
                        catch (Exception exception)
	                    {
	                    }
                    }
                    else
	                    _gridOnDownTracked = false;
	                return false;
	            });
	        }
            _gridOnDownLock = false;            
	    }

        public bool CloseOnChildTapped;

        private async void OnChildTapped(object sender, EventArgs e)
        {
            if (CloseOnChildTapped)
            {
                //var item = (OptionsListItem)e.Sender.TappedCommandParameter;
                //if (item == null) return;

                //var index = MenuList.IndexOf(item);

                await DismissDialog(-1);
            }

        }

        private void CardInterface_OnDown(object sender, XamTapEventArgs xamTapEventArgs)
        {
            _gridOnDownFired = false;
        }
    }


   

}