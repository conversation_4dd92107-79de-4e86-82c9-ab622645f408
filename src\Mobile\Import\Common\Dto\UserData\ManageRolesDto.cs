﻿using AppoMobi.Framework.Api;

namespace AppoMobi.Common.Dto.UserData
{
    /// Id - key of content to be blocked
    /// </summary>
    public class ManageRolesDto : WithIdDto
    {
        /// <summary>
        /// As title
        /// </summary>
        public string SelectRole { get; set; }


        public string SelectLanguages { get; set; }
        /// <summary>
        /// Reset all roles so we have to choose from start
        /// </summary>
        public bool Reset { get; set; }

    }
}