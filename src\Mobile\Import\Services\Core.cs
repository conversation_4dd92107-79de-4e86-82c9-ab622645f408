﻿using AppoMobi.Auth;
using AppoMobi.Common;
using AppoMobi.Common.Dto;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Nifty;
using AppoMobi.Pages;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;
using AppoMobi.Mobile.Views.Popups;
using FFImageLoading;
using Newtonsoft.Json;
using System.Globalization;
using System.Reflection;

namespace AppoMobi
{




    //===================================================================
    public partial class Core : CoreBase
    //===================================================================
    {

        #region TENANT

        //-----------------------------------------------------------------
        public List<CMyListItem> ContactList { get; set; } = new List<CMyListItem>();
        //-----------------------------------------------------------------

        //-----------------------------------------------------------------
        public void BuildContactsList()
        //-----------------------------------------------------------------
        {
            ContactList.Clear();
            var prms = MyCompany.ContactUsTel;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "tel",
                    FontIcon = FontIcons.fa_phone_square,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsWebsite;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "www",
                    FontIcon = FontIcons.fa_globe,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsEmail;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "mail",
                    FontIcon = FontIcons.fa_envelope,
                    FontOverride = "FaSolid",
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsFacebook;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "facebook",
                    FontIcon = FontIcons.fa_facebook,
                    FontOverride = "FaBrands",
                    Desc = ResStrings.Facebook,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsVk;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "vk",
                    FontIcon = FontIcons.fa_vk,
                    FontOverride = "FaBrands",
                    Desc = ResStrings.VK,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsInstagram;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "instagram",
                    FontIcon = FontIcons.fa_instagram,
                    FontOverride = "FaBrands",
                    Desc = ResStrings.Instagram,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsAddress;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "map",
                    FontIcon = FontIcons.fa_map_marked_alt,
                    Parameters = prms,
                    Desc = prms,
                    Selected = false
                });
            }

            if (Core.Current.MyCompany.MapX > 0)
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "navi",
                    FontIcon = FontIcons.fa_route,
                    Parameters = Core.Current.MyCompany.ContactUsAddress,
                    Desc = ResStrings.ButtonNavigate,
                    Selected = false
                });

            }


        }

        //-----------------------------------------------------------------
        public async void ActivateContact(string tag, string Params)
        //-----------------------------------------------------------------
        {
            switch (tag)
            {
            case "tel":
            Dial(Params);
            break;

            case "www":
            await PushInstance(typeof(WebpageCustomized), Params, MyCompany.Name, MyCompany.ContactUsTel);
            break;

            case "facebook":
            if (!Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebookNumeric))
            {
                Core.Native.OpenUrl(@"https://www.facebook.com/" + Core.Current.MyCompany.ContactUsFacebook);
            }
            break;
            case "vk": //todo
                       //if (!Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebook))
                       //{
            Native.OpenUrl(@"https://www.vk.com/" + MyCompany.ContactUsVk);
            //}
            break;
            case "instagram":
            if (!Native.OpenInAppInstagram(MyCompany.ContactUsInstagram))
            {
                //else open in our browser:
                await PushInstance(typeof(WebpageCustomized), @"https://www.instagram.com/" + MyCompany.ContactUsInstagram, Core.Current.MyCompany.Name + " " + ResStrings.Instagram, "");
            }
            break;

            case "mail":
            Core.Mail(Params);
            break;

            case "map":
            Toast.ShortMessage("В разработке");
            //                    await PushInstance(typeof(PageWeOnMap));
            break;

            case "navi":
            Toast.ShortMessage("В разработке");
            //await Navigate(MyCompany.FullName, MyCompany.MapX, MyCompany.MapY);
            break;

            }

        }



        #endregion


        public TenantInfoDTO Info { get; set; }

        public CompanyInfoDTO MyCompany

        {
            get
            {
                return Info.Companies.First();
            }
        }















        public NiftyObservableCollection<TaggedTitle> Themes = new NiftyObservableCollection<TaggedTitle>();
        public NiftyObservableCollection<TaggedTitle> Languages = new NiftyObservableCollection<TaggedTitle>();


        public async void AskForRating()

        {
            var totalCrashes = Settings.Current.TotalCrashes;
            var alreadyAsked = Settings.Current.AskedForRating;

            //log already asked
            Settings.Current.AskedForRating = true;

            if (totalCrashes > 0)
                return;

            if (IsAndroid)
            {
                if (alreadyAsked)
                    return;

                //show prompt
                var question = string.Format(ResStrings.AskForRating_Question, ResStrings.OwnerTitle);
                var ok = await ShowPrompt(question);
                if (!ok)
                {
                    await Alert(ResStrings.AskForRating_ThanksForNegative);
                    return;
                }
                await Alert(ResStrings.AskForRating_GooglePlay);
            }

            Core.Native.AskForRating();
        }


        public bool SetTheme(string tag)

        {
            if (string.IsNullOrEmpty(tag)) return false;
            try
            {
                var maybeSelected = Themes.FirstOrDefault(x => x.Tag == tag);
                if (maybeSelected == null)
                {
                    return false;
                    Themes.Selected = Themes[0]; //first Dark selected
                }
                else
                {
                    Themes.Selected = maybeSelected;
                    Settings.Current.VisualTheme = tag;
                    return true;
                }
            }
            catch (Exception e)
            {
                App.Logger.Error("SetTheme", e);
                return false;
            }
        }

        public static void Dial(string number)
        {
            try
            {
                PhoneDialer.Open(number);
            }
            catch (ArgumentNullException anEx)
            {
                // Number was null or white space
            }
            catch (FeatureNotSupportedException ex)
            {
                // Phone Dialer is not supported on this device.
            }
            catch (Exception ex)
            {
                // Other error has occurred.
            }
        }


        /// <summary>
        /// Shows help popup dialog
        /// </summary>
        /// <param name="text"></param>

        public static void ShowHelp(string text)

        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                var dialog = new PopupResult(text, ResStrings.ButtonOk);
                await dialog.ShowAsync(false);
            });
        }

        #region LOCALIZATION


        public void SetLanguage(string lang)
        {
            ResStrings.Culture = CultureInfo.CreateSpecificCulture(lang);
            Thread.CurrentThread.CurrentCulture = ResStrings.Culture;
            Thread.CurrentThread.CurrentUICulture = ResStrings.Culture;
            Settings.Current.DeviceLang = lang;
            Settings.Current.SelectedLang = lang;
        }


        #endregion



        public void SetupLanguages()

        {
            if (Settings.Current.FirstStart)
            {
                if (!Info.Languages.Contains(Settings.Current.DeviceLang))
                {
                    var d = Info.Languages.First();
                    SetLanguage(d);
                }
            }

            SetLanguage(Settings.Current.SelectedLang);
        }



        public new static Task RunOnMainThreadAsync(Task action)

        {
            var tcs = new TaskCompletionSource<object>();
            MainThread.BeginInvokeOnMainThread(
                async () =>
                {
                    try
                    {
                        await action;
                        tcs.SetResult(null);
                    }
                    catch (Exception e)
                    {
                        tcs.SetException(e);
                        App.Logger.Error("RunOnMainThreadAsync", e);

                    }
                });
            return tcs.Task;
        }

        public NiftyObservableCollection<TSlide> TutorialSlides = new NiftyObservableCollection<TSlide>();


        /// <summary>
        /// ex get string for 'fa-arrow-square-down'
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>

        public static string GetFontAwesomeContent(string tag) // ex:fa-arrow-square-down

        {
            var assembly = typeof(Core).GetTypeInfo().Assembly;
            //var css = StyleSheet.FromAssemblyResource(assembly, "CSS.fontawesome.css");
            Stream stream = assembly.GetManifestResourceStream(AssemblyName + ".Resources.fontawesome.min.css");

            if (stream == null) return null;

            string text = "";
            using (var reader = new System.IO.StreamReader(stream))
            {
                text = reader.ReadToEnd();
            }

            //todo parse
            int first = text.IndexOf(tag) + tag.Length;
            if (first < 0) return null;

            int last = text.IndexOf("}", first);
            if (last < 0) return null;

            // fa-crosshairs:before{content:"\f05b"}

            string str2 = text.Substring(first, last - first);

            var hex = str2.Trim().Replace(" ", "").Replace(":before{content:\"\\", "").Replace("\"", "");

            int code = int.Parse(hex, System.Globalization.NumberStyles.HexNumber);
            string ret = char.ConvertFromUtf32(code);

            return ret;
        }



        private WaitingPopup WaitingPopup { get; set; }
        //------------------------------------------------------------
        public async Task ShowWaitingPopup(string message = null)
        //------------------------------------------------------------
        {
            try
            {
                var Message = ResStrings.PleaseWait;
                if (message == null)
                    message = Message;
                WaitingPopup = new WaitingPopup(message);
                WaitingPopup.Open();
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);
            }
        }

        public void CloseWaitingPopup()
        {
            PopupPage.CloseAllPopups();
        }

        // &#10; - line break in xaml string
        public bool HasTabs { get; set; }

        public static ImageSource ImageFromResources(string subfolder, string filename)
        {


            //string folderPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            //string assemblyPath = Path.Combine(folderPath, new AssemblyName(Core.AssemblyName).Name + ".dll");
            //if (!File.Exists(assemblyPath)) return null;
            //Assembly assembly = Assembly.LoadFrom(assemblyPath);

            //var ass = assembly.GetName().Name;

            var generatedFilename = Core.AssemblyName + $".Images.{subfolder}.{filename}";

            //var availableRes = assembly.GetManifestResourceNames();


            var ret = ImageSource.FromResource(generatedFilename);

            return ret;
        }

        private class NavPage
        {
            public string Id { get; set; }
            public NavigationPage Page { get; set; }
        }

        private List<NavPage> Pages { get; } = new List<NavPage>();

        public double TweakSplashLogoHeight { get; set; }

        private static Core dataservice;
        public static Core Current
        {
            get { return dataservice ?? (dataservice = new Core()); }
        }

        public double ProductsExportBuild { get; set; }
        public double ProdCatsExportBuild { get; set; }
        public double SalonsExportBuild { get; set; }
        public double RegionsExportBuild { get; set; }
        public double NewsExportBuild { get; set; }



        public string LatestBuild { get; set; }
        public string MyBuild { get; set; }



        public static bool ArrowAsImage { get; set; }
        public static string AssemblyName { get; set; }
        public static bool iBoldFonts { get; set; }
        public static Size ScreenSize { get; set; }
        public static Size ScreenSizePixels { get; set; }
        public static bool HideStatusBar { get; set; }


        public static int AndroidAPI { get; set; }

        //public static double StatusBarHeight { get; set; }
        public static double StatusBarHeightRequest { get; set; }
        public static double NavBarHeight { get; set; }

        public static float DisplayDensity
        {
            get
            {
                return (float)Super.Screen.Density;
            }
        }

        public static bool IsPopupOn { get; set; }

        protected App _app
        {
            get; set;
        }

        public void Init(App app)
        {
            _app = app;

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                IsIOS = true;
            else
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                IsAndroid = true;

            var assembly = typeof(App).GetTypeInfo().Assembly;

            AssemblyName = assembly.GetName().Name;
        }



        private NavigationPage navPage { get; set; }

        private INavigation _Navigation;
        public INavigation Navigation
        {
            get { return _Navigation; }
            set
            {
                if (_Navigation != value)
                {
                    _Navigation = value;
                    //OnPropertyChanged();
                }
            }
        }




        //    private readonly IVersionsApi _apiVersions;


        public Core()
        {

            //navPage = new NavigationPage(new ContentPage());
            //Navigation = navPage.Navigation;

            // On App resume
            App.Instance.Messager.Subscribe<string>(this, "On", async (sender, arg) =>
            {
                // do something whenever the "Login" message is sent
                // using the 'arg' parameter which is a string

                if (arg == "Resume")
                {
                    // todo
                    //background ios fetch

                    OnAppResume();

                    return;
                }
                if (arg == "Sleep")
                {
                    //await AppUser.Current.SyncFavoritesOut(); //try send shit out
                    return;
                }

            });

            App.Instance.Messager.Subscribe<string>(this, "SelectPage", async (sender, arg) =>
            {

                if (arg == "prods")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_PRODS);
                    //                    await Globals.Values.TabsMain.Navigation.PopToRootAsync();
                    return;
                }
                else
                if (arg == "fav")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_FAV);
                    //                  await Globals.Values.TabsMain.Navigation.PopToRootAsync();
                    return;
                }
                else
                if (arg == "news")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_HOME);
                    //                await Globals.Values.TabsMain.Navigation.PopToRootAsync();
                    return;
                }
                else
                if (arg == "cart")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_CART);
                }
                else
                if (arg == "salons")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_LIST);
                }
                else
                if (arg == "aboutus")
                {
                    Globals.Values.AppMenu.ClickMenuItem(Globals.CONST_MENU_US);
                }

                return;
            });

            App.Instance.Messager.Subscribe<string>(this, "MainNavigationReady", async (sender, arg) =>
            {
                await ProcessDelayedPushMessages();
            });

            //end of constructor
        }



        private double _myProgress;
        public double MyProgress
        {
            get { return _myProgress; }
            set
            {
                if (_myProgress != value)
                {
                    _myProgress = value;
                    OnPropertyChanged();
                }
            }
        }

        private string _SplashLogoImage;
        public string SplashLogoImage
        {
            get { return _SplashLogoImage; }
            set
            {
                if (_SplashLogoImage != value)
                {
                    _SplashLogoImage = value;
                    OnPropertyChanged();
                }
            }
        }
        private Color _SplashColor;
        public Color SplashColor
        {
            get { return _SplashColor; }
            set
            {
                if (_SplashColor != value)
                {
                    _SplashColor = value;
                    OnPropertyChanged();
                }
            }
        }

        private Color _SplashColor2;
        public Color SplashColor2
        {
            get { return _SplashColor2; }
            set
            {
                if (_SplashColor2 != value)
                {
                    _SplashColor2 = value;
                    OnPropertyChanged();
                }
            }
        }



        public string Log { get; set; }


        public void ClearLog()

        {
            Log = "";
        }

        public void WriteLogLine(string line, params object[] args)

        {
            Log += string.Format(line, args) + "\r\n";
        }


        public void InitPush()

        {

        }



        public List<string> Modules { get; set; } = new List<string>();


        public async Task Alert(string message)

        {
            bool closed = false;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var dialog = new NiftyPopupDialog(message, ResStrings.ButtonOk);
                //dialog.DisableBackgroundClick();
                await dialog.ShowAsync(false);
                closed = true;
            });
            while (!closed)
            {
                await Task.Delay(50);
            }
        }

        public async Task<bool> ShowPrompt(string text, string yes = null, string no = null)

        {
            var dialog = new NiftyPopupDialog(text, yes, no);
            dialog.DisableBackgroundClick();
            if (!await dialog.ShowAsync())
            {
                return false;
            }
            return true;
        }




        private async void OnSystemMessageRecieved(string name, string param)

        {
            await Alert($"System Message Recieved {name} {param}");
            App.Instance.Messager.All("SERVER_MESSAGE", name + " " + param);
        }

        private async void OnRefreshMessageRecieved(string param)

        {
            //await Alert($"Refresh Message Recieved {param}");
            App.Instance.Messager.All("SERVER_REFRESH", param);
        }


        public static async Task PreloadSystemImages(params string[] urls)

        {
            var time = new TimeSpan(365, 0, 0);
            foreach (var url in urls)
            {
                if (!string.IsNullOrEmpty(url) && ImageService.Instance != null)
                    await ImageService.Instance.LoadUrl(url, time).PreloadAsync(ImageService.Instance).ConfigureAwait(false);
            }
        }



        public static string GetLanguageName(string twoDigitsCode)

        {
            var culture = CultureInfo.CreateSpecificCulture(twoDigitsCode);
            return culture.NativeName;
        }



        public async Task<string> PresentSelectionList(Page navigationPage, string title, List<string> list)

        {
            try
            {
                var buidList = new List<KeyValuePair<string, string>>();
                foreach (var item in list)
                {
                    buidList.Add(new KeyValuePair<string, string>(item, item));
                }
                var listOptions = buidList.Select(x => x.Key).ToArray();
                var action = await navigationPage.DisplayActionSheet(title, ResStrings.ButtonCancel, null, listOptions);

                if (action == ResStrings.ButtonCancel) return null;

                var selected = buidList.FirstOrDefault(x => x.Key == action);

                return selected.Value;
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);

            }
            return null;
        }

        public class TabsFavoritesItem
        {
            public string Tag { get; set; } //for saving to settings
            public string Display { get; set; }//localized display
            public bool Value { get; set; } //value
        }


        public List<bool> GetTabsVisibility()

        {
            var visibility = new List<bool>();
            var maybeJson = Settings.Current.FavsTabs;
            if (maybeJson != null)
            {
                var maybeList = JsonConvert.DeserializeObject<List<bool>>(maybeJson);
                if (maybeList != null)
                {
                    visibility = maybeList;
                }
            }

            if (visibility.Count == 0 && TabsAndMenu.TabsList.Count > 0)
            {
                //create initial list if non-existing
                foreach (var tab in TabsAndMenu.TabsList)
                {
                    if (!string.IsNullOrEmpty(tab.Platform))
                    {
                        if (!tab.Platform.Contains(DeviceInfo.Platform.ToString()))
                            continue;
                    }
                    visibility.Add(!tab.HiddenDefault);
                }
                //save to settings
                var json = JsonConvert.SerializeObject(visibility);
                Settings.Current.FavsTabs = json;
            }

            return visibility;
        }




        public async Task<bool> PresentThemesSelectionList()

        {

            //output list
            var buidList = new Dictionary<string, bool>();
            foreach (var item in Themes)
            {
                var selected = item.Tag == Settings.Current.VisualTheme;
                buidList.Add(item.Title, selected);
            }

            //show view
            bool closed = false;
            Dictionary<string, bool> retList = null;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var dialog = new MultiSelectionList(ResStrings.Settings_SelectTheme, buidList,
                    ResStrings.ButtonCancel, false);
                retList = await dialog.ShowAsync(false);
                closed = true;
            });
            while (!closed)
            {
                await Task.Delay(50);
            }

            KeyValuePair<string, bool> newSelected = new();
            foreach (var item in retList)
            {
                if (item.Value)
                {
                    newSelected = item;
                    break;
                }
            }

            var selectedTheme = Themes.FirstOrDefault(x => x.Title == newSelected.Key);
            if (selectedTheme == null)
            {
                //unknown error
                return false;
            }

            if (selectedTheme.Tag != Settings.Current.VisualTheme)
            {
                //modify settings
                SetTheme(selectedTheme.Tag);
                return true;
            }

            return false;
        }


        public async Task<bool> PresentLanguagesSelectionList()

        {

            if (Info == null
                || Info.Languages == null
                || Info.Languages.Count() < 2) return false;

            if (Languages.Count < 1)
            {
                foreach (var lang in Info.Languages)
                {
                    var culture = CultureInfo.CreateSpecificCulture(lang);
                    var title = culture.NativeName.ToTitleCase().ToTitleCase("(");
                    Languages.Add(new TaggedTitle
                    {
                        Tag = lang,
                        Title = title
                    });
                }
            }

            //output list
            var buidList = new Dictionary<string, bool>();
            foreach (var item in Languages)
            {
                var selected = item.Tag == Settings.Current.SelectedLang;
                buidList.Add(item.Title, selected);
            }

            //show view
            bool closed = false;
            Dictionary<string, bool> retList = null;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var dialog = new MultiSelectionList(ResStrings.Settings_SelectLanguage, buidList,
                    ResStrings.ButtonCancel, false);
                retList = await dialog.ShowAsync(false);
                closed = true;
            });
            while (!closed)
            {
                await Task.Delay(50);
            }

            KeyValuePair<string, bool> newSelected = new();
            foreach (var item in retList)
            {
                if (item.Value)
                {
                    newSelected = item;
                    break;
                }
            }

            var selectedItem = Languages.FirstOrDefault(x => x.Title == newSelected.Key);
            if (selectedItem == null)
            {
                //unknown error
                return false;
            }

            if (selectedItem.Tag != Settings.Current.SelectedLang)
            {
                //modify settings
                SetLanguage(selectedItem.Tag);
                return true;
            }

            return false;
        }


        //UI thread only!
        public async Task<bool> PresentFavTabsSelectionList()
        {
            //try get options from settings
            var visibility = GetTabsVisibility();

            //convert from all tabs items
            var index = 0;
            var newList = new List<TabsFavoritesItem>();
            foreach (var item in TabsAndMenu.TabsList)
            {
                if (!string.IsNullOrEmpty(item.Platform))
                {
                    if (!item.Platform.Contains(DeviceInfo.Platform.ToString()))
                        continue;
                }

                var newItem =
                    new TabsFavoritesItem
                    {
                        Tag = item.Id,//"time",
                        Display = item.NameInHeader,
                        Value = true
                    };
                if (index < visibility.Count)
                {
                    newItem.Value = visibility[index];
                }
                newList.Add(newItem);
                index++;
            }

            //output list
            var buidList = new Dictionary<string, bool>();
            foreach (var item in newList)
            {
                buidList.Add(item.Display, item.Value);
            }

            Dictionary<string, bool> retList = null;
            var dialog = new MultiSelectionList(ResStrings.Settings_ChooseYourTabsMinMax, buidList,
                ResStrings.ButtonCancel, true, 2, 5);
            retList = await dialog.ShowAsync(false);

            //todo process retList
            var newVisibility = new List<bool>();

            //todo compare with visibility
            bool changed = visibility.Count == 0;

            index = 0;
            foreach (var item in retList)
            {
                newVisibility.Add(item.Value);

                if (index < visibility.Count)
                {
                    if (visibility[index] != item.Value)
                        changed = true;
                }

                index++;
            }

            if (visibility.Count != newVisibility.Count)
            {
                changed = true;
            }

            if (changed)
            {
                //save to settings
                var json = JsonConvert.SerializeObject(newVisibility);
                Settings.Current.FavsTabs = json;

                //todo update UI
                if (visibility.Count != 0)
                {
                    //update
                    return true;
                }
            }
            return false;
        }






        public async Task<bool> Bootstrap()

        {

            MyProgress = 0.25;

            try
            {
                await Task.Delay(50);
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);

            }

            Modules = TabsAndMenu.ModulesList;

            //AuthModel.Instance.LoginOffline();
            //await AuthModel.Instance.UpdateRemoteUserInfo();

            int errors = 0;
            int res = -1;

            double countLoadingParts = TabsAndMenu.ModulesList.Count + 1; // 1 is for versions
            double loadingPart = (1 - 0.25) / countLoadingParts;

            var listErrors = new List<string>();

            //todo maybe inc versions as modul to be able to create offline apps with versions disabled
            //VERSIONS

            //AppContentVersionsStore.Instance.SaveItemsToDevice();
            //WriteLogLine("loaded {0} versions", res);            
            MyProgress += loadingPart;

            //get tenant info
            Info = await AppoDataStore.Instance.GetInfo();
            if (Info == null)
                errors++;
            else
            {
                SetupLanguages();

                //setup UI colors etc
                //TenantOptions.Init();

                SplashLogoImage = Info.Logo.GetThumbnailForDTO("large", TenantOptions.TenantKey);

                //preload images
                var pattern = Info.Pattern.GetThumbnailForDTO("normal", TenantOptions.TenantKey);
                var wallpaper = Info.Pattern.GetThumbnailForDTO("large", TenantOptions.TenantKey);
                //var logo =Info.Pattern.GetThumbnailForDTO("normal", TenantOptions.TenantKey);
                await PreloadSystemImages(pattern, wallpaper);

                TabsAndMenu.Init();
            }


            //100%
            MyProgress = 1;
            //        await Task.Delay(10); //update ui


            //todo preload images of interface

            WriteLogLine("Errors: {0}.", errors);

            // await NiftyAlert.ShowAsync(Log);

            //errors = 1;

            if (errors > 0)
            {
#if DEBUG
                await Alert(listErrors.AsString());
#endif


                return false;

            }

            // await SalonManager.Current.RefreshDataAsync();
            //  CentersDataStore.Instance.GetItemsFromBuffer("Name");

            Settings.Current.FirstStart = false;

            return true;
        }


        public bool Initialized { get; set; }



        public void Reconnect() //todo change it to something else

        {
            Initialized = false;

            //this is called only on reconnect button if connect fails
            //or by resume task after

            //HelloServer();

            Device.StartTimer(TimeSpan.FromSeconds(0.5), () =>
            {
                App.Instance.Messager.All("Reload_News", "");
                // Don't repeat the timer 
                return false;
            });
            Device.StartTimer(TimeSpan.FromSeconds(1.5), () =>
            {
                App.Instance.Messager.All("LoadSalons", "");
                // Don't repeat the timer 
                return false;
            });
            Device.StartTimer(TimeSpan.FromSeconds(2.0), () =>
            {
                App.Instance.Messager.All("Reload_Products", "");
                // Don't repeat the timer 
                return false;
            });
        }


        public async Task ProcessDelayedPushMessages()

        {

            var pushedContent = Settings.Current.PushCommand;
            if (!string.IsNullOrEmpty(pushedContent))
            {
                var pushedParam = Settings.Current.PushCommandParam;
                Device.StartTimer(TimeSpan.FromMilliseconds(1000), () =>
                {
                    App.Instance.Messager.All("PushContentSent(pushedContent", pushedParam);
                    return false;
                });
            }


            //App.Instance.Messager.PushContentSent("promo", "150aac89-9d39-48e9-a651-0cef11db981d");

        }


        private async void OnAppResume()

        {
            //show check popup
            //AuthModel.Instance.LoginOffline();

            //MyProgress = 0;
            // await PopupNavigation.Instance.PushAsync(new CheckUpdates(), false);

            //try get new versions from inet
            //var vers = await PingServerGetVersions(true);
            //if (vers != null)
            //{
            //    AppContentVersionsStore.Instance.Items.Clear();
            //    AppContentVersionsStore.Instance.Items.AddRange(vers);
            //    //compare
            //    AppContentVersionsStore.Instance.LoadBufferedFromDevice();
            //    var match = AppContentVersionsStore.Instance.CompareItemsWithBuffered();
            //    if (!match)
            //    {
            //        //ignore for this app
            //        //restart is manual
            //        //App.Instance.Messager.All( "NeedRestart", "Now");
            //        return;
            //    }
            //}


            AppUser.Current.LoadFavs(); //why not
            //await HelloServer(); //test if something changed

            //await PopupNavigation.Instance.PopAllAsync();       
        }

        protected static bool lockPushInstance { get; set; }

        public static async Task PushInstance(INavigation navigation, Page page)
        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {

                await PushPage(page, navigation);
            }
            catch (Exception e)
            {
                // Console.WriteLine(e.Message);
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", page.GetType().Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }




        /// <summary>
        /// Pushes a page and sets a Pushed event handler for an EnhancedPage so it could show back button etc.. 
        /// Try use PushInstance instead, this one is used by system.
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>

        public static async Task PushPage(Page page, INavigation navigation, bool modal = false)

        {
            //var navi = new NavigationPage(page);
            //NavigationPage.SetHasNavigationBar(navi, false);
            //NavigationPage.SetBackButtonTitle(navi, ResStrings.GoBack);
            try
            {
                var enha = (EnhancedPage)page;
                bool anim = true;

                if (Core.IsAndroid && Core.AndroidAPI < 21)
                    anim = false;

                if (!modal)
                    await NavigationService.PushAsync(navigation, page, anim);
                else
                {
                    ((EnhancedPage)page).IsModal = true;
                    await NavigationService.PushModalAsync(navigation, page, anim);
                }



            }
            catch (Exception e)
            {
                App.Logger.Error("PushPage", e);
            }

        }



        public async Task PushInstanceModal(Type type, EventHandler<NavigationEventArgs> popping, params object[] args)

        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {
                var newPage = (Page)Activator.CreateInstance(type, args);

                if (newPage is PageEnhancedNav && popping != null)
                {
                    ((PageEnhancedNav)newPage).Popping += popping;
                }

                await PushPage(newPage, Navigation, true);
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);

                // Console.WriteLine(e.Message);                
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", type.Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }


        /// <summary>
        /// Creates an instance of a page with Activator and pushes it setting a Pushed event handler for an EnhancedPage so it could show back button etc..
        /// With antispam of 1 secs.
        /// </summary>
        /// <param name="type"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static async Task PushInstanceModal(INavigation navigation, Type type, params object[] args)
        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {
                var page = (Page)Activator.CreateInstance(type, args);
                await PushPage(page, navigation, true);
            }
            catch (Exception e)
            {
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                // Console.WriteLine(e.Message);                
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", type.Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }

        public static async Task PushInstanceModal(INavigation navigation, Page page)

        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {
                await PushPage(page, navigation, true);
            }
            catch (Exception e)
            {
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                // Console.WriteLine(e.Message);                
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", page.GetType().Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }



        public async Task PushInstanceModal(Type type, params object[] args)

        {
            if (Navigation == null)
            {
                Toast.ShortMessage("Error:  Navigation not set.");
                return;
            }
            await PushInstanceModal(Navigation, type, args);
        }


        public async Task PushInstance(Type type, params object[] args)

        {
            if (Navigation == null)
            {
                Toast.ShortMessage("Error:  Navigation not set.");
                return;
            }
            await PushInstance(Navigation, type, args);
        }


        public static async Task PushInstance2(INavigation navigation, Type type, params object[] args)
        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(0.5), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            var newPage = (Page)Activator.CreateInstance(type, args);

            await PushPage(newPage, navigation);

            App.Instance.Messager.All("PagePushed", type.Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }



        /// <summary>
        /// Creates an instance of a page with Activator and pushes it setting a Pushed event handler for an EnhancedPage so it could show back button etc..
        /// With antispam of 1 secs.
        /// </summary>
        /// <param name="type"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static async Task PushInstance(INavigation navigation, Type type, params object[] args)
        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {
                var page = (Page)Activator.CreateInstance(type, args);
                await PushPage(page, navigation);
            }
            catch (Exception e)
            {
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                // Console.WriteLine(e.Message);
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", type.Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }



        public static async Task PushInstancePseudoTab(INavigation navigation, Type type, params object[] args)

        {
            if (lockPushInstance)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    lockPushInstance = false; //unlock after 2 secs
                    return false;// Don't repeat the timer 
                });
                return;
            }
            lockPushInstance = true; //lock me

            try
            {
                var page = (Page)Activator.CreateInstance(type, args);

                if (page is IPageEnhancedNav)
                {
                    ((IPageEnhancedNav)page).PseudoTab = true;
                }

                await PushPage(page, navigation);
            }
            catch (Exception e)
            {
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                // Console.WriteLine(e.Message);
                Toast.ShortMessage(e.ToString());
            }

            App.Instance.Messager.All("PagePushed", type.Name);

            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                lockPushInstance = false; //unlock after 2 secs
                return false;// Don't repeat the timer 
            });
        }



        //
        public enum AndroidAPICodes
        {
            //
            // Summary:
            //     October 2008: The original, first, version of Android. Yay!
            Base = 1,
            //
            // Summary:
            //     February 2009: First Android update, officially called 1.1.
            Base11 = 2,
            //
            // Summary:
            //     May 2009: Android 1.5.
            Cupcake = 3,
            //
            // Summary:
            //     September 2009: Android 1.6. They must explicitly request the Android.Manifest.Permission.WriteExternalStorage
            //     permission to be able to modify the contents of the SD card. (Apps targeting
            //     earlier versions will always request the permission.) They must explicitly request
            //     the Android.Manifest.Permission.ReadPhoneState permission to be able to be able
            //     to retrieve phone state info. (Apps targeting earlier versions will always request
            //     the permission.) They are assumed to support different screen densities and sizes.
            //     (Apps targeting earlier versions are assumed to only support medium density normal
            //     size screens unless otherwise indicated). They can still explicitly specify screen
            //     support either way with the supports-screens manifest tag. Android.Widget.TabHost
            //     will use the new dark tab background design.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            Donut = 4,
            //
            // Summary:
            //     November 2009: Android 2.0 The Android.App.Service.OnStartCommand(Android.Content.Intent,
            //     Android.App.StartCommandFlags, Android.App.StartCommandFlags) function will return
            //     the new Android.App.StartCommandResult.Sticky behavior instead of the old compatibility
            //     Android.App.StartCommandResult.StickyCompatibility. The Android.App.Activity
            //     class will now execute back key presses on the key up instead of key down, to
            //     be able to detect canceled presses from virtual keys. The Android.Widget.TabWidget
            //     class will use a new color scheme for tabs. In the new scheme, the foreground
            //     tab has a medium gray background the background tabs have a dark gray background.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            Eclair = 5,
            //
            // Summary:
            //     December 2009: Android 2.0.1
            Eclair01 = 6,
            //
            // Summary:
            //     January 2010: Android 2.1
            EclairMr1 = 7,
            //
            // Summary:
            //     June 2010: Android 2.2
            Froyo = 8,
            //
            // Summary:
            //     November 2010: Android 2.3 The application's notification icons will be shown
            //     on the new dark status bar background, so must be visible in this situation.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            Gingerbread = 9,
            //
            // Summary:
            //     February 2011: Android 2.3.3.
            GingerbreadMr1 = 10,
            //
            // Summary:
            //     February 2011: Android 3.0. The default theme for applications is now dark holographic:
            //     Android.Resource.Style.ThemeHolo. On large screen devices that do not have a
            //     physical menu button, the soft (compatibility) menu is disabled. The activity
            //     lifecycle has changed slightly as per Android.App.Activity. An application will
            //     crash if it does not call through to the super implementation of its Android.App.Activity.OnPause
            //     method. When an application requires a permission to access one of its components
            //     (activity, receiver, service, provider), this permission is no longer enforced
            //     when the application wants to access its own component. This means it can require
            //     a permission on a component that it does not itself hold and still access that
            //     component. Android.Content.Context.GetSharedPreferences(System.String, Android.Content.FileCreationMode)
            //     will not automatically reload the preferences if they have changed on storage,
            //     unless Android.Content.FileCreationMode.MultiProcess is used. Android.Views.ViewGroup.MotionEventSplittingEnabled
            //     will default to true. Android.Views.WindowManagerFlags.SplitTouch is enabled
            //     by default on windows. Android.Widget.PopupWindow.SplitTouchEnabled will return
            //     true by default. Android.Widget.GridView and Android.Widget.ListView will use
            //     Android.Views.View.Activated for selected items if they do not implement Android.Widget.ICheckable.
            //     Android.Widget.Scroller will be constructed with "flywheel" behavior enabled
            //     by default.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            Honeycomb = 11,
            //
            // Summary:
            //     May 2011: Android 3.1.
            HoneycombMr1 = 12,
            //
            // Summary:
            //     June 2011: Android 3.2. Android.Content.PM.ActivityInfo.ConfigChanges will have
            //     the Android.Content.PM.ConfigChanges.ScreenSize and Android.Content.PM.ConfigChanges.SmallestScreenSize
            //     bits set; these need to be cleared for older applications because some developers
            //     have done absolute comparisons against this value instead of correctly masking
            //     the bits they are interested in.
            //     Update to Honeycomb MR1 to support 7 inch tablets, improve screen compatibility
            //     mode, etc.
            //     As of this version, applications that don't say whether they support XLARGE screens
            //     will be assumed to do so only if they target Android.OS.Build.VERSION_CODES.Honeycomb
            //     or later; it had been Android.OS.Build.VERSION_CODES.Gingerbread or later. Applications
            //     that don't support a screen size at least as large as the current screen will
            //     provide the user with a UI to switch them in to screen size compatibility mode.
            //     This version introduces new screen size resource qualifiers based on the screen
            //     size in dp: see Android.Content.ResStrings.Configuration.ScreenWidthDp, Android.Content.ResStrings.Configuration.ScreenHeightDp,
            //     and Android.Content.ResStrings.Configuration.SmallestScreenWidthDp. Supplying these
            //     in <supports-screens> as per Android.Content.PM.ApplicationInfo.RequiresSmallestWidthDp,
            //     Android.Content.PM.ApplicationInfo.CompatibleWidthLimitDp, and Android.Content.PM.ApplicationInfo.LargestWidthLimitDp
            //     is preferred over the older screen size buckets and for older devices the appropriate
            //     buckets will be inferred from them.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            //     New Android.Content.PM.PackageManager.FeatureScreenPortrait and Android.Content.PM.PackageManager.FeatureScreenLandscape
            //     features were introduced in this release. Applications that target previous platform
            //     versions are assumed to require both portrait and landscape support in the device;
            //     when targeting Honeycomb MR1 or greater the application is responsible for specifying
            //     any specific orientation it requires.
            //     Android.OS.AsyncTask`3 will use the serial executor by default when calling Android.OS.AsyncTask`3.execute(Params...).
            HoneycombMr2 = 13,
            //
            // Summary:
            //     October 2011: Android 4.0. For devices without a dedicated menu key, the software
            //     compatibility menu key will not be shown even on phones. By targeting Ice Cream
            //     Sandwich or later, your UI must always have its own menu UI affordance if needed,
            //     on both tablets and phones. The ActionBar will take care of this for you. 2d
            //     drawing hardware acceleration is now turned on by default. You can use Android.Resource.Attribute.HardwareAccelerated
            //     to turn it off if needed, although this is strongly discouraged since it will
            //     result in poor performance on larger screen devices. The default theme for applications
            //     is now the "device default" theme: Android.Resource.Style.ThemeDeviceDefault.
            //     This may be the holo dark theme or a different dark theme defined by the specific
            //     device. The Android.Resource.Style.ThemeHolo family must not be modified for
            //     a device to be considered compatible. Applications that explicitly request a
            //     theme from the Holo family will be guaranteed that these themes will not change
            //     character within the same platform version. Applications that wish to blend in
            //     with the device should use a theme from the Android.Resource.Style.ThemeDeviceDefault
            //     family. Managed cursors can now throw an exception if you directly close the
            //     cursor yourself without stopping the management of it; previously failures would
            //     be silently ignored. The fadingEdge attribute on views will be ignored (fading
            //     edges is no longer a standard part of the UI). A new requiresFadingEdge attribute
            //     allows applications to still force fading edges on for special cases. Android.Content.Context.BindService(Android.Content.Intent,
            //     Android.Content.IServiceConnection, Android.Content.IServiceConnection) will
            //     not automatically add in Android.Content.Bind.WaivePriority. App Widgets will
            //     have standard padding automatically added around them, rather than relying on
            //     the padding being baked into the widget itself. An exception will be thrown if
            //     you try to change the type of a window after it has been added to the window
            //     manager. Previously this would result in random incorrect behavior. Android.Views.Animations.AnimationSet
            //     will parse out the duration, fillBefore, fillAfter, repeatMode, and startOffset
            //     XML attributes that are defined. Android.App.ActionBar.SetHomeButtonEnabled(System.Boolean)
            //     is false by default.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            IceCreamSandwich = 14,
            //
            // Summary:
            //     December 2011: Android 4.0.3.
            IceCreamSandwichMr1 = 15,
            //
            // Summary:
            //     June 2012: Android 4.1. You must explicitly request the Android.Manifest.Permission.ReadCallLog
            //     and/or Android.Manifest.Permission.WriteCallLog permissions; access to the call
            //     log is no longer implicitly provided through Android.Manifest.Permission.ReadContacts
            //     and Android.Manifest.Permission.WriteContacts. Android.Widget.RemoteViews will
            //     throw an exception if setting an onClick handler for views being generated by
            //     a Android.Widget.RemoteViewsService for a collection container; previously this
            //     just resulted in a warning log message. New Android.App.ActionBar policy for
            //     embedded tabs: embedded tabs are now always stacked in the action bar when in
            //     portrait mode, regardless of the size of the screen. Android.Webkit.WebSettings.AllowFileAccessFromFileURLs
            //     and Android.Webkit.WebSettings.AllowUniversalAccessFromFileURLs default to false.
            //     Calls to Android.Content.PM.PackageManager.SetComponentEnabledSetting(Android.Content.ComponentName,
            //     Android.Content.PM.ComponentEnabledState, Android.Content.PM.ComponentEnabledState)
            //     will now throw an IllegalArgumentException if the given component class name
            //     does not exist in the application's manifest. Android.Nfc.NfcAdapter.setNdefPushMessage(android.nfc.NdefMessage,
            //     android.app.Activity, android.app.Activity...), Android.Nfc.NfcAdapter.setNdefPushMessageCallback(android.nfc.NfcAdapter.CreateNdefMessageCallback,
            //     android.app.Activity, android.app.Activity...) and Android.Nfc.NfcAdapter.setOnNdefPushCompleteCallback(android.nfc.NfcAdapter.OnNdefPushCompleteCallback,
            //     android.app.Activity, android.app.Activity...) will throw IllegalStateException
            //     if called after the Activity has been destroyed. Accessibility services must
            //     require the new Android.Manifest.Permission.BindAccessibilityService permission
            //     or they will not be available for use. Android.AccessibilityServices.AccessibilityServiceInfo.FlagIncludeNotImportantViews
            //     must be set for unimportant views to be included in queries.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            JellyBean = 16,
            //
            // Summary:
            //     Android 4.2: Moar jelly beans! Content Providers: The default value of android:exported
            //     is now false. See the android:exported section in the provider documentation
            //     for more details.Android.Views.View.LayoutDirection can return different values
            //     than Android.Views.View.LayoutDirectionLtr based on the locale etc. Android.Webkit.WebView.AddJavascriptInterface(Java.Lang.Object,
            //     System.String) requires explicit annotations on methods for them to be accessible
            //     from Javascript.
            //     Applications targeting this or a later release will get these new changes in
            //     behavior:
            JellyBeanMr1 = 17,
            //
            // Summary:
            //     Android 4.3: Jelly Bean MR2, the revenge of the beans.
            JellyBeanMr2 = 18,
            Kitkat = 19,
            KitkatWatch = 20,
            Lollipop = 21,
            LollipopMr1 = 22,
            M = 23,
            N = 24,
            NMr1 = 25,
            O = 26,
            //
            // Summary:
            //     Magic version number for a current development build, which has not yet turned
            //     into an official release.
            CurDevelopment = 10000
        }

        public void ShowMenu()
        {

        }

        public void FastBootsrap()
        {
            throw new NotImplementedException();
        }

        public class CrashLog
        {
            public string Log { get; set; }
            public string Exceptions { get; set; }
            public string Stacktrace { get; set; }
        }


        public static void Crashed(string ex, string stack)

        {
            var log = new CrashLog();
            log.Exceptions = ex;
            log.Stacktrace = stack;

            try
            {
#if DEBUG
                log.Log = "DEBUG";
#else
                log.Log = "RELEASE";
#endif
            }
            catch (Exception e)
            {
                App.Logger.Error(MethodBase.GetCurrentMethod().DeclaringType.Name, e);
                log.Log = "Crashed FAILED.";
            }
            //ContentDataStore.Instance.LogCrashed(log);
        }

        public Task ProcessApiException(Exception exception)
        {
            throw new NotImplementedException();
        }
    }

    public class PopupMenu
    {

    }

}
