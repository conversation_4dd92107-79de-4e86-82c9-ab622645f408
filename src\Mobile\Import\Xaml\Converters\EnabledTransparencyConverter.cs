﻿using System;
using System.Globalization;


namespace AppoMobi.Xam.Converters
{
    public class EnabledTransparencyConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var result = 1.0;
 
            if (value is bool)
            {
                if ((bool) value)
                {
                    if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                        return 0.25;
                }
            }


            return result;
        }

 
    }
}
