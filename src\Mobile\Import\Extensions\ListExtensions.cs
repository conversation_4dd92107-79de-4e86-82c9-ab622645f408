﻿ 

namespace AppoMobi.Common
{

    //*************************************************************
    public static class ListExtensions
    //*************************************************************
    {

        
        public static string AsString<T> (this List<T> source)
        
        {
            var ret = "";
            try
            {
                int c = 0;
                foreach (var item in source)
                {
                    c++;
                    ret += item.ToString();
                    if (c < source.Count)
                    {
                        ret += "\r\n";
                    }
                }
            }
            catch (Exception e)
            {
            }
            return ret;
        }


    }


}
