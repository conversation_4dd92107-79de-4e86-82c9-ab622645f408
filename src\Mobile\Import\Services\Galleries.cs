﻿using AppoMobi.Common.Dto.Required;
using AppoMobi.Xam;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AppoMobi.Models
{
    //===================================================================
    public class Gallery : GalleryDTO, INotifyPropertyChanged
    //===================================================================
    {
        //-----------------------------------------------------------------
        public void Init()
        //-----------------------------------------------------------------
        {
            if (ImageColor.HasNoContent())
                ImageColor = BackColors.PlaceholderRemoteImages;
            ImageMain = new ImageInfo(ImageId, ImageColor.ColorFromHex(), Title);

        }

        public ImageInfo ImageMain { get; set; }

        private bool _Selected;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    OnPropertyChanged("Selected");
                }
            }
        }

        private List<GalleryImage> _Images;
        public new List<GalleryImage> Images
        {
            get { return _Images; }
            set
            {
                if (_Images != value)
                {
                    _Images = value;
                }
            }
        }

        #region INTERFACE

        public void RaiseProperties(params object[] raiseProperties)
        {
            if (raiseProperties != null)
            {
                foreach (var prop in raiseProperties)
                {
                    OnPropertyChanged(prop.ToString());
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

    }

    public class GalleryImage : GalleryDTO, INotifyPropertyChanged
    {
        public void Init()
        {
            if (ImageColor.HasNoContent())
                ImageColor = BackColors.PlaceholderRemoteImages;
            ImageMain = new ImageInfo(ImageId, ImageColor.ColorFromHex(), Title);
        }

        public ImageInfo ImageMain { get; set; }


        private bool _Selected;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    OnPropertyChanged("Selected");
                }
            }
        }


        //
        //public string NameSort
        //    
        //{
        //    get
        //    {
        //        if (string.IsNullOrWhiteSpace(Title) || Title.Length == 0)
        //            return "?";

        //        return Title[0].ToString().ToUpper(); // Title.ToTitleCase();
        //    }
        //}

        //
        //public string NameAll
        //    
        //{
        //    get
        //    {
        //        return AppoMobi.Forms.Common.ResX.ResStrings.AllSortedByDistance;
        //    }
        //}

        #region INTERFACE

        public void RaiseProperties(params object[] raiseProperties)
        {
            if (raiseProperties != null)
            {
                foreach (var prop in raiseProperties)
                {
                    OnPropertyChanged(prop.ToString());
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

    }


}
