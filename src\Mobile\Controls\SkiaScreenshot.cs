﻿namespace AppoMobi.Xam;

public class SkiaScreenshot : ContentLayout
{

    public SkiaScreenshot()
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            resized = await TakeScreenshot();
        });
    }

    protected SkiaImage Wallpaper { get; set; }

    private bool resized;

    public async Task<bool> Capture()
    {
        var bytes = await Super.CaptureScreenshotAsync();
        var bmp = SKBitmap.Decode(bytes);
        Image = bmp;
        return true;
    }

    private SemaphoreSlim _lockMe = new SemaphoreSlim(1);

    public async Task<bool> TakeScreenshot()
    {
        await _lockMe.WaitAsync();
        try
        {
            if (Wallpaper == null)
            {
                Erase();

                if (await Capture())
                {
                    Wallpaper = new()
                    {
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill,
                        UseCache = SkiaCacheType.Image,
                        LoadSourceOnFirstDraw = false,
                        Aspect = TransformAspect.None,
                        RescalingQuality = SKFilterQuality.None,
                        VerticalAlignment = DrawImageAlignment.Start,
                        //VisualEffects = new List<SkiaEffect>()
                        //{
                        //    new BlurEffect()
                        //    {
                        //        Amount = 3
                        //    },
                        //    new TintEffect()
                        //    {
                        //        Color = Color.FromHex("#40261F7A")
                        //    }
                        //}
                    };

                    Wallpaper.SetBitmapInternal(Image);

                    Content = Wallpaper;

                    return true;
                }
            }

            return false;
        }
        finally
        {
            _lockMe.Release();
        }
    }

    public override void OnDisposing()
    {
        base.OnDisposing();
        Image?.Dispose();
    }



    public void Erase()
    {
        //Image?.Dispose();
        Image = null;

        //GC.Collect();
    }

    //public byte[] Data { get; set; }

    protected override void OnLayoutReady()
    {
        base.OnLayoutReady();

        //void Act()
        //{
        //    if (MainThread.IsMainThread)
        //    {
        //        resized = TakeScreenshot();
        //    }
        //    else
        //    {
        //        MainThread.BeginInvokeOnMainThread(() =>
        //        {
        //            resized = TakeScreenshot();
        //        });
        //    }
        //}

        if (!resized)
        {



            //if (Device.RuntimePlatform == Device.iOS)
            //{
            //    Act();
            //}
            //else
            //{
            //    Tasks.StartDelayed(TimeSpan.FromMilliseconds(10), () =>
            //    {
            //        Act();
            //    });
            //}

        }

    }



    protected SKBitmap Image;
}
