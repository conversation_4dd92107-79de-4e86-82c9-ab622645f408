﻿using System;
using AppoMobi.Models;



namespace AppoMobi
{



 
    public interface ILaunchTwitter
        

    {
        bool OpenUserName(string username);
        bool OpenStatus(string statusId);
    }

    
    public class CWorld : BaseViewModelBak
    
    {

        public static void Dial(string number)
        {
            try
            {
                PhoneDialer.Open(number);
            }
            catch (ArgumentNullException anEx)
            {
                // Number was null or white space
            }
            catch (FeatureNotSupportedException ex)
            {
                // Phone Dialer is not supported on this device.
            }
            catch (Exception ex)
            {
                // Other error has occurred.
            }
        }

     

        public bool IsConnected()
        {
            return Connectivity.NetworkAccess == NetworkAccess.Internet;

        }

        public static void Mail(string adress)
        {
            Core.Native.OpenUrl($"mailto:{adress}");

          
        }
      

        //public async Task GetCoordsFromGoogle(string adress)
        //{
            //NiftyHttpClient client = new NiftyHttpClient();
            //  HttpResponseMessage response = await client.GetAsync(;
            //  string jsonstring = await response.Content.ReadAsStringAsync();

            //  System.Diagnostics.Debug.WriteLine(jsonstring);
            //  await DisplayAlert("JSON", jsonstring, ResStrings.ButtonOk);
        //}

       
    }




}
