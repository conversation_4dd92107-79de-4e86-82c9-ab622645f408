﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Xam;

namespace AppoMobi.Mobile.Views
{

    
    public static class Elements
    {
        public static SkiaGradient ControlGradient
        {
            get
            {
                return new SkiaGradient()
                {
                    Type = GradientType.Linear,
                    Colors =new List<Color>()
                    {
                        BackColors.GradientStartNav,
                        BackColors.GradientEndNav
                    },
                };

            }
        }
    }

    public class DrawnFontIcon : SkiaLabel
    {
        public DrawnFontIcon()
        {
            UseCache = SkiaCacheType.Operations;
            FontFamily = "FaSolid";
            VerticalTextAlignment = TextAlignment.Center;
            HorizontalTextAlignment = DrawTextAlignment.Center;
        }

        public void UpdateSkin()
        {
            Invalidate();
        }

        private const string nameIconName = "IconName";
        public static readonly BindableProperty IconNameProperty = BindableProperty.Create(nameIconName, typeof(string),
            typeof(DrawnFontIcon), string.Empty, propertyChanged: NeedSetIcon);


        public string IconName
        {
            get { return (string)GetValue(IconNameProperty); }
            set { SetValue(IconNameProperty, value); }
        }

        public void SetIcon(FontIconsPreset value, double scale = -1, string fontOverride = null)
        {
            if (fontOverride==null)
                fontOverride = value.FontOverride;

            //   label.Preset = value;
            if (value == null)
            {
                Text = null;
            }
            else
            {
                TranslationY = value.TranslationY;

                if (scale < 0)
                    Scale = value.scale;
                else
                    Scale = scale;

                Text = value.icon;

                if (!string.IsNullOrEmpty(fontOverride))
                    FontFamily = value.FontOverride;
            }

            UpdateSkin();
            //label.TranslationY = value.TranslationY;
            //label.VerticalOptions = LayoutOptions.Center;
        }

        private static void NeedSetIcon(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is DrawnFontIcon control)
            {
                var maybePreset = FontIcons.GetPresetByName(newValue as string);
                control.Preset = maybePreset;
            }
        }


        // Preset

        private const string namePreset = "Preset";
        public static readonly BindableProperty PresetProperty = BindableProperty.Create(namePreset,
            typeof(FontIconsPreset), typeof(DrawnFontIcon), null, propertyChanged: OnChangePreset);

        private static void OnChangePreset(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is DrawnFontIcon control)
            {
                control.SetIcon(newValue as FontIconsPreset);
            }
        }

        public FontIconsPreset Preset
        {
            get { return (FontIconsPreset)GetValue(PresetProperty); }
            set { SetValue(PresetProperty, value); }
        }

    }
}
