﻿using System;

using AppoMobi.Xam;
using AppoMobi.Touch;
using Microsoft.Maui.Controls.Shapes;


namespace AppoMobi.UI
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class FrameButton
    {
	    public EventHandler RendererNeedUpdate { get; set; }

        
	    protected override void OnSizeAllocated(double width, double height)
        
	    {
	        base.OnSizeAllocated(width, height);

            if (width>0) RendererNeedUpdate?.Invoke(this, null);
        }

	    
        public FrameButton()
		{
			InitializeComponent ();

            GradientOrientation = StackOrientation.Vertical;

            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                cLabel.TranslationY = -0.2;

            }
            else
            {
                cLabel.TranslationY = 0.2;

            }

		    

            SetupIndicator();
		    SetupStyle(StyleOptions);
		}
        
	    private void SetupIndicator()
        
	    {
	      

        }

        
	    private void SetDisable(bool on)
        
	    {
	        if (!on)
	        {
	            Opacity = 1;
	        }
	        else
	        {
	            Opacity = 0.5;
	        }
	    }

        
	    private void SetupStyle(ButtonStyle style)
        
	    {
	        switch (style)
	        {
	            case ButtonStyle.Normal:
	                //IndicatorColor = Color.Parse("#ffffff");
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#20000000");
                        StartColor = Colors.Transparent;//FromHex("#ff2727");
                        EndColor = Colors.Transparent;//("#ff2727");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#20000000");
                        StartColor = Colors.White;//FromHex("#ff2727");
                        EndColor = Colors.White;//("#ff2727");
                    }
  
	                TextColor = Colors.Black;
	                break;

	            case ButtonStyle.Active:
	                IndicatorColor = Color.Parse("#d77cff");
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#a0000000");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#8182f7");
                    }

                    StartColor = BackColors.GradientStart;
	                EndColor = BackColors.GradientEnd;
	                TextColor = Colors.White;
	                break;

	            case ButtonStyle.Error:
	                IndicatorColor = Color.Parse("#ff2727");
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#90000000");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#fb6363");
                    }

                    StartColor = IndicatorColor;
	                EndColor = Color.Parse("#ff2727");
	                TextColor = Colors.White;
	                break;

	            case ButtonStyle.Success:
	                IndicatorColor = Color.Parse("#51be37");
                    if (DeviceInfo.Platform == DevicePlatform.Android)
                    {
                        ShadowColor = Color.Parse("#a0000000");
                    }
                    else
                    {
                        ShadowColor = Color.Parse("#51be37");
                    }

                    StartColor = IndicatorColor;
	                EndColor = IndicatorColor;
                    TextColor = Colors.White;
	                break;

	        }

	        RendererNeedUpdate?.Invoke(this, null);
        }

	    
	    // Tag
	    
	    private const string nameTag = "Tag";
	    public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(FrameButton), string.Empty);
	    public string Tag
	    {
	        get { return (string)GetValue(TagProperty); }
	        set { SetValue(TagProperty, value); }
	    }


        //-------------------------------------------------------------
        // Index
        //-------------------------------------------------------------
        private const string nameIndex = "Index";
        public static readonly BindableProperty IndexProperty = BindableProperty.Create(nameIndex, typeof(int), typeof(FrameButton), 0); //, BindingMode.TwoWay
        public int Index
        {
            get { return (int)GetValue(IndexProperty); }
            set { SetValue(IndexProperty, value); }
        }	


        
        // Text
        
        private const string nameText = "Text";
	    public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(FrameButton), string.Empty);
	    public string Text
	    {
	        get { return (string)GetValue(TextProperty); }
	        set { SetValue(TextProperty, value); }
	    }

	    //-------------------------------------------------------------
	    // Selected
	    //-------------------------------------------------------------
	    private const string nameSelected = "Selected";
	    public static readonly BindableProperty SelectedProperty = BindableProperty.Create(nameSelected, typeof(bool), typeof(FrameButton), false); //, BindingMode.TwoWay
	    public bool Selected
	    {
	        get { return (bool)GetValue(SelectedProperty); }
	        set { SetValue(SelectedProperty, value); }
	    }

        //-------------------------------------------------------------
        // TextColor
        //-------------------------------------------------------------
        private const string nameTextColor = "TextColor";
	    public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameTextColor, typeof(Color), typeof(FrameButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color TextColor
	    {
	        get { return (Color)GetValue(TextColorProperty); }
	        set { SetValue(TextColorProperty, value); }
	    }



        //-------------------------------------------------------------
        // StyleOptions
        //-------------------------------------------------------------
        private const string nameStyleOptions = "StyleOptions";
        public static readonly BindableProperty StyleOptionsProperty = BindableProperty.Create(nameStyleOptions, typeof(ButtonStyle), typeof(FrameButton), ButtonStyle.Normal); //, BindingMode.TwoWay
        public ButtonStyle StyleOptions
        {
            get { return (ButtonStyle)GetValue(StyleOptionsProperty); }
            set { SetValue(StyleOptionsProperty, value); }
        }

	    
	    public enum ButtonStyle
	    
	    {
	        Normal,
	        Active,
	        Error,
            Success
	    }



        //-------------------------------------------------------------
        // Disabled
        //-------------------------------------------------------------
        private const string nameDisabled = "Disabled";
        public static readonly BindableProperty DisabledProperty = BindableProperty.Create(nameDisabled, typeof(bool), typeof(FrameButton), false); //, BindingMode.TwoWay
        public bool Disabled
        {
            get { return (bool)GetValue(DisabledProperty); }
            set { SetValue(DisabledProperty, value); }
        }	

        //-------------------------------------------------------------
        // ShadowColor
        //-------------------------------------------------------------
        private const string nameShadowColor = "ShadowColor";
	    public static readonly BindableProperty ShadowColorProperty = BindableProperty.Create(nameShadowColor, typeof(Color), typeof(FrameButton), Color.Parse("#28000000")); //, BindingMode.TwoWay
	    public Color ShadowColor
	    {
	        get { return (Color)GetValue(ShadowColorProperty); }
	        set { SetValue(ShadowColorProperty, value); }
	    }

	    //-------------------------------------------------------------
	    // IndicatorColor
	    //-------------------------------------------------------------
	    private const string nameIndicatorColor = "IndicatorColor";
	    public static readonly BindableProperty IndicatorColorProperty = BindableProperty.Create(nameIndicatorColor, typeof(Color), typeof(FrameButton), Color.Parse("#d77cff")); //, BindingMode.TwoWay
	    public Color IndicatorColor
	    {
	        get { return (Color)GetValue(IndicatorColorProperty); }
	        set { SetValue(IndicatorColorProperty, value); }
	    }


        //-------------------------------------------------------------
        // StartColor
        //-------------------------------------------------------------
        private const string nameStartColor = "StartColor";
	    public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(FrameButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color StartColor
	    {
	        get { return (Color)GetValue(StartColorProperty); }
	        set { SetValue(StartColorProperty, value); }
	    }

        //-------------------------------------------------------------
        // EndColor
        //-------------------------------------------------------------
        private const string nameEndColor = "EndColor";
	    public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(FrameButton), Colors.Transparent); //, BindingMode.TwoWay
	    public Color EndColor
	    {
	        get { return (Color)GetValue(EndColorProperty); }
	        set { SetValue(EndColorProperty, value); }
	    }


        //-------------------------------------------------------------
        // GradientOrientation
        //-------------------------------------------------------------
        private const string nameGradientOrientation = "GradientOrientation";
        public static readonly BindableProperty GradientOrientationProperty = BindableProperty.Create(nameGradientOrientation, typeof(StackOrientation), typeof(FrameButton), StackOrientation.Horizontal); //, BindingMode.TwoWay
        public StackOrientation GradientOrientation
        {
            get { return (StackOrientation)GetValue(GradientOrientationProperty); }
            set { SetValue(GradientOrientationProperty, value); }
        }


        //-------------------------------------------------------------
        // FontSize
        //-------------------------------------------------------------
        private const string nameFontSize = "FontSize";
        public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize, typeof(double), typeof(FrameButton), 12.0); //, BindingMode.TwoWay
        public double FontSize
        {
            get { return (double)GetValue(FontSizeProperty); }
            set { SetValue(FontSizeProperty, value); }
        }

        //property changed
        //case nameFontSize:
        //             ControlName.FontSize = FontSize;
        //             break;		


        //-------------------------------------------------------------
        // BorderRadius
        //-------------------------------------------------------------
        private const string nameBorderRadius = "BorderRadius";
        public static readonly BindableProperty BorderRadiusProperty = BindableProperty.Create(nameBorderRadius, typeof(double), typeof(FrameButton), 8.0); //, BindingMode.TwoWay
        public double BorderRadius
        {
            get { return (double)GetValue(BorderRadiusProperty); }
            set { SetValue(BorderRadiusProperty, value); }
        }

        //property changed
        //case nameBorderRadius:
        //             ControlName.BorderRadius = BorderRadius;
        //             break;		

        private const string nameCornerRadius = "CornerRadius";
        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(nameCornerRadius, typeof(float), typeof(FrameButton), 8.0f); //, BindingMode.TwoWay
        public float CornerRadius
        {
            get { return (float)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }

        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {
	            //property changed
	            case nameDisabled:
	                SetDisable(Disabled);
	                break;

                case nameText:
	                cLabel.Text = Text;
	                break;

                case nameCornerRadius:
                    StrokeShape = new RoundRectangle
                    {
                        CornerRadius = new CornerRadius(CornerRadius)
                    };
                    Update();
                    break;

            case nameTextColor:
	                cLabel.TextColor = TextColor;
	                break;

	            case nameBackgroundColor:	       
                    StartColor = BackgroundColor;
	                EndColor = BackgroundColor.MakeDarker(15);
	                Update();
                    break;

                case nameStyleOptions:
                    SetupStyle(StyleOptions);
                    Update();
                    break;
            }

	    }

        public void Update()
        {
            RendererNeedUpdate?.Invoke(this, null);
        }

        //-------------------------------------------------------------
        // BackgroundColor
        //-------------------------------------------------------------
        private const string nameBackgroundColor = "BackgroundColor";
        public new static readonly BindableProperty BackgroundColorProperty = BindableProperty.Create(nameBackgroundColor, typeof(Color), typeof(FrameButton), Colors.Red); //, BindingMode.TwoWay
        public new Color BackgroundColor
        {
            get { return (Color)GetValue(BackgroundColorProperty); }
            set { SetValue(BackgroundColorProperty, value); }
        }


		//-------------------------------------------------------------
		// Light
		//-------------------------------------------------------------
		private const string nameLight = "Light";
		public static readonly BindableProperty LightProperty = BindableProperty.Create(nameLight, typeof(double), typeof(FrameButton), 1.0); //, BindingMode.TwoWay
		public double Light
		{
			get { return (double)GetValue(LightProperty); }
			set { SetValue(LightProperty, value); }
		}

        //-------------------------------------------------------------
        // LightOnDown
        //-------------------------------------------------------------
        private const string nameLightOnDown = "LightOnDown";
        public static readonly BindableProperty LightOnDownProperty = BindableProperty.Create(nameLightOnDown, typeof(double), typeof(FrameButton), 1.20); //, BindingMode.TwoWay
        public double LightOnDown
        {
            get { return (double)GetValue(LightOnDownProperty); }
            set { SetValue(LightOnDownProperty, value); }
        }



		
		private void CGrid_OnUp(object sender, DownUpEventArgs e)
	    
	    {
	        if (Disabled) return;
	        
	        OnUp();
	    }
        private Color _backgroundColor;

        
        private void CGrid_OnDown(object sender, DownUpEventArgs e)
        
        {
            if (Disabled) 
                return;

            Core.Native.ExecuteTask("playClickSound", null);

                //// Update the UI
                //_backgroundColor = BackgroundColor;
                //Scale = 0.98;
                
                Light = LightOnDown;

                //BackgroundColor = BackgroundColor.MakeLighter(25);
                ////StartColor = BackgroundColor.MakeDarker(20);
                ////EndColor = BackgroundColor.MakeDarker(35);

            Clicked?.Invoke(this, e);

        }

        public void Release()
        {
            OnUp();
        }

        protected void OnUp()
        {
            Light = 1.0;
            //Scale = 1.0;
            //BackgroundColor = _backgroundColor;
        }

        public event EventHandler Clicked = null;



	    
	    public void Select(ButtonStyle style)
	    
	    {
	        StyleOptions = style;
	        Selected = true;
	    }

        
        public void Deselect(ButtonStyle style)
        
	    {
	        StyleOptions = style; 
	        Selected = false;
	    }

	}
}