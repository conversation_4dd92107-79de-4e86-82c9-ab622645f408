﻿using System;
using System.Globalization;


namespace AppoMobi.Xam.Converters
{
    public class ColorSemiTransparentConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
          
            if (value is Color)
            {
                var ret = ((Color) value).MultiplyAlpha(0.5f);
                return ret;
            }

            return value;
        }

 
    }
}
