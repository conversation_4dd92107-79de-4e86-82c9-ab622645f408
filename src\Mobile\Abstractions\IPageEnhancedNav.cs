﻿using AppoMobi.Maui.Navigation;
using AppoMobi.Xam;

namespace AppoMobi.Services;

public interface IIncludedContent : IView, IDisposable
{
    void CommandToContentRecieved(string command, string param);
    void CommandFromPopupRecieved(string command, string param);

    void OnAppearing();
    void OnDisappearing();
    void AnimatingPage();
    void PreparingPageAnimations();
    void OnBeforeGoBack();

    void OnTabActivated();
    void OnTabDeactivated();
    void OnUpdatedControls();
    void OnRightIcon1Clicked();
    void OnRightIcon2Clicked();

    void Init(IPageEnhancedNav parent);
}

public interface IPageEnhancedNav : IPageEnhanced
{

    bool CommandToPopup(string command, string param);
    bool CommandFromPopup(string command, string param);
    void CommandFromPopupRecieved(string command, string param);
    void CommandFromContentRecieved(string command, string param);
    bool CommandFromContent(string command, string param);

    Task GoBack();
    Task Destroy();

    void ToggleButtonVisibility(ButtonType button, bool visible);

    FontIconLabel LeftIcon1Symbol { get; }
    FontIconLabel LeftIcon2Symbol { get; }
    FontIconLabel RightIcon1Symbol { get; }
    FontIconLabel RightIcon2Symbol { get; }
}

public interface IPageEnhanced 
{
    
    public string Tag { get; set; }

    bool IsInTabs { get; set; }
    bool IsActiveTab { get; set; }
    bool PseudoTab { get; set; }
    public bool IsModal { get; set; }

    /// <summary>
    /// Whether we can navigate back or not if root 
    /// </summary>
    public bool IsRoot { get; set; }

    void TabDisactivated();
    void TabActivated();
    void TabsRendered();
    void AddedToTabs();

    void SendOnAppearing();
}

public enum ButtonType
{
    Left1,
    Left2,
    Left3,
    Right1,
    Right2,
    Right3
}


public enum ScrollDirection
{
    Up,
    Down,
    None
}
