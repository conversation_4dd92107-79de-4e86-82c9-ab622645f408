﻿using System;
using System.Collections.Generic;
using System.Linq;
using AppoMobi.Touch;



namespace AppoMobi.Xam
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SelectionListView
    {

        public List<SelectionListItem> MenuList = new List<SelectionListItem>();


        
        public SelectionListView(Action<string> callback, string title, List<KeyValuePair<string, string>> list, string cancel, bool disableBackgroundclick = false, bool quitOnBackPressed = false, string selectedKey = null)
        
        {
            InitializeComponent();


            DisableBackgroundClick = disableBackgroundclick;
            QuitOnBackPressed = quitOnBackPressed;

            Message = title;
            CallbackListKey = callback;

            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                cTitle.TranslationY = 1;
            }

            //todo selected
            var ii = 0;
            foreach (var item in list)
            {
                var add = new SelectionListItem();
                add.Title = item.Value;
                if (item.Key == selectedKey)
                    add.Selected = true;
                ii++;
                MenuList.Add(add);
            }

            //submit =)
            BindingContext = this;

            if (list != null && list.Any())
            {

            }
            else
                Grid_OnDown(null, null);

            DataStack.ItemsSource = MenuList;
        }


        
        public SelectionListView(Action<int> callback, string title, List<string> list, string cancel, bool disableBackgroundclick = false, bool quitOnBackPressed = false, int selected=-1)
        
        {
            InitializeComponent();


            DisableBackgroundClick = disableBackgroundclick;
            QuitOnBackPressed = quitOnBackPressed;

            Message = title;
            CallbackList = callback;

            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                cTitle.TranslationY = 1;
            }

            //todo selected
            var ii = 0;
            foreach (var item in list)
            {
                var add = new SelectionListItem();
                add.Title = item;
                if (ii == selected)
                    add.Selected = true;
                ii++;
                MenuList.Add(add);
            }
            
            //submit =)
            BindingContext = this;

            if (list!=null && list.Any())
            {

            }
            else
                Grid_OnDown(null, null);
            
            DataStack.ItemsSource = MenuList;
        }




        private bool _tapped = false;
        //-------------------------------------------------------------
        private async void OnTapped_Item(object sender, TapEventArgs e)
        //-------------------------------------------------------------
        {
            if (_tapped) return;
            _tapped = true;

            _gridOnDownFired = false;

            var item = (SelectionListItem)e.Sender.TappedCommandParameter;
            if (item == null) return;

            var index = MenuList.IndexOf(item);

            await DismissDialog(index);
            _tapped = false;
        }

        private bool _gridOnDownLock;
        private bool _gridOnDownFired;
        private bool _gridOnDownTracked;
        
        private void Grid_OnDown(object sender, DownUpEventArgs e)
        
	    {
	        if (_gridOnDownLock) return;
	        _gridOnDownLock = true;

            _gridOnDownFired = true;
	        if (!_gridOnDownTracked)
	        {
	            _gridOnDownTracked = true;
	            Device.StartTimer(TimeSpan.FromMilliseconds(250),  () =>
	            {
	                if (_gridOnDownFired)
	                {
	                    try
	                    {
	                        DismissDialog(-1);
                        }
                        catch (Exception exception)
	                    {
	                    }
                    }
                    else
	                    _gridOnDownTracked = false;
	                return false;
	            });
	        }
            _gridOnDownLock = false;            
	    }
    }


   

}