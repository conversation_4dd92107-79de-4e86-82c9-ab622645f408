﻿using System;
using System.Collections.Generic;
using System.Text;
using Android.App;
using Android.OS;
using Android.Runtime;
using Android.Views;
using AppoMobi.Droid.Native;
 
using Configuration = FFImageLoading.Config.Configuration;

namespace AppoMobi.Droid
{
    public class Reuse
    {

        
        public  static void AndroidEnvironmentUnhandledExceptionRaiser(object sender, RaiseThrowableEventArgs e)
        
        {
            //todo send error  !!! 

            string str = ToTags(GetInnerExceptionsMessages(e.Exception));
            Core.Crashed(str, e.Exception.StackTrace);

            


        }


        //-----------------------------------------------------------------
        public static int ConvertPixelsToDp(float pixelValue, Android.Content.Res.Resources resources)
        //-----------------------------------------------------------------
        {
            var dp = (int)((pixelValue) / resources.DisplayMetrics.Density);
            return dp;
        }

        
        public static void AfterOnCreate(Activity activity, Bundle bundle, Android.Content.Res.Resources resources)
        
        {

             

            //exceptions
            AndroidEnvironment.UnhandledExceptionRaiser += AndroidEnvironmentUnhandledExceptionRaiser;

            //images
            //FFImageLoading.Forms.Platform.CachedImageRenderer.Init(enableFastRenderer: true);
            //var config = new Configuration
            //{
            //    ExecuteCallbacksOnUIThread = true,
            //    FadeAnimationEnabled = false,
            //    AllowUpscale = true,
            //};
            //FFImageLoading.ImageService.Instance.Initialize(config);

            //appomobi engine warmup
            Core.AndroidAPI = (int)Build.VERSION.SdkInt;
            Core.IsAndroid = true;
            DroidCore.Init();
            DroidCore.MainWindow = activity.Window;
            DroidCore.MainActivity = activity as MainActivity;
            DroidCore.Current.MainView = activity.Window.DecorView;
            //Init AppHelper
            var metrics = resources.DisplayMetrics;
            Core.ScreenSizePixels = new Size(metrics.WidthPixels, metrics.HeightPixels);
            Core.ScreenSize = new Size(ConvertPixelsToDp(metrics.WidthPixels, resources), ConvertPixelsToDp(metrics.HeightPixels, resources));
            //Core.DisplayDensity = resources.DisplayMetrics.Density;

            //Core.StatusBarHeight = GetStatusBarHeight(resources);

            Core.StatusBarHeightRequest = Super.StatusBarHeight;
            //var flags = activity.Window.Attributes.Flags;
            //if ((flags & WindowManagerFlags.TranslucentStatus) == WindowManagerFlags.TranslucentStatus)
            //{
            //    //                trans = true;
            //}
            //else
            //{
            //    Core.HideStatusBar = true;
            //    Core.StatusBarHeight = 0;
            //}

        }



        public static int GetStatusBarHeight(Android.Content.Res.Resources resources)
        {
            int statusBarHeight = 0, totalHeight = 0, contentHeight = 0;
            int resourceId = resources.GetIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0)
            {
                statusBarHeight = resources.GetDimensionPixelSize(resourceId);

                totalHeight = resources.DisplayMetrics.HeightPixels;
                contentHeight = totalHeight - statusBarHeight;
                statusBarHeight = (int)(statusBarHeight / resources.DisplayMetrics.Density);
            }

            return statusBarHeight;
        }

        
        public static IEnumerable<string> GetInnerExceptionsMessages(Exception ex)
        
        {
            if (ex == null)
            {
                throw new ArgumentNullException("ex");
            }

            var innerException = ex;
            do
            {
                yield return innerException.Message;
                innerException = innerException.InnerException;
            }
            while (innerException != null);
        }

        
        public static string ToTags(IEnumerable<string> input, string separator = ",")
        
        {
            if (input != null)
            {
                return String.Join(separator, input);
            }
            else
            {
                return "";
            }
        }

    }
}
