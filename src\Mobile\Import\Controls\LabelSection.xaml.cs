﻿


namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class LabelSection : ContentView
    {
        public LabelSection()
        {
            InitializeComponent();
        }



        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
            
        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
                case "TextColor":
                    Section.TextColor = TextColor;//DeviceInfo.Current.Platform == DevicePlatform.iOS ? Text.ToUpperInvariant() : Text;
                    break;
          
                case "Text":
                    Section.Text = Text;
                    break;
               

                    //                case nameLabelIcon:
                    //                    ControlLabelIcon.Source = LabelIcon;
                    //                    break;
            }

        }


        public static readonly BindableProperty TextProperty =
            BindableProperty.Create(nameof(Text), typeof(string), typeof(LabelSection), string.Empty);
   
        
        public string Text
        
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }

        public static readonly BindableProperty TextColorProperty =
            BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(LabelSection), Colors.Black);

        
        public Color TextColor
            
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }


    }
}
