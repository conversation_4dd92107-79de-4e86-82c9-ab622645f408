﻿using System.Diagnostics;
using AppoMobi;
using AppoMobi.Maui.Gestures;
using AppoMobi.Xam;
using DrawnUi.Controls;
using DrawnUi.Views;

namespace AppoMobi.Mobile.Views
{
    //add effect on down
    //todo check this background for page {x:Static xam11:BackColors.Page} => status dim
    //stop rendering upon navigating back!

    /// <summary>
    /// Main shell for the application that handles navigation and UI layout.
    /// Inherits from SkiaShell to utilize DrawnUI rendering capabilities.
    /// </summary>
    public class AppShell : SkiaShell
    {
        /// <summary>
        /// Builds the UI hierarchy for the shell.
        /// Sets up the Canvas with layouts and initializes UI components.
        /// </summary>
        public override void Build()
        {
            base.Build();

            NavigationPage.SetBackButtonTitle(this, ResStrings.BtnGoBack);

#if ANDROID
            Super.SetBlackTextStatusBar();
            Super.SetNavigationBarColor(AppColors.ControlPrimary, AppColors.ControlPrimary, true);
#endif

            KillCanvas();

            Content = new Canvas()
            {
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Gestures = GesturesMode.Lock,
                RenderingMode = RenderingModeType.Default,
#if WINDOWS
                BackgroundColor = Colors.Black, //but will be opaque on Windows
#endif
                Content =
                    new SkiaLayout() //wrapper
                    {
                        Background =
                            new LinearGradientBrush(
                                new GradientStopCollection()
                                {
                                    new GradientStop(BackColors.GradientPageFaderStart, 0),
                                    new GradientStop(BackColors.GradientPageFaderStartEnd, 1),
                                }, new(0, 0), new(0, 1)),
                        Tag = "Canvas", //wrapper
                        VerticalOptions = LayoutOptions.Fill,
                        HorizontalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            new SkiaLayout()
                            {
                                Tag = "ShellLayout",
                                VerticalOptions = LayoutOptions.Fill,
                                HorizontalOptions = LayoutOptions.Fill,
                            }.WithChildren(
                                new SkiaImage()
                                    {
                                        BackgroundColor = Colors.Red,
                                        //UseCache = SkiaCacheType.Image,
                                        Opacity = 0.133,
                                        Source = @"Images\back.jpg",
                                    }
                                    .Fill(),
                                new SkiaLayout()
                                {
                                    Tag = "RootLayout", //    RootLayout placeholder will be replaced upon navigation
                                    VerticalOptions = LayoutOptions.Fill,
                                    HorizontalOptions = LayoutOptions.Fill,
                                }
                            ),
#if DEBUG
                            new SkiaLabelFps()
                            {
                                Margin = new(0, 0, 4, 24),
                                VerticalOptions = LayoutOptions.Center,
                                HorizontalOptions = LayoutOptions.End,
                                Rotation = -45,
                                BackgroundColor = Colors.DarkRed,
                                TextColor = Colors.White,
                                ZIndex = 110,
                            }
#endif
                        }
                    }
            };

            Canvas.WillFirstTimeDraw += async (object? sender, SkiaDrawingContext? ctx) =>
            {
                // Animate FADE IN after splash screen
                Canvas.Opacity = 0.001;
                await Canvas.FadeTo(1, 1500, Easing.CubicIn);
                TouchEffect.Density = Canvas.RenderingScale;
            };

            this.Content = Canvas;

            OnSubscribing(true);

            //if (Initialized)
            {
                Initialize(OrderedRoute);
            }
        }

        //public override void OnPopupsStackChanged(int count)
        //{
        //    base.OnPopupsStackChanged(count);

        //    if (count == 0)
        //    {
        //        App.Instance.Singletons.Messager.All(AppMessages.Shell, "PopupsClosed");
        //    }
        //    else
        //    {
        //        App.Instance.Singletons.Messager.All(AppMessages.Shell, "PopupOpen");
        //    }
        //}

        //public override void OnModalStackChanged(int count)
        //{
        //    base.OnModalStackChanged(count);

        //    if (count == 0)
        //    {
        //        App.Instance.Singletons.Messager.All(AppMessages.Shell, "ModalsClosed");
        //    }
        //    else
        //    {
        //        App.Instance.Singletons.Messager.All(AppMessages.Shell, "ModalOpen");
        //    }
        //}

        private NavigationViewModel? Model;

        /// <summary>
        /// Initializes the flyout menu for the shell.
        /// Currently configured with no menu.
        /// </summary>
        void InitFlyout()
        {
            //no menu
        }

        public override double OnKeyboardResized(double size)
        {
            if (Model == null)
            {
                return size;
            }

            Model.Keyboard = size;

            return base.OnKeyboardResized(size);
        }

        public NavigationViewModel Presentation;

        /// <summary>
        /// Initializes a new instance of the AppShell class.
        /// Sets up routes, navigation properties, and UI configurations.
        /// </summary>
        public AppShell()
        {
            Model = App.Shell.Presentation;
            NavigationPage.SetHasNavigationBar(this, false);
            BackgroundColor = Colors.Transparent; //AppColors.ControlPrimary;

            // https://learn.microsoft.com/en-us/dotnet/maui/user-interface/system-theme-changes?view=net-maui-8.0
            Application.Current.UserAppTheme = AppTheme.Dark;

            //ROUTES
            foreach (var appRoute in AppDrawnRoutes.GetRoutes())
            {
                RegisterRoute(appRoute.Item1, appRoute.Item2);
            }

            //RegisterActionRoute("settings", () => { this.NavigationLayout.SelectedIndex = 3; });

            OrderedRoute = AppDrawnRoutes.RootDefault;

            //UI tweaks
            Super.BottomTabsHeight = 68;
            Super.NavBarHeight = 86;

            ToastBackgroundColor = Color.Parse("#AA000000");
            ToastTextFont = "FontText";

            PopupsAnimationSpeed = 250;
            PopupBackgroundColor = Color.Parse("#22000000");
            PopupsBackgroundBlur = 0;
        }

        /// <summary>
        /// Called when navigation occurs within the shell.
        /// Initializes components and connections based on the navigation route.
        /// </summary>
        /// <param name="e">Navigation event arguments containing route information</param>
        protected override void OnNavigated(SkiaShellNavigatedArgs e)
        {
            base.OnNavigated(e);

            //if (e.Source == NavigationSource.Push)
            //{
            //    App.Instance.Messager.All(AppMessages.NavigatedToView, $"{e.View}");
            //}

            System.Diagnostics.Debug.WriteLine($"Shell navigated to {e.Route} {e.View}");

            //if (e.Route == AppRoutes.Root.Route)
            //{
            //    InitFlyout();

            //    Tasks.StartDelayed(TimeSpan.FromSeconds(1.5), InsureSignalsConnected);
            //}
        }

        /// <summary>
        /// Disposes of the Canvas to free resources and prevent memory leaks.
        /// </summary>
        public void KillCanvas()
        {
            if (Canvas != null)
            {
                //Canvas.FocusedItemChanged -= Model.OnFocusedItemChanged;
                Canvas.Dispose();
            }

            OnSubscribing(false);
        }


        /// <summary>
        /// Handles disposal of resources when the shell is disposed.
        /// Ensures proper cleanup of the Canvas and BindingContext.
        /// </summary>
        /// <param name="isDisposing">Whether this is being called from Dispose() or the finalizer</param>
        protected override void Dispose(bool isDisposing)
        {
            if (isDisposing)
            {
                KillCanvas();
                this.Content = null;
            }

            if (this.BindingContext is IDisposable disposable)
            {
                disposable.Dispose();
            }

            base.Dispose(isDisposing);
        }


        protected void OnSubscribing(bool subscribe)
        {
            if (!subscribe)
            {
            }
            else
            {
            }
        }
    }
}
