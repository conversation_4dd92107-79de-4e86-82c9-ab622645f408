﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AppoMobi.Common.Dto;
using AppoMobi.Common.Constants;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Main;
using AppoMobi.Models;
using AppoMobi.Xam;

namespace AppoMobi
{

     

//================================================
public class AppoDataStore : InstancedBaseModel<AppoDataStore>
    //================================================
    {


        private string ContentKey { get; set; } = Constants.KeyAppo;

   //    public readonly IAppoApi Api = RestService.For<IAppoApi>(Constants.ServerApi);       

        
        public AppoDataStore() 
        
        {
        }
        
        public async Task<TenantInfoDTO> GetInfo()
        
        {
            //todo here mock
            throw new NotImplementedException();

            var ret = new TenantInfoDTO();

            ret.Languages=new List<string>();
            ret.Companies = new List<CompanyInfoDTO>
            {
                new CompanyInfoDTO
                {                     
                    Name = "ART OF FOTO",
                    FullName = "ART OF FOTO",
                    ContactUsEmail = "<EMAIL>",
                    ContactUsTel = "+7 (812) 312 28 56",
                    ContactUsWebsite = "http://artoffoto.com",
                    ContactUsFacebook = "artoffoto",
                    ContactUsInstagram = "artoffoto",
                    ContactUsVk = "https://vk.com/public93007499",
                    MapX = 59.940916,
                    MapY = 30.324088,
                    ContactUsAddress = ResStrings.OfflineCompanyAddress
                }
            };
            ret.Modules = new List<string>();
            ret.Strings = new AppStrings();
            ret.Strings.AboutCompanyDesc = ResStrings.OfflineCompanyDesc;
            return ret;

        }

        
        public TenantInfoDTO GetInfoOffline()
        
        {
            //todo here mock

            var ret = new TenantInfoDTO();

            ret.Companies = new List<CompanyInfoDTO>
            {
                new CompanyInfoDTO
                {
                    Name = "Art of Foto",
                    FullName = "Art of Foto",
                    ContactUsEmail = "<EMAIL>",
                    ContactUsTel = "+7 (812) 312 28 56",
                    ContactUsWebsite = "http://artoffoto.com",
                    ContactUsFacebook = "artoffoto",
                    ContactUsFacebookNumeric = "1627629767473944", // fb://page/?id=1627629767473944
                    ContactUsInstagram = "artoffoto",
                    ContactUsVk = "public93007499",
                    MapX = 59.940916,
                    MapY = 30.324088,
                    ContactUsAddress = ResStrings.OfflineCompanyAddress
                }
            };

            ret.Languages = new List<string>
            {
                "en", //first IMPORTANT
                "de",
                "fr",
                "es",
                "ru",
                "zh",
                "ko",
            };
            


            ret.Skins = new List<string>
            {
                "Dark", 
                "Light",
            };

            ret.FadeWallpaper = 0.85;

            ret.Modules = new List<string>();
            ret.Strings = new AppStrings();
            ret.Strings.AboutCompanyDesc = ResStrings.OfflineCompanyDesc;

            return ret;

        }


        
        public async Task<TenantSplashDTO> GetSplash()
        
        {
            return null;
        }
      
      


    }



    //================================================
    public class RegionsDataStore : GenericDataStore<RegionDTO>
    //================================================
    {
        public static readonly RegionsDataStore Instance = new RegionsDataStore();

        
        public RegionsDataStore() : base(Constants.KeyRegions)
        
        {
        }
        
        public override ObservableRangeCollection<RegionDTO> SortItems(ObservableRangeCollection<RegionDTO> sorted)
        
        {
            var list = new ObservableRangeCollection<RegionDTO>();
            list.AddRange(BufferedItems.OrderByDescending(x => x.Priority));
            return list;
        }

    }


    //================================================
    public class TenantDataStore : GenericDataStore<TenantInfoDTO>
    //================================================
    {
        public static readonly TenantDataStore Instance = new TenantDataStore();
        
        public TenantDataStore() : base(Constants.KeyUI)
        
        {
        }
    }

     


    //================================================
    public class GenericDataStore <T> : NiftyBaseModel
    //================================================
    {
        //for generic sorting
        public class GenericItem
        {
            public string Id { get; set; }
            public int Priority { get; set; }
        }

       // protected readonly IAppContentApi ContentApi = RestService.For<IAppContentApi>(Constants.ServerApp);

        private string ContentKey { get; set; }
        private string StorageContentKey
        {
            get { return "DataStore_"+ContentKey; }
        }
        private string StorageContentVersionKey
        {
            get { return "DataStore_" + ContentKey+"_Version"; }
        }

        
        public GenericDataStore(string contentKey)
        
        {
            ContentKey = contentKey;
        }
     
 

        private bool _hasContent;
        public bool HasContent
        {
            get { return _hasContent; }
            set
            {
                if (_hasContent != value)
                {
                    _hasContent = value;
                    OnPropertyChanged();
                }
            }
        }

        public virtual ObservableRangeCollection<T> SortItems(ObservableRangeCollection<T> sorted)
        {
            return sorted;
        }

      


        //ALL Bindable
        public ObservableRangeCollection<T> Items { get; } = new ObservableRangeCollection<T>();

        //Buffer to read, sort etc
        public ObservableRangeCollection<T> BufferedItems { get; } = new ObservableRangeCollection<T>();

        
    }
}
