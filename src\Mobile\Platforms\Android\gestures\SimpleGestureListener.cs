using Android.Runtime;
using Android.Views;
using AppoMobi.Touch;
using AppoMobi.Touch.Droid.EventArgs;
using System;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace AppoMobi.Touch.Droid
{
	internal class SimpleGestureListener : GestureDetector.SimpleOnGestureListener, IMultiTouchGestureListener
	{
		private IWithTouch element;

		private Android.Views.View view;

		private IGestureListener listener;

		private MotionEvent start;

		private MotionEvent lastPan;

		private bool scrolling;

		private PanEventArgs lastPanArgs;

		private PinchEventArgs previousPinchArgs;

		private RotateEventArgs previousRotateArgs;

		private MotionEvent LastPan
		{
			get
			{
				return this.lastPan;
			}
			set
			{
				MotionEvent motionEvent;
				if (this.lastPan != null)
				{
					this.lastPan.Recycle();
				}
				if (value != null)
				{
					motionEvent = MotionEvent.Obtain(value);
				}
				else
				{
					motionEvent = null;
				}
				this.lastPan = motionEvent;
			}
		}

		private MotionEvent Start
		{
			get
			{
				return this.start;
			}
			set
			{
				MotionEvent motionEvent;
				if (this.start != null)
				{
					this.start.Recycle();
				}
				if (value != null)
				{
					motionEvent = MotionEvent.Obtain(value);
				}
				else
				{
					motionEvent = null;
				}
				this.start = motionEvent;
			}
		}

		internal SimpleGestureListener(IWithTouch element, Android.Views.View view, IGestureListener listener)
		{
			this.element = element;
			this.view = view;
			this.listener = listener;
		}

		internal SimpleGestureListener(IntPtr handle, JniHandleOwnership ownership) : base(handle, ownership)
		{
		}

		public void EndGestures(MotionEvent e)
		{
			if (this.Start != null)
			{
				this.OnPanned(e);
			}
			this.OnPinched(e);
			this.OnRotated(e);
			this.Start = null;
			this.lastPanArgs = null;
			this.LastPan = null;
		}

		public override bool OnDown(MotionEvent e)
		{
			this.Start = e;
			this.scrolling = false;
			return false;
		}

		public override bool OnFling(MotionEvent down, MotionEvent up, float velocityX, float velocityY)
		{
			SimpleGestureListener.u003cu003ec__DisplayClass19_0 variable = null;
			if (this.scrolling)
			{
				int scaledMaximumFlingVelocity = ViewConfiguration.Get(this.view.Context).ScaledMaximumFlingVelocity;
				float single = velocityX / (float)scaledMaximumFlingVelocity;
				float single1 = velocityY / (float)scaledMaximumFlingVelocity;
				bool flag = Math.Abs(single) > AppoMobi.Touch.Droid.Settings.SwipeVelocityThreshold;
				bool flag1 = Math.Abs(single1) > AppoMobi.Touch.Droid.Settings.SwipeVelocityThreshold;
				if (!(flag | flag1) || !this.element.GestureHandler.HandlesSwiped)
				{
					this.OnPanned(up);
				}
				else
				{
					Direction direction = Direction.NotClear;
					if (!flag1)
					{
						direction = (single > 0f ? Direction.Right : Direction.Left);
					}
					else if (!flag)
					{
						direction = (single1 > 0f ? Direction.Down : Direction.Up);
					}
					SwipeEventArgs androidSwipeEventArg = new AndroidSwipeEventArgs(this.LastPan ?? this.Start, up, this.lastPanArgs, this.view, direction);

				    Task.Run(() =>
				    {
				        listener.OnSwiped(androidSwipeEventArg);
				    });
                  

				}
			}
			this.scrolling = false;
			this.lastPanArgs = null;
			this.LastPan = null;
			this.Start = null;
			return false;
		}

		public bool OnMoved(MotionEvent current)
		{
			this.OnPinched(current);
			this.OnRotated(current);
			return false;
		}

		public bool OnMoving(MotionEvent current)
		{
			bool flag = this.OnScroll(this.Start, current, 0f, 0f);
			this.OnPinching(current);
			this.OnRotating(current);
			return flag;
		}

		private void OnPanned(MotionEvent e)
		{
			SimpleGestureListener.u003cu003ec__DisplayClass18_0 variable = null;
			if (this.scrolling)
			{
				if (this.element.GestureHandler.HandlesPanning || this.element.GestureHandler.HandlesPanned)
				{
					AndroidPanEventArgs androidPanEventArg = new AndroidPanEventArgs(this.LastPan ?? this.Start, e, this.lastPanArgs, this.view);
					//Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnPanned(this.args)));
				    Task.Run(() =>
				    {
				        listener.OnPanned(androidPanEventArg);
				    });
                }
				this.scrolling = false;
			}
		}

		private void OnPinched(MotionEvent current)
		{
			SimpleGestureListener.u003cu003ec__DisplayClass26_0 variable = null;
			if (this.previousPinchArgs != null)
			{
				if (this.element.GestureHandler.HandlesPinching || this.element.GestureHandler.HandlesPinched)
				{
					AndroidPinchEventArgs androidPinchEventArg = new AndroidPinchEventArgs(current, this.previousPinchArgs, this.view);
					//Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnPinched(this.pinchArgs)));
				    Task.Run(() =>
				    {
				        listener.OnPinched(androidPinchEventArg);
				    });
                }
				this.previousPinchArgs = null;
			}
		}

		private void OnPinching(MotionEvent current)
		{
			SimpleGestureListener.u003cu003ec__DisplayClass23_0 variable = null;
			AndroidPinchEventArgs androidPinchEventArg = new AndroidPinchEventArgs(current, this.previousPinchArgs, this.view);
			if (this.element.GestureHandler.HandlesPinching)
			{
				//Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnPinching(this.pinchArgs)));
			    Task.Run(() =>
			    {
			        listener.OnPinching(androidPinchEventArg);
			    });
            }
			this.previousPinchArgs = androidPinchEventArg;
		}

		private void OnRotated(MotionEvent current)
		{
 
			if (this.previousRotateArgs != null)
			{
				if (this.element.GestureHandler.HandlesRotating || this.element.GestureHandler.HandlesRotated)
				{
					AndroidRotateEventArgs androidRotateEventArg = new AndroidRotateEventArgs(current, this.previousRotateArgs, this.view);
					//Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnRotated(this.rotateArgs)));
				    Task.Run(() =>
				    {
				        listener.OnRotated(androidRotateEventArg);
				    });
                }
				this.previousRotateArgs = null;
			}
		}

		private void OnRotating(MotionEvent current)
		{
			AndroidRotateEventArgs androidRotateEventArg = new AndroidRotateEventArgs(current, this.previousRotateArgs, this.view);
			if (this.element.GestureHandler.HandlesRotating)
			{
				//Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnRotating(this.rotateArgs)));
			    Task.Run(() =>
			    {
			        listener.OnRotating(androidRotateEventArg);
			    });
            }
			this.previousRotateArgs = androidRotateEventArg;
		}

		public override bool OnScroll(MotionEvent down, MotionEvent move, float distanceX, float distanceY)
		{
		    this.scrolling = true;
			AndroidPanEventArgs androidPanEventArg = new AndroidPanEventArgs(this.LastPan ?? this.Start, move, this.lastPanArgs, this.view);
			if (this.element.GestureHandler.HandlesPanning || this.element.GestureHandler.HandlesPanned)
			{
			//	Task.Run<bool>(new Func<bool>(variable, () => this.u003cu003e4__this.listener.OnPanning(this.args)));

			    Task.Run(() =>
			    {
			        listener.OnPanning(androidPanEventArg);
			    });

            }
			this.lastPanArgs = androidPanEventArg;
			this.LastPan = move;
			return false;
		}


      

        [CompilerGenerated]
        // <>c__DisplayClass18_0
        private sealed class u003cu003ec__DisplayClass18_0
        {
            public AndroidPanEventArgs args;

            // <>4__this
            public SimpleGestureListener u003cu003e4__this;

            public u003cu003ec__DisplayClass18_0()
            {
            }

            // <OnPanned>b__0
            internal bool u003cOnPannedu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnPanned(this.args);
            }
        }

        [CompilerGenerated]
        // <>c__DisplayClass19_0
        private sealed class u003cu003ec__DisplayClass19_0
        {
            public SwipeEventArgs args;

            // <>4__this
            public SimpleGestureListener u003cu003e4__this;

            public u003cu003ec__DisplayClass19_0()
            {
            }

            // <OnFling>b__0
            internal bool u003cOnFlingu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnSwiped(this.args);
            }
        }

        [CompilerGenerated]
        // <>c__DisplayClass23_0
        private sealed class u003cu003ec__DisplayClass23_0
        {
            // <>4__this
            public SimpleGestureListener u003cu003e4__this;

            public AndroidPinchEventArgs pinchArgs;

            public u003cu003ec__DisplayClass23_0()
            {
            }

            // <OnPinching>b__0
            internal bool u003cOnPinchingu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnPinching(this.pinchArgs);
            }
        }

 
        [CompilerGenerated]
        // <>c__DisplayClass26_0
        private sealed class u003cu003ec__DisplayClass26_0
        {
            public AndroidPinchEventArgs pinchArgs;

            // <>4__this
            public SimpleGestureListener u003cu003e4__this;

            public u003cu003ec__DisplayClass26_0()
            {
            }

            // <OnPinched>b__0
            internal bool u003cOnPinchedu003eb__0()
            {
                return this.u003cu003e4__this.listener.OnPinched(this.pinchArgs);
            }
        }

 
    }
}