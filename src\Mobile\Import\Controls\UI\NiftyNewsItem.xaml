﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyNewsItem"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui">
    <ContentView.Content>
        <StackLayout Spacing="0">

            <appoMobi:CardView
                Margin="0,0,0,4"
                BackgroundColor="#fdfefe"
                VerticalOptions="StartAndExpand">
                <StackLayout HorizontalOptions="FillAndExpand" Spacing="0">
                    <appoMobi:HeaderDivider />

                    <!--  NEWS TEXT  -->
                    <Grid
                        ColumnSpacing="0"
                        RowSpacing="0"
                        VerticalOptions="StartAndExpand">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Label
                            x:Name="NewsText"
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="10,16,10,16"
                            FontSize="14"
                            StyleId="text"
                            Text="{Binding Text}"
                            TextColor="{x:Static appoMobi:AppColors.BwGrey}" />


                        <!--  SHARE ICON  -->
                        <ContentView
                            Grid.Row="0"
                            Grid.Column="1"
                            Padding="0,3,16,4"
                            VerticalOptions="FillAndExpand">

                            <appoMobi:CImage
                                Grid.Column="1"
                                HorizontalOptions="FillAndExpand"
                                Source="sharew"
                                StyleId="share"
                                VerticalOptions="FillAndExpand"
                                WidthRequest="25">

                                <appoMobi:CImage.GestureRecognizers>
                                    <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="News_Touch_ShareIconTap" />
                                </appoMobi:CImage.GestureRecognizers>

                                <appoMobi:CImage.Transformations>
                                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_LinkIcon}" />
                                </appoMobi:CImage.Transformations>
                            </appoMobi:CImage>
                        </ContentView>
                        <appoMobi:CImage
                            Grid.Row="1"
                            Grid.Column="0"
                            Grid.ColumnSpan="2"
                            Aspect="AspectFit"
                            ClassId="{Binding Id}"
                            Error="Card_OnError"
                            ErrorPlaceholder="sad.png"
                            FadeAnimationEnabled="False"
                            HeightRequest="{Binding ImageDynamicHeight}"
                            HorizontalOptions="FillAndExpand"
                            RetryCount="10"
                            Source="{Binding Image}"
                            StyleId="image"
                            Success="CachedImage_OnSuccess">
                            <!--<appoMobi:CImage.GestureRecognizers>
                                                <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="Banner_OnTapped" />
                                            </appoMobi:CImage.GestureRecognizers>-->
                        </appoMobi:CImage>
                    </Grid>

                    <!--  NEWS IMAGE  -->



                    <!--<StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="ImageGrid_OnTapped" />
                                        </StackLayout.GestureRecognizers>-->




                </StackLayout>
            </appoMobi:CardView>
        </StackLayout>
    </ContentView.Content>
</ContentView>