﻿using System;
using FFImageLoading.Maui;



namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NiftyNewsItem : ContentView
    {
        public NiftyNewsItem()
        {
            InitializeComponent();
        }

        private void Card_OnError(object sender, CachedImageEvents.ErrorEventArgs e)
        {
            
        }

        private void CachedImage_OnSuccess(object sender, CachedImageEvents.SuccessEventArgs e)
        {
             
        }

        private void News_Touch_ShareIconTap(object sender, EventArgs e)
        {
              
        }
    }
}