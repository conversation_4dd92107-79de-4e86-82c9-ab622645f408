﻿using SkiaSharp;
 
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using FFImageLoading;
using SkiaSharp.Views.Maui;
using SkiaSharp.Views.Maui.Controls;
 

namespace AppoMobi.Forms.Controls.Skia
{


	public class SkiaImage : ContentView, IDisposable
	{
		public event EventHandler<ContentLoadedEventArgs> OnError;
		public event EventHandler<ContentLoadedEventArgs> OnSuccess;
		public event EventHandler<ContentLoadedEventArgs> OnFinishedLoading;

		/// <summary>
		/// Silly fast fix hack for iOS
		/// todo better
		/// </summary>
		public double SensorRotation { get; set; }

		private readonly SKCanvasView _canvasView = new SKCanvasView
		{
			BackgroundColor = Colors.Transparent
		};

		public void Dispose()
		{
			_canvasView.PaintSurface -= CanvasViewOnPaintSurface;
			Bitmap?.Dispose();
			ImageSource = null;
		}


		public SkiaImage()
		{

			HorizontalOptions = LayoutOptions.Start;
			VerticalOptions = LayoutOptions.Start;
			Padding = new Thickness(0);

			var assembly = Assembly.GetExecutingAssembly();
			_part1 = assembly.GetName().Name + $".Images.";

			// Thanks to TheMax for pointing out that on mobile, the icon will have a shadow by default.
			// Also it has a white background, which we might not want.
			//HasShadow = false;

			BackgroundColor = Colors.Transparent;

			Content = _canvasView;

			_canvasView.PaintSurface += CanvasViewOnPaintSurface;
		}

		#region PROPERTIES

		//-------------------------------------------------------------
		// Effect
		//-------------------------------------------------------------
		private const string nameEffect = "Effect";
		public static readonly BindableProperty EffectProperty = BindableProperty.Create(nameEffect, typeof(SkiaImageEffect), typeof(SkiaImage),
			SkiaImageEffect.None,
			propertyChanged: RedrawCanvas);
		public SkiaImageEffect Effect
		{
			get { return (SkiaImageEffect)GetValue(EffectProperty); }
			set { SetValue(EffectProperty, value); }
		}


		//-------------------------------------------------------------
		// ZoomX
		//-------------------------------------------------------------
		private const string nameZoomX = "ZoomX";
		public static readonly BindableProperty ZoomXProperty = BindableProperty.Create(nameZoomX, typeof(double), typeof(SkiaImage), 1.0); //, BindingMode.TwoWay
		public double ZoomX
		{
			get { return (double)GetValue(ZoomXProperty); }
			set { SetValue(ZoomXProperty, value); }
		}

		//-------------------------------------------------------------
		// ZoomY
		//-------------------------------------------------------------
		private const string nameZoomY = "ZoomY";
		public static readonly BindableProperty ZoomYProperty = BindableProperty.Create(nameZoomY, typeof(double), typeof(SkiaImage), 1.0); //, BindingMode.TwoWay
		public double ZoomY
		{
			get { return (double)GetValue(ZoomYProperty); }
			set { SetValue(ZoomYProperty, value); }
		}


		//-------------------------------------------------------------
		// DarkenAmount
		//-------------------------------------------------------------
		private const string nameDarkenAmount = "DarkenAmount";
		public static readonly BindableProperty DarkenAmountProperty = BindableProperty.Create(nameDarkenAmount, typeof(double), typeof(SkiaImage),
			5.0,
			propertyChanged: RedrawCanvas);
		public double DarkenAmount
		{
			get { return (double)GetValue(DarkenAmountProperty); }
			set { SetValue(DarkenAmountProperty, value); }
		}

		//-------------------------------------------------------------
		// BlurAmount
		//-------------------------------------------------------------
		private const string nameBlurAmount = "BlurAmount";
		public static readonly BindableProperty BlurAmountProperty = BindableProperty.Create(nameBlurAmount, typeof(double), typeof(SkiaImage),
			0.0,
			propertyChanged: RedrawCanvas);
		public double BlurAmount
		{
			get { return (double)GetValue(BlurAmountProperty); }
			set { SetValue(BlurAmountProperty, value); }
		}

		//-------------------------------------------------------------
		// InflateAmount
		//-------------------------------------------------------------
		private const string nameInflateAmount = "InflateAmount";
		public static readonly BindableProperty InflateAmountProperty = BindableProperty.Create(nameInflateAmount, typeof(double), typeof(SkiaImage),
			0.0,
			propertyChanged: RedrawCanvas);
		public double InflateAmount
		{
			get { return (double)GetValue(InflateAmountProperty); }
			set { SetValue(InflateAmountProperty, value); }
		}

		//-------------------------------------------------------------
		// UseGradient
		//-------------------------------------------------------------
		private const string nameUseGradient = "UseGradient";
		public static readonly BindableProperty UseGradientProperty = BindableProperty.Create(nameUseGradient, typeof(bool), typeof(SkiaImage), false,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public bool UseGradient
		{
			get { return (bool)GetValue(UseGradientProperty); }
			set { SetValue(UseGradientProperty, value); }
		}


		//-------------------------------------------------------------
		// StartColor
		//-------------------------------------------------------------
		private const string nameStartColor = "StartColor";
		public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(SkiaImage),
			Colors.DarkGray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color StartColor
		{
			get { return (Color)GetValue(StartColorProperty); }
			set { SetValue(StartColorProperty, value); }
		}

		//-------------------------------------------------------------
		// EndColor
		//-------------------------------------------------------------
		private const string nameEndColor = "EndColor";
		public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(SkiaImage),
			Colors.Gray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color EndColor
		{
			get { return (Color)GetValue(EndColorProperty); }
			set { SetValue(EndColorProperty, value); }
		}

		//-------------------------------------------------------------
		// TintColor
		//-------------------------------------------------------------
		private const string nameTintColor = "TintColor";
		public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(SkiaImage),
				Colors.Transparent,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color TintColor
		{
			get { return (Color)GetValue(TintColorProperty); }
			set { SetValue(TintColorProperty, value); }
		}

		//-------------------------------------------------------------
		// EffectBlendMode
		//-------------------------------------------------------------
		private const string nameEffectBlendMode = "EffectBlendMode";
		public static readonly BindableProperty EffectBlendModeProperty = BindableProperty.Create(nameEffectBlendMode, typeof(SKBlendMode), typeof(SkiaImage),
			SKBlendMode.SrcIn,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay

		//-------------------------------------------------------------
		public SKBlendMode EffectBlendMode
		//-------------------------------------------------------------
		{
			get { return (SKBlendMode)GetValue(EffectBlendModeProperty); }
			set { SetValue(EffectBlendModeProperty, value); }
		}


		//-------------------------------------------------------------
		// HorizontalOffset
		//-------------------------------------------------------------
		private const string nameHorizontalOffset = "HorizontalOffset";
		public static readonly BindableProperty HorizontalOffsetProperty = BindableProperty.Create(nameHorizontalOffset, typeof(double), typeof(SkiaImage), 0.0,
			propertyChanged: RedrawCanvas);
		public double HorizontalOffset
		{
			get { return (double)GetValue(HorizontalOffsetProperty); }
			set { SetValue(HorizontalOffsetProperty, value); }
		}



		#endregion


		public void Render()
		{

		}

		//-------------------------------------------------------------
		private void CanvasViewOnPaintSurface(object sender, SKPaintSurfaceEventArgs args)
		//-------------------------------------------------------------
		{
			SKCanvas canvas = args.Surface.Canvas;

			canvas.Clear(SKColors.Transparent);

			if (Bitmap == null)
				return;

			SKImageInfo info = args.Info;

			RenderImage(info, canvas);

		}

		private SKBitmap _bitmap;
		public SKBitmap Bitmap
		{
			get
			{
				return _bitmap;
			}
			set
			{
				_bitmap = value;
			}
		}

		//-------------------------------------------------------------
		// UseAssembly
		//-------------------------------------------------------------
		private const string nameUseAssembly = "UseAssembly";
		public static readonly BindableProperty UseAssemblyProperty = BindableProperty.Create(nameUseAssembly, typeof(object), typeof(SkiaImage),
			null);
		public object UseAssembly
		{
			get { return (object)GetValue(UseAssemblyProperty); }
			set { SetValue(UseAssemblyProperty, value); }
		}

		public void Update()
		{
			if (IsVisible)
			{
				MainThread.BeginInvokeOnMainThread(() =>
				{
					this._canvasView.InvalidateSurface();
				});
			}
		}

		//-------------------------------------------------------------
		protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
		//-------------------------------------------------------------
		{
			base.OnPropertyChanged(propertyName);

			if (propertyName == "ImageSource")
			{
				if (!string.IsNullOrEmpty(ImageSource))
				{
					if (ImageSource.ToLower().Contains("http"))
						UpdateImageFromUrl();
					else
						UpdateImageFromFile();

					if (Bitmap != null)
					{
						Update();
						OnSuccess?.Invoke(this, new ContentLoadedEventArgs(ImageSource));
					}
					else
					{
						Console.WriteLine($"[SkiaImage] Error loading {ImageSource}");
						OnError?.Invoke(this, new ContentLoadedEventArgs(ImageSource));
					}

					OnFinishedLoading?.Invoke(this, new ContentLoadedEventArgs(ImageSource));
				}
				else
				{
					//empty image
					Bitmap = null;
					_canvasView?.InvalidateSurface();
				}
			}


		}

		protected async void UpdateImageFromUrl()
		{
			if (string.IsNullOrEmpty(ImageSource))
			{
				Bitmap = null;
				return;
			}

			try
			{

				var stream = await ImageService.Instance.LoadUrl(ImageSource).AsJPGStreamAsync(ImageService.Instance);//.AsUIImageAsync();
				Bitmap = SKBitmap.Decode(stream);

				//if (_myClient == null)
				//    _myClient = CreateClient();
				//var bytes = await _myClient.GetByteArrayAsync(ImageSource);
				//using (var stream = new MemoryStream(bytes))
				//{
				//    // decode the bitmap stream
				//    image = SKBitmap.Decode(stream);
				//}
				Debug.WriteLine($"[SKIAIMAGE] success loading {ImageSource}");

				_canvasView?.InvalidateSurface();
			}
			catch (Exception e)
			{
				Console.WriteLine($"[SKIAIMAGE] Couldn't load fom url {ImageSource}");

				Console.WriteLine(e);
			}


		}

		private static SKBitmap AutoOrient(SKBitmap bitmap, SKEncodedOrigin origin)
		{
			SKBitmap rotated;
			switch (origin)
			{
				case SKEncodedOrigin.BottomRight:
					using (var surface = new SKCanvas(bitmap))
					{
						surface.RotateDegrees(180, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
						surface.DrawBitmap(bitmap.Copy(), 0, 0);
					}
					return bitmap;
				case SKEncodedOrigin.RightTop:
					rotated = new SKBitmap(bitmap.Height, bitmap.Width);
					using (var surface = new SKCanvas(rotated))
					{
						surface.Translate(rotated.Width, 0);
						surface.RotateDegrees(90);
						surface.DrawBitmap(bitmap, 0, 0);
					}
					return rotated;
				case SKEncodedOrigin.LeftBottom:
					rotated = new SKBitmap(bitmap.Height, bitmap.Width);
					using (var surface = new SKCanvas(rotated))
					{
						surface.Translate(0, rotated.Height);
						surface.RotateDegrees(270);
						surface.DrawBitmap(bitmap, 0, 0);
					}
					return rotated;
				default:
					return bitmap;
			}
		}

		protected void UpdateImageFromFile()
		{
			if (string.IsNullOrEmpty(ImageSource))
			{
				Bitmap = null;
				return;
			}

			var fullname = _part1 + ImageSource;


			bool loaded = false;

			//try load from assembly resources

			using (var fileStream = GetType().Assembly.GetManifestResourceStream(fullname))
			{
				if (fileStream != null)
				{
					try
					{
						using (var stream = new SKManagedStream(fileStream))
						{
							Bitmap = SKBitmap.Decode(stream);

							//bitmap = AutoOrient(unrotated, unrotated.Info.e)

							if (Bitmap == null)
							{
								Console.WriteLine($"[SKIAIMAGE] Couldn't decode {fullname}");
								return;
							}
						}

						Debug.WriteLine($"[SKIAIMAGE] success loading {fullname}");

						_canvasView?.InvalidateSurface();
					}
					catch (Exception e)
					{
						Console.WriteLine(e);
					}
				}
				else
				{
					Bitmap = null;
					Console.WriteLine($"[SKIAIMAGE] Couldn't load {fullname}, trying local..");

					try
					{
						fullname = ImageSource;

						var codec = SKCodec.Create(fullname);
						var orientation = SKEncodedOrigin.Default;
						if (codec != null)
							orientation = codec.EncodedOrigin;

						using (var stream = File.OpenRead(fullname))
						{

							//SKCodec codec = SKCodec.Create(fullname);
							//SKImageInfo info = codec.Info;
							//var o = info.o


							//bug https://github.com/mono/SkiaSharp/issues/1621
							SKImage img = SKImage.FromEncodedData(stream);
							var bitmap = SKBitmap.FromImage(img);

							//var bitmap = SKBitmap.Decode(stream);


							if (bitmap == null)
							{
								Console.WriteLine($"[SKIAIMAGE] Couldn't decode {fullname}");
								return;
							}

							if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
							{
								//var w = Bitmap.Width;
								//var h = Bitmap.Height;
								//  Bitmap = bitmap;

								if (orientation == SKEncodedOrigin.Default)
								{
									Bitmap = RotateSKBitmap(bitmap, (float)SensorRotation);
								}
								else
								{
									Bitmap = HandleOrientation(bitmap, orientation);
								}

								//w = Bitmap.Width;
								//h = Bitmap.Height;

							}
							else
							{
								Bitmap = bitmap;
							}
						}

						Debug.WriteLine($"[SKIAIMAGE] success loading {fullname}");

						_canvasView?.InvalidateSurface();
					}
					catch (Exception e)
					{
						Console.WriteLine(e);
					}


					return;
				}
			}

		}

		public static SKBitmap RotateSKBitmap(SKBitmap bitmap, float orientation)
		{
			using (var surface = new SKCanvas(bitmap))
			{
				surface.RotateDegrees(orientation, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
				surface.DrawBitmap(bitmap.Copy(), 0, 0);
			}

			return bitmap;
		}

		public static SKBitmap RotateSKBitmapSpecial(SKBitmap bitmap, double orientation)
		{
			var rotation = 0;
			switch (orientation)
			{
				case 90:
					rotation = 90;
					break;

				case 180:
					rotation = 270;
					break;

				case 270:
					rotation = 180;
					break;

				default:
					return bitmap;
			}

			using (var surface = new SKCanvas(bitmap))
			{
				surface.RotateDegrees(rotation, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
				surface.DrawBitmap(bitmap.Copy(), 0, 0);
			}

			return bitmap;
		}

		public static SKBitmap HandleOrientation(SKBitmap bitmap, SKEncodedOrigin orientation)
		{
			SKBitmap rotated;
			switch (orientation)
			{
				case SKEncodedOrigin.BottomRight:

					using (var surface = new SKCanvas(bitmap))
					{
						surface.RotateDegrees(180, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
						surface.DrawBitmap(bitmap.Copy(), 0, 0);
					}

					return bitmap;

				case SKEncodedOrigin.RightTop:
					rotated = new SKBitmap(bitmap.Height, bitmap.Width);

					using (var surface = new SKCanvas(rotated))
					{
						surface.Translate(rotated.Width, 0);
						surface.RotateDegrees(90);
						surface.DrawBitmap(bitmap, 0, 0);
					}

					return rotated;

				case SKEncodedOrigin.LeftBottom:
					rotated = new SKBitmap(bitmap.Height, bitmap.Width);

					using (var surface = new SKCanvas(rotated))
					{
						surface.Translate(0, rotated.Height);
						surface.RotateDegrees(270);
						surface.DrawBitmap(bitmap, 0, 0);
					}

					return rotated;

				default:
					return bitmap;
			}
		}

		//-------------------------------------------------------------
		// Aspect
		//-------------------------------------------------------------
		private const string nameAspect = "Aspect";
		public static readonly BindableProperty AspectProperty = BindableProperty.Create(nameAspect, typeof(BitmapStretch), typeof(SkiaImage),
			BitmapStretch.Fill,
			propertyChanged: RedrawCanvas);
		public BitmapStretch Aspect
		{
			get { return (BitmapStretch)GetValue(AspectProperty); }
			set { SetValue(AspectProperty, value); }
		}

		//-------------------------------------------------------------
		// VerticalAlignment
		//-------------------------------------------------------------
		private const string nameVerticalAlignment = "VerticalAlignment";
		public static readonly BindableProperty VerticalAlignmentProperty = BindableProperty.Create(nameVerticalAlignment, typeof(BitmapAlignment), typeof(SkiaImage),
			BitmapAlignment.Center,
			propertyChanged: RedrawCanvas);
		public BitmapAlignment VerticalAlignment
		{
			get { return (BitmapAlignment)GetValue(VerticalAlignmentProperty); }
			set { SetValue(VerticalAlignmentProperty, value); }
		}

		//-------------------------------------------------------------
		// HorizontalAlignment
		//-------------------------------------------------------------
		private const string nameHorizontalAlignment = "HorizontalAlignment";
		public static readonly BindableProperty HorizontalAlignmentProperty = BindableProperty.Create(nameHorizontalAlignment, typeof(BitmapAlignment), typeof(SkiaImage),
			BitmapAlignment.Center,
			propertyChanged: RedrawCanvas);
		public BitmapAlignment HorizontalAlignment
		{
			get { return (BitmapAlignment)GetValue(HorizontalAlignmentProperty); }
			set { SetValue(HorizontalAlignmentProperty, value); }
		}

		public static readonly BindableProperty ImageSourceProperty = BindableProperty.Create(
			nameof(ImageSource),
			typeof(string),
			typeof(SkiaImage),
			default(string),
			propertyChanged: RedrawCanvas);

		private string _part1;



		public string ImageSource
		{
			get => (string)GetValue(ImageSourceProperty);
			set => SetValue(ImageSourceProperty, value);
		}

		private static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)
		{
			SkiaImage svgIcon = bindable as SkiaImage;
			svgIcon?._canvasView.InvalidateSurface();
		}

		#region RENDERiNG

		public void RenderImage(SKImageInfo info, SKCanvas canvas)
		{
			using (SKPaint paint = new SKPaint())
			{

				//MATRIX
				// https://docs.microsoft.com/en-us/xamarin/xamarin-forms/user-interface/graphics/skiasharp/effects/color-filters


				paint.ImageFilter = SKImageFilter
					.CreateBlur((float)BlurAmount, (float)BlurAmount);

				if (Effect == SkiaImageEffect.Tint &&
					(TintColor != null && TintColor != Colors.Transparent && TintColor != Colors.Transparent))
				{
					//todo tint
					paint.ColorFilter =
						SKColorFilter.CreateColorMatrix(new float[]
						{
                            //row-major matrix
                            // R G       B        A  +Ttranslation
                            1, 0, 0, 0, (float)TintColor.Red*255, // R result
                            0, 1, 0, 0, (float)TintColor.Green*255, // G result
                            0, 0, 1, 0, (float)TintColor.Blue*255, // B result
                            0, 0, 0, 1, 0 // A result
                        });
				}
				else
				if (Effect == SkiaImageEffect.Darken &&
					DarkenAmount != 0.0)
				{
					paint.ColorFilter =
						SKColorFilter.CreateColorMatrix(new float[]
						{
                            //row-major matrix
                            // R G       B        A  +Ttranslation
                            1, 0, 0, 0, -(float)DarkenAmount*255, // R result
                            0, 1, 0, 0, -(float)DarkenAmount*255, // G result
                            0, 0, 1, 0, -(float)DarkenAmount*255, // B result
                            0, 0, 0, 1, 0 // A result
                        });
				}
				else if (Effect == SkiaImageEffect.BlackAndWhite)
				{
					paint.ColorFilter =
						SKColorFilter.CreateColorMatrix(new float[]
						{
                            //row-major matrix
                            // R     G       B        A  +Ttranslation
                            0.21f, 0.72f, 0.07f, 0, 0, // R result
                            0.21f, 0.72f, 0.07f, 0, 0, // G result
                            0.21f, 0.72f, 0.07f, 0, 0, // B result
                            0,       0,       0,       1,  0 // A result
                        });
				}
				else
				if (Effect == SkiaImageEffect.Pastel)
				{
					paint.ColorFilter =
						SKColorFilter.CreateColorMatrix(new float[]
						{
							0.75f, 0.25f, 0.25f, 0, 0,
							0.25f, 0.75f, 0.25f, 0, 0,
							0.25f, 0.25f, 0.75f, 0, 0,
							0, 0, 0, 1, 0
						});
				}

				SKRect dest = new SKRect(0, 0, info.Width, info.Height);


				BitmapStretch stretch = Aspect;
				BitmapAlignment horizontal = HorizontalAlignment;
				BitmapAlignment vertical = VerticalAlignment;

				DrawBitmap(canvas, Bitmap, dest, stretch, horizontal, vertical, paint);
			}
		}

		// https://docs.microsoft.com/en-us/xamarin/xamarin-forms/user-interface/graphics/skiasharp/bitmaps/displaying

		protected void DrawBitmap(SKCanvas canvas, SKBitmap bitmap, SKRect dest,
			BitmapStretch stretch,
			BitmapAlignment horizontal = BitmapAlignment.Center,
			BitmapAlignment vertical = BitmapAlignment.Center,
			SKPaint paint = null)
		{
			if (stretch == BitmapStretch.Fill)
			{
				//SKRect display = CalculateDisplayRect(dest, (float)ZoomX * dest.Width, (float)ZoomY * dest.Height,
				//    horizontal, vertical);

				canvas.DrawBitmap(bitmap, dest, paint);
			}
			else
			{
				float scaleX = 1;
				float scaleY = 1;

				switch (stretch)
				{
					case BitmapStretch.None:
						break;

					case BitmapStretch.Fill:
						scaleX = dest.Width / bitmap.Width;
						scaleY = dest.Height / bitmap.Height;
						break;


					case BitmapStretch.Uniform:
						scaleX = Math.Min(dest.Width / bitmap.Width, dest.Height / bitmap.Height);
						scaleY = scaleX;
						break;

					case BitmapStretch.UniformToFill:

						//var aspect = dest.Height / dest.Width;
						//var aspectTexture = (float)bitmap.Height / (float)bitmap.Width;
						//var k = (float)bitmap.Width / (float)dest.Width; 
						//var kkk = (aspect - 1) / k + 1;

						var s1 = dest.Width / bitmap.Width;
						var s2 = dest.Height / bitmap.Height;
						//var check = s1 * s2;

						scaleX = Math.Max(s1, s2);
						scaleY = scaleX;
						break;

					case BitmapStretch.AspectFitFill:

						if (bitmap.Width < bitmap.Height)
						{
							scaleX = dest.Height / bitmap.Height;
						}
						else
						{
							scaleX = dest.Width / bitmap.Width;
						}

						scaleY = scaleX;
						break;
				}

				scaleX *= (float)ZoomX;
				scaleY *= (float)ZoomY;

				MaxWidth = PixelsToDeviceUnits(scaleX * bitmap.Width);

				SKRect display = CalculateDisplayRect(dest, scaleX * bitmap.Width, scaleY * bitmap.Height,
					horizontal, vertical);

				//if (this.BlurAmount > 0)
				display.Inflate(new SKSize((float)InflateAmount, (float)InflateAmount));

				display.Offset((float)DeviceUnitsToPixels(HorizontalOffset), 0);

				canvas.DrawBitmap(bitmap, display, paint);
			}
		}


		#endregion

		public double MaxWidth { get; protected set; }

		protected int DeviceUnitsToPixels(double units)
		{
			return (int)(units * Core.DisplayDensity);
		}

		protected double PixelsToDeviceUnits(double units)
		{
			return units / Super.Screen.Density;
		}


		public static SKRect CalculateDisplayRect(SKRect dest, float bmpWidth, float bmpHeight,
			BitmapAlignment horizontal, BitmapAlignment vertical)
		{
			float x = 0;
			float y = 0;

			switch (horizontal)
			{
				case BitmapAlignment.Center:
					x = (dest.Width - bmpWidth) / 2.0f;
					break;

				case BitmapAlignment.Start:
					break;

				case BitmapAlignment.End:
					x = dest.Width - bmpWidth;
					break;
			}

			switch (vertical)
			{
				case BitmapAlignment.Center:
					y = (dest.Height - bmpHeight) / 2.0f;
					break;

				case BitmapAlignment.Start:
					break;

				case BitmapAlignment.End:
					y = dest.Height - bmpHeight;
					break;
			}

			x += dest.Left;
			y += dest.Top;

			return new SKRect(x, y, x + bmpWidth, y + bmpHeight);
		}

	}


	public enum SkiaImageEffect
	{
		None,
		BlackAndWhite,
		Pastel,
		Tint,
		Darken
	}

	public enum BitmapAlignment
	{
		Start,
		Center,
		End
	}

	public enum BitmapStretch
	{
		None,
		Fill,
		Uniform,
		UniformToFill,
		AspectFit = Uniform,
		AspectFill = UniformToFill,
		AspectFitFill
	}


}
