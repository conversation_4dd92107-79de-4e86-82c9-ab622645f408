﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Timers;
using Timer = System.Timers.Timer;


namespace AppoMobi.Models
{
    //****************************************************
    public class Countdown : INotifyPropertyChanged
    //****************************************************
    {
        /// <summary>
        /// Gets the start date time.
        /// </summary>
        public DateTime StartDateTime { get; private set; }

        /// <summary>
        /// Gets the remain time in seconds.
        /// </summary>
        public double RemainTime
        {
            get { return remainTime; }

            private set
            {
                remainTime = value;
                OnPropertyChanged();
                OnPropertyChanged("RemainTimeSecondsText");  
            }
        }

        /// <summary>
        /// Gets the remain time in seconds.
        /// </summary>
        public string RemainTimeSecondsText
        {
            get
            {
                secondsToExplain = (int) remainTime;
                OnPropertyChanged("RemainTimeExplainSeconds");
                if (secondsToExplain < 1.0) return "0";
                return secondsToExplain.ToString("##");
            }
        }


        private int secondsToExplain { get; set; }
        
        public string RemainTimeExplainSeconds
        
        {
            get
            {
                var seconds = secondsToExplain.ExplainToString(
                    ResStrings.ExplainSeconds_0,
                    ResStrings.ExplainSeconds_1,
                    ResStrings.ExplainSeconds_X1,
                    ResStrings.ExplainSeconds_X2,
                    ResStrings.ExplainSeconds_X);

                //if (remainTime < 1.0) return "0";
                return " "+seconds;
            }
        }

        /// <summary>
        /// Occurs when completed.
        /// </summary>
        public event Action Completed;

        /// <summary>
        /// Occurs when ticked.
        /// </summary>
        public event Action Ticked;

        /// <summary>
        /// The timer.
        /// </summary>
        Timer timer;

        /// <summary>
        /// The remain time.
        /// </summary>
        double remainTime;

        /// <summary>
        /// The remain time total.
        /// </summary>
        double remainTimeTotal;

        //
        //public void StartUpdating(double total, Action actionOnComplete, double period = 1.0)
        //
        //{
        //    Completed = actionOnComplete;
        //    StartUpdating(total, period);
        //}

        
        /// <summary>
        /// Starts the updating with specified period, total time and period are specified in seconds.
        /// </summary>
        
        public void StartUpdating(double total, double period = 1.0)
        
        {
            if (timer != null)
            {
                StopUpdating();
            }

            remainTimeTotal = total;
            RemainTime = total;

            StartDateTime = DateTime.Now;

            timer = new Timer(period * 1000);
            //timer = new Timer();
            timer.Elapsed += (sender, e) => Tick();
            timer.Enabled = true;
        }

        
        /// <summary>
        /// Stops the updating.
        /// </summary>
        
        public void StopUpdating()
        
        {
            //RemainTime = 0;
            remainTimeTotal = 0;

            if (timer != null)
            {
                timer.Enabled = false;
                timer = null;
            }
        }

        /// <summary>
        /// Updates the time remain.
        /// </summary>
        
        public void Tick()
        
        {
            var delta = (DateTime.Now - StartDateTime).TotalSeconds;

            if (delta < remainTimeTotal)
            {
                RemainTime = remainTimeTotal - delta;

                var ticked = Ticked;
                if (ticked != null)
                {
                    ticked();
                }
            }
            else
            {
                StopUpdating();

                RemainTime = 0;

                var completed = Completed;
                if (completed != null)
                {
                    completed();
                }
            }
        }

        #region INotifyPropertyChanged implementation

        /// <summary>
        /// Occurs when property changed.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the property changed event.
        /// </summary>
        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null)
            {
                handler(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        #endregion
    }
}
