﻿<?xml version="1.0" encoding="utf-8"?>

<xam:PopupDialogBase
    x:Class="AppoMobi.Xam.PopupOptionsListView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Center"
    Color="Transparent">

    <!--<pages:PopupPage.Animation>
        <animations1:MoveAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"

            PositionIn="Center"
            PositionOut="Center" />
    </pages:PopupPage.Animation>-->

    <ContentPage.Content>

        <xam:GesturesGrid
            HorizontalOptions="FillAndExpand"
            Tapped="Grid_OnDown"
            VerticalOptions="FillAndExpand">

            <xam:GesturesFrame
                x:Name="cardInterface"
                Margin="20,80,20,20"
                Padding="0,8,0,8"
                BackgroundColor="{x:Static xam:BackColors.OptionLine}"
                HorizontalOptions="Center"
                Stroke="{x:Static xam:TextColors.Placeholder}"
                Tapped="CardInterface_OnDown"
                VerticalOptions="Start">

                <StackLayout Spacing="0">

                    <StackLayout Spacing="0">


                        <xam:NiftyDataStack
                            x:Name="DataStack"
                            Margin="0,8,0,8"
                            ItemsSource="{Binding MenuList}"
                            Spacing="0.5"
                            Tag="Popup"
                            VerticalOptions="Start">
                            <xam:NiftyDataStack.ItemTemplate>
                                <DataTemplate>

                                    <xam:CellOptionsList DownCommandParameter="{Binding .}" ProcessTap="OnChildTapped" />

                                </DataTemplate>
                            </xam:NiftyDataStack.ItemTemplate>


                        </xam:NiftyDataStack>


                    </StackLayout>


                </StackLayout>
            </xam:GesturesFrame>


        </xam:GesturesGrid>


    </ContentPage.Content>

</xam:PopupDialogBase>