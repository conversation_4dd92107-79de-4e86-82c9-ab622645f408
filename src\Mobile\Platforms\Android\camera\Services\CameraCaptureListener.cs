using Android.Hardware.Camera2;
using Android.Media;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Droid.Camera.Views;
using AppoMobi.Forms.Content.Camera.Controls;
using Java.Lang;
using Console = System.Console;
using Exception = Java.Lang.Exception;
using Image = Android.Media.Image;

namespace AppoMobi.Droid.Camera.Services
{

    // NATIVE
	public class CameraCaptureListener : CameraCaptureSession.CaptureCallback,
		Android.Media.ImageReader.IOnImageAvailableListener
	{
		private readonly CameraFragment owner;

		public CameraCaptureListener(CameraFragment owner)
		{
			if (owner == null)
				throw new System.ArgumentNullException("owner");
			this.owner = owner;
		}

        private TaskCompletionSource<BrightnessResult> _brightnessResultHandler;
        private bool _isMeasuringBrightness;

        public void SetBrightnessMeasurementHandler(TaskCompletionSource<BrightnessResult> handler)
        {
            _brightnessResultHandler = handler;
            _isMeasuringBrightness = true;
        }

        public override void OnCaptureCompleted(CameraCaptureSession session, CaptureRequest request, TotalCaptureResult result)
		{
			Process(result);
		}

		public override void OnCaptureProgressed(CameraCaptureSession session, CaptureRequest request, CaptureResult partialResult)
		{
			Process(partialResult);
		}

		public void OnImageAvailable(ImageReader reader)
		{
			//todo STILL IMAGE CAPTURE

			Image image = null;
			try
			{


				System.Diagnostics.Debug.WriteLine("[CAMERA] AcquireLatestImage..");

				image = reader.AcquireNextImage();
				if (image != null)
				{

					/*
                    #region JPEG

                    var buffer = image.GetPlanes()[0].Buffer;
                    var bytes = new byte[buffer.Remaining()];
                    buffer.Get(bytes);


                    //File musicDirectory = new File(owner.Context.GetExternalFilesDir(Environment.DirectoryDcim));
                    var jFolder = new Java.IO.File(Environment.GetExternalStoragePublicDirectory(Environment.DirectoryDcim),"Camera");
                    //var jFolder = new Java.IO.File(owner.Context.GetExternalFilesDir(Environment.DirectoryDcim), "Camera");
                    if (!jFolder.Exists())
                        jFolder.Mkdirs();

                    var filename = owner.GenerateJpgFileName();

                    var jFile = new Java.IO.File(jFolder, filename);

                    using (var output = new FileOutputStream(jFile))
                    {
                        output.Write(bytes);
                        output.Close();
                    }

                    #endregion
                    */

					System.Diagnostics.Debug.WriteLine("[CAMERA] Processing still image..");

					var ok = owner.SaveImageFromYUV(image);


                    //if (ok)
                    //    owner.ShowToast(ResStrings.Success_);


                    #region NV21

                    #endregion

                    //MediaStore.Images.Media.InsertImage(owner.Context.ContentResolver, jFile.AbsolutePath, filename, null);

                    owner.OnCaptureSuccess();
                }
			}
			catch (Exception e)
			{
				Console.WriteLine(e.Message);

				owner.ShowToast(ResStrings.Error);

				if (owner.CaptureLocation == CaptureLocationType.Bitmap)
				{
					owner.CapturedImage?.Invoke(null);
				}

                owner.OnCaptureError(e);
            }
			finally
			{
				if (image != null)
				{
					image.Close();
				}
				System.Diagnostics.Debug.WriteLine("[CAMERA] Still capture finished");
				owner.CapturingStill = false;
			}

		}

		private void Process(CaptureResult result)
		{
            // Handle brightness measurement if active
            if (_isMeasuringBrightness && _brightnessResultHandler != null)
            {
                try
                {
                    // Get auto exposure values chosen by camera
                    var measuredISO = (int)result.Get(CaptureResult.SensorSensitivity);
                    var measuredExposureTime = (long)result.Get(CaptureResult.SensorExposureTime); // nanoseconds
                    var measuredAperture = (float)result.Get(CaptureResult.LensAperture);

                    // Convert exposure time to seconds
                    var measuredShutterSpeed = measuredExposureTime / 1_000_000_000.0;

                    // Calculate the EV that the camera chose for "proper" exposure
                    var chosenEV = MathF.Log2((float)((measuredAperture * measuredAperture) / measuredShutterSpeed)) + MathF.Log2((float)(measuredISO / 100.0));

                    // Convert EV to scene brightness (lux)
                    const double K = 12.5; // For reflected light meter
                    var sceneBrightness = K * MathF.Pow(2, chosenEV) / (measuredISO / 100.0);

                    System.Diagnostics.Debug.WriteLine($"[ANDROID CAMERA] Measured: f/{measuredAperture:F1}, 1/{(1 / measuredShutterSpeed):F0}, ISO{measuredISO}");
                    System.Diagnostics.Debug.WriteLine($"[ANDROID CAMERA] Calculated EV: {chosenEV:F1}, Scene brightness: {sceneBrightness:F0} lux");

                    var brightnessResult = new BrightnessResult
                    {
                        Success = true,
                        Brightness = sceneBrightness
                    };

                    // Complete the task
                    _brightnessResultHandler.SetResult(brightnessResult);
                    _brightnessResultHandler = null;
                    _isMeasuringBrightness = false;

                    return; // Don't process normal capture logic
                }
                catch (Exception ex)
                {
                    _brightnessResultHandler.SetResult(new BrightnessResult
                    {
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                    _brightnessResultHandler = null;
                    _isMeasuringBrightness = false;
                    return;
                }
            }

            owner.Camera.Meta.ISO = (int)result.Get(CaptureResult.SensorSensitivity);
			owner.Camera.Meta.FocalLength = (float)result.Get(CaptureResult.LensFocalLength);

			//var meta = new Metadata()
			//{
			//    Name = $"{Android.OS.Build.Manufacturer} {Android.OS.Build.Model}",
			//    Orientation = (int)result.Get(CaptureResult.JpegOrientation),
			//    ISO = (int)result.Get(CaptureResult.SensorSensitivity),
			//    FocalLength = (float)result.Get(CaptureResult.LensFocalLength)
			//};

			switch (owner.mState)
			{
				case CameraFragment.STATE_WAITING_LOCK:
					{
						owner.CapturingStill = true;
						Integer afState = (Integer)result.Get(CaptureResult.ControlAfState);
						if (afState == null)
						{
							owner.mState = CameraFragment.STATE_PICTURE_TAKEN; // avoids multiple picture callbacks



							owner.CaptureStillPicture();
						}

						else if (
							(((int)ControlAFState.FocusedLocked) == afState.IntValue()) ||
								   (((int)ControlAFState.NotFocusedLocked) == afState.IntValue()))
						{
							// ControlAeState can be null on some devices
							Integer aeState = (Integer)result.Get(CaptureResult.ControlAeState);
							if (aeState == null ||
									aeState.IntValue() == ((int)ControlAEState.Converged))
							{
								owner.mState = CameraFragment.STATE_PICTURE_TAKEN;



								owner.CaptureStillPicture();
							}
							else
							{
								owner.RunPrecaptureSequence();
							}
						}



						break;
					}
				case CameraFragment.STATE_WAITING_PRECAPTURE:
					{
						// ControlAeState can be null on some devices
						Integer aeState = (Integer)result.Get(CaptureResult.ControlAeState);
						if (aeState == null ||
								aeState.IntValue() == ((int)ControlAEState.Precapture) ||
								aeState.IntValue() == ((int)ControlAEState.FlashRequired))
						{
							owner.mState = CameraFragment.STATE_WAITING_NON_PRECAPTURE;
						}
						break;
					}
				case CameraFragment.STATE_WAITING_NON_PRECAPTURE:
					{
						// ControlAeState can be null on some devices
						Integer aeState = (Integer)result.Get(CaptureResult.ControlAeState);
						if (aeState == null || aeState.IntValue() != ((int)ControlAEState.Precapture))
						{
							owner.mState = CameraFragment.STATE_PICTURE_TAKEN;

							owner.CaptureStillPicture();
						}
						break;
					}
			}
		}
	}

}
