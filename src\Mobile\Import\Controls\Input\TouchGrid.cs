﻿using AppoMobi.Touch;


namespace AppoMobi.Forms.Controls
{
    public class TouchGrid : AppoMobi.Touch.LegacyGesturesGrid
 
    {
        public TouchGrid()
        {
            Down += OnDown;
            Up += OnUp;
        }

        //-------------------------------------------------------------
        // Reaction
        //-------------------------------------------------------------
        private const string nameReaction = "Reaction";
        public static readonly BindableProperty ReactionProperty = BindableProperty.Create(nameReaction, typeof(HotspotReaction), typeof(TouchGrid), HotspotReaction.Minify); //, BindingMode.TwoWay
        public HotspotReaction Reaction
        {
            get { return (HotspotReaction)GetValue(ReactionProperty); }
            set { SetValue(ReactionProperty, value); }
        }




        //-------------------------------------------------------------
        // TintColor
        //-------------------------------------------------------------
        private const string nameTintColor = "TintColor";
        public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(TouchGrid), Colors.Transparent); //, BindingMode.TwoWay
        public Color TintColor
        {
            get { return (Color)GetValue(TintColorProperty); }
            set { SetValue(TintColorProperty, value); }
        }


        //-------------------------------------------------------------
        // DownOpacity
        //-------------------------------------------------------------
        private const string nameDownOpacity = "DownOpacity";
        public static readonly BindableProperty DownOpacityProperty = BindableProperty.Create(nameDownOpacity, typeof(double), typeof(TouchGrid), 0.75); //, BindingMode.TwoWay
        private double _savedOpacity;

        public double DownOpacity
        {
            get { return (double)GetValue(DownOpacityProperty); }
            set { SetValue(DownOpacityProperty, value); }
        }


        //-------------------------------------------------------------
        // TouchDown
        //-------------------------------------------------------------
        private const string nameTouchDown = "TouchDown";
        public static readonly BindableProperty TouchDownProperty = BindableProperty.Create(nameTouchDown, typeof(bool), typeof(TouchGrid), false); //, BindingMode.TwoWay
        public bool TouchDown
        {
            get { return (bool)GetValue(TouchDownProperty); }
            set { SetValue(TouchDownProperty, value); }
        }	

        protected Color _savedColor;

        private void OnUp(object sender, DownUpEventArgs e)
        {
            if (Reaction == HotspotReaction.Minify)
            {
                Scale = 1.0;
            }
            else
            if (Reaction == HotspotReaction.Zoom)
            {
                Scale = 1.0;
            }

            Opacity = _savedOpacity;
            BackgroundColor = _savedColor;

            TouchDown = false;
        }

        private void OnDown(object sender, DownUpEventArgs e)
        {

            if (_savedOpacity != DownOpacity)
                _savedOpacity = Opacity;

            if (_savedColor != TintColor)
                _savedColor = BackgroundColor;

            if (Reaction == HotspotReaction.Tint)
            {
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
            }
            else
            if (Reaction == HotspotReaction.Minify)
            {
                Opacity = DownOpacity;
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
                Scale = 0.985;
            }
            else
            if (Reaction == HotspotReaction.Zoom)
            {
                Opacity = DownOpacity;
                if (TintColor != null && TintColor != Colors.Transparent)
                {
                    BackgroundColor = TintColor;
                }
                Scale = 1.1;
            }

            TouchDown = true;
        }
    }
}
