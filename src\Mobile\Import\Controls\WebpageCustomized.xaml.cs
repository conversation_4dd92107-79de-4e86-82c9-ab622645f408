﻿using AppoMobi.Common.Constants;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;
using System;
using System.Diagnostics;
using System.Windows.Input;





namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WebpageCustomized : PageEnhancedNav
    {



        private bool _isLoading = true;
        public bool IsLoading
        {
            get { return _isLoading; }

            private set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Tel { get; set; } = "";








        public WebpageCustomized(string url, string title, string tel)

        {
            InitializeComponent();


            Title = title;

            if (!string.IsNullOrEmpty(tel))
            {
                Tel = tel;

                RightIcon1Symbol.SetIcon(FontIcons.fa_phone);
                RightIcon1Symbol.RotationY = 180;
                ToggleButtonVisibility(ButtonType.Right1, true);
                //cNavBar.RightIcon1Source = "resource://AppoMobi.Mobile.Images.Navbar.phones.png";
                //cNavBar.ToggleButtonVisibility(ButtonType.Right1, true);
            }
            else
            {

            }
            ToggleButtonVisibility(ButtonType.Right2, false);
            RightIcon2Symbol.SetIcon(FontIcons.arrow_alt_circle_left);


            if (string.IsNullOrEmpty(url))
            {
                url = string.Format(Constants.ServerRedirectLinkMask, TenantOptions.TenantKey, "site");
            }


            //System.Net.WebUtility.UrlEncode(subject)

            bool secure = url.ToLower().Contains("https");

            var myurl = url.Replace(@"http://", "");
            myurl = myurl.Replace(@"http//", "");
            myurl = myurl.Replace(@"https://", "");

            if (secure)
                myurl = @"https://" + myurl;
            else
                myurl = @"http://" + myurl;

            Uri uri = new Uri(myurl);

            Debug.WriteLine("*** URL: " + myurl);
            Debug.WriteLine("*** Navigating to: " + uri);

            try
            {
                MyBrowser.Source = uri; //;

            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }



        }


        ICommand telCommand;
        public ICommand TelCommand =>
            telCommand ?? (telCommand = new Command(TelClicked));

        void TelClicked()

        {
            //var World = new CWorld();

            Core.Dial(Tel);
        }

        private int _navigated = 0;
        private void MyBrowser_OnNavigating(object sender, WebNavigatingEventArgs e)
        {
            //            Indicator.IsVisible = true;
            IsLoading = true;
        }

        protected override void OnSizeAllocated(double width, double height)
        {
            base.OnSizeAllocated(width, height);

            var testmeh = IsLoading;

            return;
        }

        private void MyBrowser_OnNavigated(object sender, WebNavigatedEventArgs e)
        {
            IsLoading = false;
            //Indicator.IsVisible = false;
            _navigated++;
            if (_navigated > 2)
            {

                ToggleButtonVisibility(ButtonType.Right2, true);


                //ToolbarItems.Add(new ToolbarItem
                //{
                //    Order = ToolbarItemOrder.Primary,
                //    Icon = "tbback",
                //    Text = ResStrings.WebBack,
                //    Command = BackCommand

                //});

            }

        }

        ICommand backCommand;
        public ICommand BackCommand => backCommand ?? (backCommand = new Command(GoBack));

        new void GoBack()

        {
            try
            {
                if (MyBrowser.CanGoBack) MyBrowser.GoBack();
            }
            catch
            {

            }
            //World.Dial(ResStrings.OwnerTel);
        }


        public override async void OnRightIcon1Clicked()
        {
            TelClicked();
        }

        public override async void OnRightIcon2Clicked()
        {
            GoBack();
        }



    }
}