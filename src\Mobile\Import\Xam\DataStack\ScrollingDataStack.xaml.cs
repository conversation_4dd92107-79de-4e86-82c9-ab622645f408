﻿using System;
using System.Linq;
using System.Threading.Tasks;




namespace AppoMobi.Xam
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ScrollingDataStack 
    {
        public ScrollingDataStack()
        {
            InitializeComponent();

            ControlDataStack.ItemTemplate = ItemTemplate;
        }

        public bool LockRendering { get; set; }

        public DataTemplate ItemTemplate
        {
            get
            {
                if (ControlDataStack != null)
                {
                    return ControlDataStack.ItemTemplate;
                }
                return null;
            }
            set
            {
                if (ControlDataStack != null)
                {
                    ControlDataStack.ItemTemplate = value;
                }
            }
        }

        public NiftyDataStack DataStack => ControlDataStack;

        /*
        
        // ItemsSource
        
        private const string nameItemsSource = "ItemsSource";
        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameItemsSource, typeof(IList), typeof(ScrollingDataStack), null, BindingMode.TwoWay);
        public IList ItemsSource
        {
            get
            {
                if (ControlDataStack != null)
                {
                    return ControlDataStack.ItemsSource;
                }
                return null;
            }
            set
            {
                if (ControlDataStack != null)
                {
                    ControlDataStack.ItemsSource = value;
                }
            }
        }
        */      

        

        private bool lock_onscrolled { get; set; }
        private void OnScrolled_ScrollView(object sender, ScrolledEventArgs e)
        {
            if (lock_onscrolled) return;
            lock_onscrolled = true;

            UpdateCellsVisibility();

            lock_onscrolled = false;
        }

        public bool TuchedTop { get; protected set; }
        public bool TuchedBottom { get; protected set; }

        
        protected void UpdateCellsVisibility()
        
        {
            if (Orientation == ScrollOrientation.Vertical)
            {
                ControlDataStack.ProcessVisibility(MainScroll.ScrollY, MainScroll.Height, true);
                if (MainScroll.ScrollY < 1)
                {
                    if (!TuchedTop)
                    {
                        TuchedTop = true;
                        ScrollTuchedTop?.Invoke(this, null);
                        //Debug.WriteLine($"[TUCHED TOP] : {this.GetType().Name}");
                    }
                }
                else
                {
                    TuchedTop = false;
                    //check if last cell appeared, means we tuched bottom
                    var last = ControlDataStack.Views.Last();
                    var maybeTuchedBottom = false;
                    if (last != null && last is NiftyCell)
                    {
                        if (((NiftyCell)last).Appeared)
                        {
                            if (!TuchedBottom)
                            {
                                maybeTuchedBottom = true;
                                ScrollTuchedBottom?.Invoke(this, null);
                                Task.Delay(1);
                                OnScrollTuchedBottom();
                                //Debug.WriteLine($"[TUCHED BOTTOM] : {this.GetType().Name}");
                            }
                        }
                    }
                    TuchedBottom = maybeTuchedBottom;
                }
            }
            else
            {
                ControlDataStack.ProcessVisibility(MainScroll.ScrollX, MainScroll.Width, false);
            }
        }

        private void OnScrollTuchedBottom()
        {
            if (ControlDataStack.RenderedPage < ControlDataStack.TotalPages - 1)
            {
                LockRendering = true;
                
                RendererCommand?.Invoke("StopScrolling",null);
                SendScrollFinished();
                Task.Delay(1);

                var savedHeight = ControlStackLayout.Height;

                ControlDataStack.Render(ControlDataStack.RenderedPage + 1);

                //todo unlock rendering when the height of the internal stacklayout changes

                Device.StartTimer(TimeSpan.FromMilliseconds(10), () =>
                {
                    if (ControlStackLayout.Height != savedHeight)
                    {
                        LockRendering = false;
                        return false;
                    }
                    return true;
                });

            }

        }

        public event EventHandler RendererCommand;

        public event EventHandler ScrollTuchedBottom = null;
        public event EventHandler ScrollTuchedTop = null;

        private void OnFinishedDrawing_DataStack(object sender, EventArgs e)
        {
            UpdateCellsVisibility();
        }

        
        public void Dispose()
        
        {           
            ControlDataStack.Dispose();
        }

        //-------------------------------------------------------------
        // PageSize
        //-------------------------------------------------------------
        private const string namePageSize = "PageSize";
        public static readonly BindableProperty PageSizeProperty = BindableProperty.Create(namePageSize, typeof(int), typeof(ScrollingDataStack), 0); //, BindingMode.TwoWay
        /// <summary>
        /// Paged output size in items. Disabled if PageSize is less than 1
        /// </summary>
        public int PageSize
        {
            get
            {
                if (ControlDataStack != null)
                {
                    return ControlDataStack.PageSize;
                }
                return 0;
            }
            set
            {
                if (ControlDataStack != null)
                {
                    ControlDataStack.PageSize = value;
                }
            }
        }

    }
}