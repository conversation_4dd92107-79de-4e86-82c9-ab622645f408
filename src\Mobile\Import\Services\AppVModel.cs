﻿namespace AppoMobi
{
//    //===================================================================
//    public class AppVModel : NiftyBaseModel
//    //===================================================================
//    {
//        #region SELF_CREATE
//        //self create
//        private static AppVModel _self;
//        public static AppVModel Instance
//        {
//            get
//            {
//                if (_self == null)
//                {
//                    _self = new AppVModel();
//                    Doctor.Mobi.NotifyInstanceCreation(_self.GetType().Name);
//                }
//                return _self;
//            }
//        }
//        #endregion

//        //data
//     //   public TenantInfoDTO Info { get; set; }

//        //Bookable Objects

//        //[0:] Binding: 'AppoObjects' property not found on 'AppoMobi.AppVModel', 
//        // target property: 'Nifty.NiftyDataStack.ItemsSource'








//        private bool _HasContent;
//        public bool HasContent
//        {
//            get { return _HasContent; }
//            set
//            {
//                if (_HasContent != value)
//                {
//                    _HasContent = value;
//                    OnPropertyChanged();
//                }
//            }
//        }


//        private string _SearchString;
//        public string SearchString
//        {
//            get { return _SearchString; }
//            set
//            {
//                if (_SearchString != value)
//                {
//                    _SearchString = value;
//                    OnPropertyChanged();
//                }
//            }
//        }





//        #region BOOKABLE_TIME

//        private DateTime _ShowTimeFrom;
//        public DateTime ShowTimeFrom
//        {
//            get { return _ShowTimeFrom; }
//            set
//            {
//                if (_ShowTimeFrom != value)
//                {
//                    _ShowTimeFrom = value;
//                    OnPropertyChanged();
//                }
//            }
//        }

//        private DateTime _ShowTimeTo;
//        public DateTime ShowTimeTo
//        {
//            get { return _ShowTimeTo; }
//            set
//            {
//                if (_ShowTimeTo != value)
//                {
//                    _ShowTimeTo = value;
//                    OnPropertyChanged();
//                }
//            }
//        }

//        public TimeSpan ShowTimeInterval { get; set; } = new TimeSpan(60,0,0,0);

//        
//        public CompanyInfoDTO MyCompany
//        
//        {
//            get
//            {
//                return Info.Companies.First();
//            }
//        }





 

 

//        public DateTime ShowCalendarFrom { get; set; }
//        public DateTime ShowCalendarTo { get; set; }
//        public TimeSpan ShowCalendarPeriod { get; set; }

//        #endregion

//        private string _debugCtx;
//        public string debugCtx
//        {
//            get { return _debugCtx; }
//            set
//            {
//                if (_debugCtx != value)
//                {
//                    _debugCtx = value;
//                    OnPropertyChanged();
//                }
//            }
//        }
        

//        
//        public AppVModel()
//        
//        {
//            //commands:
//            //OnSuccess_Avatar_Command = new Command(OnSuccess_Avatar);

//            ShowTimeFrom =DateTimeExtensions.GetLocalToday();
//            ShowTimeTo = ShowTimeFrom + ShowTimeInterval;

//            IsLoading = false;

//            //debugCtx = "AppVModel";

//            DateTime now = DateTimeExtensions.GetLocalTimeNow();
//            TimeSpan span = now.AddMonths(1) - now;
//            ShowCalendarPeriod = span;

////            Info = ret;

//        }

//        public bool UserRegistered
//        {
//            get {return  false; }
//        }

//        public bool UserNotRegistered
//        {        
//            get { return !UserRegistered; }
//        }



//        
 

   
    
        
//    }
}
