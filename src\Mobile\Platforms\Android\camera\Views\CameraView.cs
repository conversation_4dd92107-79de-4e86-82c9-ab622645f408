﻿using System;
using Android.Content;
using Android.Runtime;
using Android.Util;
using AppoMobi.Droid.Camera.Rendered;

namespace AppoMobi.Droid.Camera.Views
{
 

 

    public class CameraView : CameraLayout
    {
        protected CameraView(IntPtr javaReference, JniHandleOwnership transfer) : base(javaReference, transfer)
        {
        }

        public CameraView(Context context, int createRandom) : base(context, createRandom)
        {
        }

        public CameraView(Context context, IAttributeSet attrs) : base(context, attrs)
        {
        }

        public CameraView(Context context, IAttributeSet attrs, int defStyleAttr) : base(context, attrs, defStyleAttr)
        {
        }

        public CameraView(Context context, IAttributeSet attrs, int defStyleAttr, int defStyleRes) : base(context, attrs, defStyleAttr, defStyleRes)
        {
        }


    }

 


}