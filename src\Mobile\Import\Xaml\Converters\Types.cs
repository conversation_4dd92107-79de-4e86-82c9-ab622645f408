﻿using System;
using System.Globalization;

namespace AppoMobi.Xam.Converters
{
   


    //***************************************************************
    public class TypeIsStringConverter : ConverterBase
    //***************************************************************
    {

        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                return true;
            }
            return false;
        }

    }


    //***************************************************************
    public class TypeIsNotStringConverter : ConverterBase
    //***************************************************************
    {

        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                return false;
            }
            return true;
        }

    }

}
