﻿using System;

namespace AppoMobi.Framework.Attributes
{

    //================================================
    public class Dropdown : Attribute
    //================================================
    {
        public string DataType { get; set; }
        //-
        public Dropdown(string customDataType)
        //-
        {
            DataType = customDataType;
        }

        public Type Source { get; set; }

        public bool IsSystemDb { get; set; }

        public Dropdown(Type source, bool forceSystemDb = false)

        {
            Source = source;

            IsSystemDb = forceSystemDb;
        }
    }
    //================================================


    //================================================

    //================================================

    //================================================

    //================================================


    //================================================
    //================================================

    //================================================

    //================================================

    // This defaults to Inherited = true.


    //-


    //public class IsExportProhibited : Attribute
    //{
    //}
}