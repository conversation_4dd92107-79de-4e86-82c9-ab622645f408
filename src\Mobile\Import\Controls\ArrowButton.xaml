﻿<?xml version="1.0" encoding="UTF-8" ?>
<xam:NiftyCell
    x:Class="AppoMobi.UI.ArrowButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    BackgroundColor="WhiteSmoke"
    Down="NiftyImageButton_OnDown"
    HorizontalOptions="FillAndExpand"
    Tapped="OnTapped_Frame"
    VerticalOptions="StartAndExpand">

    <gestures:LegacyGesturesStackLayout
        x:Name="SelectionBox"
        HorizontalOptions="FillAndExpand"
        SizeChanged="VisualElement_OnSizeChanged"
        Spacing="0"
        VerticalOptions="FillAndExpand">

        <Grid
            x:Name="ControlLayout"
            Padding="0"
            ColumnSpacing="0"
            HeightRequest="45"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  background image  -->
            <forms:CachedImage
                x:Name="imgBack"
                Grid.ColumnSpan="4"
                Aspect="Fill"
                BitmapOptimizations="False"
                FadeAnimationEnabled="False"
                HorizontalOptions="Fill"
                IsOpaque="False"
                LoadingPriority="Highest"
                VerticalOptions="Fill" />
            <!--  left image COL 0 icon  -->
            <forms:CachedImage
                x:Name="ControlImageLeft"
                Grid.Column="0"
                Aspect="Fill"
                Error="OnImageErrors"
                FadeAnimationEnabled="False"
                HorizontalOptions="End"
                IsVisible="False"
                VerticalOptions="Center" />
            <Label
                x:Name="ControlLabel"
                Grid.Column="1"
                Margin="8,0,0,0"
                FontSize="14.5"
                HorizontalOptions="StartAndExpand"
                TextColor="{x:Static appoMobi:AppColors.BwGrey}"
                VerticalOptions="Center"
                VerticalTextAlignment="Center" />
            <Grid
                x:Name="cSelectedGrid"
                Grid.Column="2"
                Margin="8,0,0,0"
                HorizontalOptions="EndAndExpand"
                VerticalOptions="Center">

                <!--<Label
           Margin="0.75,0.75,0,0"
           HorizontalTextAlignment="End"
           FontSize="13"
            TextColor="#232323"
            VerticalTextAlignment="Center"
           x:Name="txtSelectedBack"
           />-->

                <Label
                    x:Name="txtSelected"
                    FontSize="13"
                    HorizontalTextAlignment="End"
                    TextColor="#383838"
                    VerticalTextAlignment="Center" />
            </Grid>

            <!--  right aligned image  -->
            <forms:CachedImage
                x:Name="ControlImageRight"
                Grid.Column="2"
                Aspect="Fill"
                Error="OnImageErrors"
                FadeAnimationEnabled="False"
                HorizontalOptions="Start"
                IsVisible="False"
                VerticalOptions="Center" />

            <!--  arrow Text="⇨"  -->
            <xam:FontIconLabel
                x:Name="cMyArrow"
                Grid.Column="3"
                Margin="8,0,8,0"
                FontSize="20"
                TextColor="LightGray"
                VerticalOptions="Center" />

            <!--  separator  -->

            <BoxView
                Grid.Column="0"
                Grid.ColumnSpan="4"
                BackgroundColor="LightGray"
                HeightRequest="0.75"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="End" />

        </Grid>



    </gestures:LegacyGesturesStackLayout>



</xam:NiftyCell>





