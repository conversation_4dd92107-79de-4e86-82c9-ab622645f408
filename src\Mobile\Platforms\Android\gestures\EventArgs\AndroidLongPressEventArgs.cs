using Android.Views;
using AppoMobi.Touch;
using System;
using System.Collections.Generic;
using Xamarin.Forms;
using View = Android.Views.View;
using Microsoft.Maui.Graphics;

namespace AppoMobi.Touch.Droid.EventArgs
{
	public class AndroidLongPressEventArgs : LongPressEventArgs
	{
		public AndroidLongPressEventArgs(MotionEvent end, View view, bool cancel = false)
		{
			this.Cancelled = (cancel ? true : end.Action == MotionEventActions.Cancel);
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(end);
			this.Duration = end.EventTime - end.DownTime;
		}

		public AndroidLongPressEventArgs(IEnumerable<Microsoft.Maui.Graphics.Point> touches, long duration, View view)
		{
			this.Cancelled = false;
			this.ViewPosition = AndroidEventArgsHelper.GetViewPosition(view);
			this.Touches = AndroidEventArgsHelper.GetTouches(touches);
			this.Duration = duration;
		}
	}
}