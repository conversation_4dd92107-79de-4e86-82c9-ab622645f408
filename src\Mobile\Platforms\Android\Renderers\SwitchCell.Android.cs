﻿using Android.Graphics;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;

namespace AppoMobi.Droid.Renderers
{

    public class CustomSwitchRendererDroid : SwitchHandler
    {
        protected override void ConnectHandler(SwitchCompat platformView)
        {
            base.ConnectHandler(platformView);

            Control.CheckedChange += OnChecked;

            Update();
        }

        private void OnChecked(object? sender, CompoundButton.CheckedChangeEventArgs e)
        {
            //VirtualView.SetValueFromRenderer(Switch.IsToggledProperty, Control.Checked);

            Update();
        }

        protected override void DisconnectHandler(SwitchCompat platformView)
        {
            Control.CheckedChange -= OnChecked;

            base.DisconnectHandler(platformView);
        }

        void Update()
        {
            if (Control.Checked)
            {
                Control.ThumbDrawable.SetColorFilter(AppColors.Accent.ToPlatform(), PorterDuff.Mode.SrcAtop);
                Control.TrackDrawable.SetColorFilter(AppColors.Accent.ToPlatform(), PorterDuff.Mode.SrcAtop);
            }
            else
            {
                Control.ThumbDrawable.SetColorFilter(Colors.Transparent.ToPlatform(), PorterDuff.Mode.SrcAtop);
                Control.TrackDrawable.SetColorFilter(AppColors.Accent.ToPlatform(), PorterDuff.Mode.SrcAtop);
            }
        }

        SwitchCompat Control => PlatformView as SwitchCompat;

    }
}
