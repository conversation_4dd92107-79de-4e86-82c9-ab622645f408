﻿using System;
using AppoMobi.Models;


namespace AppoMobi
{
    //===================================================================
    public class NewsItem : ObservableObject
    //===================================================================
    {
        private string name;

        private string subtitle;

        private string _Title;
        public string Title
        {
            get { return _Title; }
            set
            {
                if (_Title != value)
                {
                    _Title = value;
                    OnPropertyChanged();
                }
            }
        }

        private string _Description;
        public string Description
        {
            get { return _Description; }
            set
            {
                if (_Description != value)
                {
                    _Description = value;
                    OnPropertyChanged();
                }
            }
        }

         

        public string Image { get; set; }
        public DateTime DateTime { get; set; }
        public string ExtUrl { get; set; }
    }

    //===================================================================
    public class NewsList
    //===================================================================
    {
        private NewsItem selectedItem;

        
    }
}