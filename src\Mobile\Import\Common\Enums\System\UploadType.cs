﻿using System.ComponentModel;
using AppoMobi.Specials.Localization;
using AppoMobi.Forms.Common.ResX;

namespace AppoMobi.Common.Enums.System
{
    [TypeConverter(typeof(LocalizedEnumConverter))]
    [FromResources(Type = typeof(ResStrings))]
    public enum UploadType
    {
        Default,
        Avatar,
        Banner,
        CategoryBanner,
        Gallery,
        ID,
        Doc,
        AdminAttachment,
        ChatBanner,
        ChatAttachment,
        Other,
        ProfileAttachment,
    }
}