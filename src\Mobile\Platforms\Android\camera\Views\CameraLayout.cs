﻿using System;
using Android.Content;
using Android.Graphics;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Views.Animations;
using Android.Widget;
using AppoMobi.Droid.Camera.Views;

using Microsoft.Maui.Platform;
using Animation = Android.Views.Animations.Animation;
using Boolean = Java.Lang.Boolean;
using Color = Android.Graphics.Color;
using Console = System.Console;
using Debug = System.Diagnostics.Debug;
using Element = Android.Renderscripts.Element;
using Exception = System.Exception;
using Image = Android.Media.Image;
using Math = System.Math;
using Point = Android.Graphics.Point;
using RelativeLayout = Android.Widget.RelativeLayout;
using Semaphore = Java.Util.Concurrent.Semaphore;
using Size = Android.Util.Size;
using SizeF = Android.Util.SizeF;
using Stream = Android.Media.Stream;
using StringBuilder = Java.Lang.StringBuilder;
using View = Android.Views.View;

namespace AppoMobi.Droid.Camera.Rendered
{
    public class CameraLayout : RelativeLayout
    {
        public CameraFragment Control;

        // Flash the screen to signal that we took a photo. BLINK
        public void FlashScreen(long duration = 250)
        {
            var flashView = new View(this.Context);
            flashView.SetBackgroundColor(Color.Black);

            this.AddView(flashView);

            //this.AddSubview(flashView);
            //flashView.BackgroundColor = UIColor.Black;
            //flashView.Layer.Opacity = 1;
            //UIView.Animate(duration, () => { flashView.Layer.Opacity = 0; },
            //    () => { flashView.RemoveFromSuperview(); });

            Animation animation = new AlphaAnimation(1, 0); // Change alpha
            // from fully
            // visible to
            // invisible
            animation.Duration = duration;  
            animation.Interpolator = new LinearInterpolator(); // do not alter
            // animation
            // rate
            animation.RepeatCount = 0; // Repeat animation
            // infinitely
            animation.RepeatMode = 0; // Reverse animation at


            animation.SetAnimationListener(new AnimationListener
            {
                OnEnded = () =>
                {
                    flashView.SetBackgroundColor(Color.Transparent);
                    this.RemoveView(flashView);
                    flashView.Animation = null;
                }
            });

            // the
            // end so the layout will
            // fade back in
            flashView.Animation = animation;

        }

        public class AnimationListener : Java.Lang.Object, Animation.IAnimationListener
        {
            public Action OnEnded { get; set; }

            public void OnAnimationEnd(Animation animation)
            {
               OnEnded?.Invoke();
            }

            public void OnAnimationRepeat(Animation animation)
            {
                var stop = true;
            }

            public void OnAnimationStart(Animation animation)
            {
                
            }
        }

        public void Init(Context context)
        {
            this.Tag = "CameraLayout";

            if (Id==0)
                this.Id = 6128;

#if DEBUG
            this.SetBackgroundColor(Android.Graphics.Color.Red);
#else
            this.SetBackgroundColor(Android.Graphics.Color.Black);
#endif
            var fm = Platform.CurrentActivity.GetFragmentManager();

            Control = new CameraFragment();

            var ft = fm.BeginTransaction();
            ft.Add(Id, Control, this.Tag.ToString());
            ft.Commit();
        }

        public void Destroy()
        {
            //Control.Stop();
            Control?.OnPause();
            var fm = Platform.CurrentActivity.GetFragmentManager();
            var ft = fm.BeginTransaction();
            ft.Remove(Control);
            ft.Commit();
            //Control.Dispose();

        }

        protected CameraLayout(IntPtr javaReference, JniHandleOwnership transfer) : base(javaReference, transfer)
        {
        }

        public CameraLayout(Context context, int createRandom) : base(context)
        {
            this.Id = createRandom;

            Init(context);
        }

        public CameraLayout(Context context) : base(context)
        {
            Init(context);
        }

        public CameraLayout(Context context, IAttributeSet attrs) : base(context, attrs)
        {
            Init(context);
        }

        public CameraLayout(Context context, IAttributeSet attrs, int defStyleAttr) : base(context, attrs, defStyleAttr)
        {
            Init(context);
        }

        public CameraLayout(Context context, IAttributeSet attrs, int defStyleAttr, int defStyleRes) : base(context, attrs, defStyleAttr, defStyleRes)
        {
            Init(context);
        }
    }
}