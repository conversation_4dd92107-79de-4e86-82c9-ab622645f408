﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace AppoMobi.Framework
{
    public interface IHasDynamicProperties
    {

    }

    public class DynamicPropertyAttribute : Attribute
    {

    }



    public class ObjectInfo
    {
        public string Id { get; set; }

        public string Title { get; set; }

        public string Description { get; set; }
    }


    public class Tree
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("dad")]
        public string Parent { get; set; }

        [JsonProperty("tree")]
        public List<Tree> Children { get; set; }

        [JsonProperty("count")]
        public int Total { get; set; }
    }

}
