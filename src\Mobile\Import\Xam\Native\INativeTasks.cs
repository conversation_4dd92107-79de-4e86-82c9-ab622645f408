﻿using System.Collections.Generic;

namespace AppoMobi.Xam
{
    //****************************************************
    public interface INativeTasks
    //****************************************************
    {
    

        bool CheckGpsSupported();
        bool CheckGpsEnabled();
        void InitPush();

        void ExecuteTask(string task, params object[] parameters);

        List<string> ListAvailableFonts();

        bool OpenInAppInstagram(string user);
        bool OpenInAppFacebook(string user);

        bool NavigateYandexMaps(string name, double latitude, double longitude);
        bool NavigateYandexNavigator(string name, double latitude, double longitude);

        bool OpenAppInStore();

        object CurrentViewDesc();

        bool AskForRating();
        void CloseApp();
        //void UpdateTabbedMenu();
        void OpenSettings();
        void OpenGPSSettings();
        void OpenGPSPermissions();
        void OpenPermissions();
        bool OpenUrl(string url);
        void PushEnableSound(bool value);
        void Test();
        double GetButtonWidth(string text);
        string GetAppVersion();
        string GetAppBuild();
        //void HideNavigationOverlay();
        /// <summary>
        /// In pixels
        /// </summary>
        /// <param name="control"></param>
        /// <returns></returns>
        Microsoft.Maui.Graphics.Point GetViewPositionOnScreen(View control);
    }
}
