﻿using System;
using System.Globalization;

namespace AppoMobi.Forms.Framework.Xaml
{
    public class IsNotNullConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if ((object)value != null)
                {
                    return true;
                }
            }
            catch (Exception e)
            {
            }
            return false;
        }
    }
}
