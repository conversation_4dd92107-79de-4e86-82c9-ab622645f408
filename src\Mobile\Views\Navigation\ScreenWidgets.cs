﻿namespace AppoMobi.Mobile.Views
{
    public class ScreenWidgets : AppScreen
    {
        public ScreenWidgets()
        {
            var widgets = TabsAndMenu.TabsList.ToList();
            var menu = TabsAndMenu.MenuList.ToList();

            Type = LayoutType.Column;
            Spacing = 0;
            VerticalOptions = LayoutOptions.Fill;

            Children = new List<SkiaControl>()
            {
                //navbar with padding
                new TopNavBar(),

                //widgets
                new SkiaScroll()
                {
                    RespondsToGestures = false,
                    Content = new VStack()
                    {
                        Spacing = 16,
                        UseCache = SkiaCacheType.ImageComposite,
                        Padding = new(0, 16),
                        Children = new List<SkiaControl>()
                        {
                            new SkiaStack()
                            {
                                HorizontalOptions = LayoutOptions.Center,
                                UseCache = SkiaCacheType.ImageComposite,
                                ItemsSource = widgets,
                                Spacing = 8,
                                Split = 0,
                                ItemTemplateType = typeof(ModuleWidget)
                            },
                            new SkiaStack()
                            {
                                HorizontalOptions = LayoutOptions.Center,
                                UseCache = SkiaCacheType.ImageComposite,
                                ItemsSource = menu,
                                Spacing = 8,
                                Split = 0,
                                ItemTemplateType = typeof(ModuleWidgetMenu)
                            }
                        }
                    }
                }.Fill()

                //bottom padding
            };
        }
    }
}
