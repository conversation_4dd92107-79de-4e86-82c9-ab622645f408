﻿using SkiaSharp;
using SkiaSharp.Views.Maui.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace AppoMobi.Forms.Controls.Skia
{
	public class SkiaLabel : SKCanvasView, IDisposable
	{

		public void Dispose()
		{
			PaintSurface -= CanvasViewOnPaintSurface;

		}

		public SkiaLabel()
		{
			//IgnorePixelScaling = true;

			//todo move this to set parent cuz maybe styles were applied
			HorizontalOptions = LayoutOptions.Start;
			VerticalOptions = LayoutOptions.Start;
			BackgroundColor = Colors.Transparent;

			PaintSurface += CanvasViewOnPaintSurface;

			//_typeface = SKFontManager.Default.MatchCharacter('м');
		}

		public static float[] KernelEdgeDetection => new float[9]
		{
			-1, -1, -1,
			-1,  8, -1,
			-1, -1, -1
		};

		public static float[] KernekLaplacianOfGaussian => new float[25]
		{
			0,  0, -1,  0,  0,
			0, -1, -2, -1,  0,
			-1, -2, 16, -2, -1,
			0, -1, -2, -1,  0,
			0,  0, -1,  0,  0
		};

		public static float[] KernelEmboss => new float[9]
		{
			-2, -1, 0,
			-1,  1, 1,
			0,  1, 2
		};

		public static float[] KernelSharpen => new float[9]
		{
			0,       -0.5f,    0,
			-0.5f,    3f,     -0.5f,
			0,      -0.5f,     0,
		};



		/*
        public static void DrawRasterizedText(SKCanvas canvas, float x, float y, string text, SKPaint textPaint, SKColor colorOutline)
        {
      //      using (SKPaint paint = new SKPaint())
            using (var imageText = RasterizeText(text, textPaint, colorOutline))
            {
                //paint.FilterQuality = SKFilterQuality.High;
                //paint.IsAntialias = false;
                //paint.IsDither = false;
 
 
                //var kernelSize = new SKSizeI(3, 3);
                //var kernelOffset = new SKPointI(1, 1);
                //paint.ImageFilter = SKImageFilter.CreateMatrixConvolution(
                //    kernelSize, KernelSharpen, 1.0f, 0f, kernelOffset, SKShaderTileMode.Clamp, false, null,null ); //1f, 0f, new SKPointI(1, 1),
           

                var imageY = y - imageText.Height + 1;

                var padding = 0;

                if (colorOutline != SKColor.Empty)
                    padding = 2;

                canvas.DrawImage(imageText, x-padding, imageY-padding);
            }
        }

        public static SKImage RasterizeText(string text, SKPaint paint, SKColor colorOutline)
        {

            using (var paintStrokeBigger = new SKPaint())
            using (var paintBigger = new SKPaint
            {
                TextSize = paint.TextSize* _scaleResampleText,
                Color = paint.Color,
                StrokeWidth = paint.StrokeWidth,
                IsStroke = paint.IsStroke,
                IsAntialias = paint.IsAntialias,
                Typeface = paint.Typeface,
            })
            {
                float paddingY = 0;
                float paddingX = 0;

                SKRect bounds = new SKRect();
                paint.MeasureText(text, ref bounds);

                SKRect boundsBigger = new SKRect();
                paintBigger.MeasureText(text, ref boundsBigger);

     
                if (colorOutline != SKColor.Empty)
                {
                    paintStrokeBigger.TextSize = paint.TextSize * _scaleResampleText;
                    paintStrokeBigger.Color = colorOutline;
                    paintStrokeBigger.StrokeWidth = 4.5f *  _scaleResampleText;
                    paintStrokeBigger.IsStroke = true;
                    paintStrokeBigger.IsAntialias = paint.IsAntialias;
                    paintStrokeBigger.Typeface = paint.Typeface;

                    SKRect boundsBiggerStroke = new SKRect();
                    paintStrokeBigger.MeasureText(text, ref boundsBiggerStroke);

                    paddingY = 4f * _scaleResampleText; //(boundsBiggerStroke.Height - boundsBigger.Height) / 2.0;
                    paddingX = 4f * _scaleResampleText; //(boundsBiggerStroke.Width - boundsBigger.Width) / 2.0;

                    //Debug.WriteLine($"[Rasterizer] {text} pX{paddingX}, pY={paddingY} ({boundsBiggerStroke.Width} {boundsBigger.Width})");
                }


                var height = paint.TextSize + paddingY / _scaleResampleText * 2;
                var width = bounds.Width + paddingX/ _scaleResampleText * 2;
                var info = new SKImageInfo((int)width, (int)height);
                
                var infoBigger = new SKImageInfo((int)(width*_scaleResampleText), (int)(height* _scaleResampleText));

                using (var surfaceBigger = SKSurface.Create(infoBigger))
                {
                SKCanvas canvasRasterizedText = surfaceBigger.Canvas;

                canvasRasterizedText.Clear();

                if (colorOutline != SKColor.Empty)
                {
                    canvasRasterizedText.DrawText(text, 0 + paddingX, infoBigger.Height - paddingY, paintStrokeBigger);
                }

                canvasRasterizedText.DrawText(text, 0 + paddingX, infoBigger.Height - paddingY, paintBigger);
                
                canvasRasterizedText.Flush();
                using (var srcImg = surfaceBigger.Snapshot())
                {
                    //downsample
                    using (var surface = SKSurface.Create(info))
                    using (var paintRescale = new SKPaint())
                    {
                        // high quality with antialiasing
                        paintRescale.IsAntialias = true;
                        paintRescale.FilterQuality = SKFilterQuality.High;




                        // draw the bitmap to fill the surface
                        surface.Canvas.DrawImage(srcImg, new SKRectI(0, 0, info.Width, info.Height), paintRescale);


                            //paintRescale.FilterQuality = SKFilterQuality.High;
                            //paintRescale.IsAntialias = false;
                            //paintRescale.IsDither = false;


                            //var kernelSize = new SKSizeI(3, 3);
                            //var kernelOffset = new SKPointI(1, 1);
                            //paintRescale.ImageFilter = SKImageFilter.CreateMatrixConvolution(
                            //    kernelSize, KernelSharpen, 1.0f, 0f, kernelOffset, SKShaderTileMode.Clamp, false, null,null ); //1f, 0f, new SKPointI(1, 1),


                            surface.Canvas.Flush();

                        return surface.Snapshot();
                    }
                }
            }
            }

 
        }

     */

		public double Sharpen { get; set; }

		private void CanvasViewOnPaintSurface(object sender, SKPaintSurfaceEventArgs argsSurface)
		{
			SKCanvas canvas = argsSurface.Surface.Canvas;

			canvas.Clear(SKColors.Transparent);

			SKImageInfo info = argsSurface.Info;

			canvas.Clear(SKColors.Transparent);

			Render(info, canvas);

		}


		private static float _scaleResampleText = 1.0f;

		private float LineSpacing = 1.2f;

		public void Render(SKImageInfo info, SKCanvas canvas, float scale = 1.0f)
		{
			using (var textPaint = new SKPaint
			{
				TextSize = (float)(FontSize * scale),
				Color = TextColor.ToSKColor(),
				IsAntialias = true,
				Typeface = this.TypeFace,
			})
			//using (var textStroke = new SKPaint
			//{
			//    TextSize = (float)(FontSize * scale),
			//    Color = TextStrokeColor.ToSKColor(),
			//    StrokeWidth = Core.DisplayDensity * 3f,
			//    IsStroke = true,
			//    IsAntialias = true,
			//    Typeface = _typeface, 
			//})
			{
				//offsets on canvas


				var translateX = 0.0f;
				var translateY = 0.0f;


				//measure text
				//var fm = new SKFontMetrics();
				//textPaint.GetFontMetrics(out fm);

				//1 line atm, todo multiline
				var textHeight = textPaint.TextSize;
				var textWidth = textPaint.MeasureText(Text);


				var canvasWidth = info.Width;
				var canvasHeight = info.Height;

				var margins = new Thickness(Margin.Left, Margin.Top, Margin.Right, Margin.Bottom);

				var layoutHorizontal = new LayoutOptions(HorizontalOptions.Alignment, HorizontalOptions.Expands);
				var layoutVertical = new LayoutOptions(VerticalOptions.Alignment, VerticalOptions.Expands);

				var offsetRotated = 0.0f;

				var signTextY = 1;

				if (RotateLayoutParameters)
				{
					if (SensorRotation == 90 || SensorRotation == 270)
					{
						canvasWidth = info.Height;
						canvasHeight = info.Width;

						offsetRotated = ((info.Height - info.Width) / 2.0f);

					}
					else
					if (SensorRotation == 180)
					{
						signTextY = 0;
					}
				}


				//layoutHorizontal
				var textX = offsetRotated;

				if (layoutHorizontal.Alignment == LayoutAlignment.Center)
				{
					//center

					textX = (canvasWidth - textWidth) / 2 - offsetRotated;
				}
				else
				if (layoutHorizontal.Alignment == LayoutAlignment.End)
				{
					//end

					textX = canvasWidth - textWidth - (float)this.Margin.Right * scale - offsetRotated;

				}
				else
				{
					//start

					textX = (float)this.Margin.Left * scale - offsetRotated;
				}

				//VerticalOptions
				var textY = offsetRotated;

				if (layoutVertical.Alignment == LayoutAlignment.Center)
				{
					//center

					textY = canvasHeight / 2.0f + textHeight / 2 + offsetRotated;
				}
				else
				if (layoutVertical.Alignment == LayoutAlignment.End)
				{
					//end

					textY = canvasHeight - (float)this.Margin.Bottom * scale + textHeight;
				}
				else
				{
					//start

					textY = (int)(offsetRotated + this.Margin.Top * scale + textHeight);
				}


				if (SensorRotation != 0)
				{
					if (!RotateLayoutParameters)
					{
						if (SensorRotation == 270) //right side
						{
							if (HorizontalOptions.Alignment == LayoutAlignment.End)
							{
								translateX = textWidth / 2.0f + textHeight;
							}
							if (VerticalOptions.Alignment == LayoutAlignment.Start)
							{
								translateY = (float)(-textHeight - Margin.Top + textWidth / 2.0f + Margin.Right - 4); //4 is outline padding
							}
						}
						else
						if (SensorRotation == 90) //left side
						{
							if (HorizontalOptions.Alignment == LayoutAlignment.Start)
							{
								translateX = -textWidth / 2.0f - textHeight;
							}
							if (VerticalOptions.Alignment == LayoutAlignment.Start)
							{
								translateY = (float)(-textHeight - Margin.Top + textWidth / 2.0f + Margin.Right - 4);
							}
						}
						else
						if (SensorRotation == 180) //upside down
						{

							translateY = (float)(-textHeight - Margin.Top + textHeight / 2.0 - Margin.Top + Margin.Bottom);

							//    translateY = (float)(-textHeight - Margin.Top + textHeight + Margin.Bottom); //4 is outline padding

						}

						textX += translateX;
						textY += translateY;
					}

					canvas.Save();


					if (RotateLayoutParameters)
					{
						canvas.RotateDegrees((float)SensorRotation, info.Width / 2.0f, info.Height / 2.0f);
					}
					else
					{
						canvas.RotateDegrees((float)SensorRotation, textX + textWidth / 2.0f, textY + textHeight / 2.0f);
					}

					DrawText(canvas, (float)textX, textY, Text, textPaint, TextStrokeColor.ToSKColor(), scale);

					canvas.Restore();

				}
				else
				{
					//normal draw
					//   canvas.DrawText(textX, textY, Text, textPaint);
					DrawText(canvas, textX, textY, Text, textPaint, TextStrokeColor.ToSKColor(), scale);
				}

				/*
                if (SensorRotation == 180) //upside down
                    {
                        canvas.Save();
                        canvas.RotateDegrees((float)SensorRotation, textX + textWidth / 2.0, textY + textHeight / 2);

                        DrawRasterizedText(canvas, (float)textX, (float)textY + textHeight * 2, Text, textPaint, TextStrokeColor.ToSKColor());
                        //canvas.DrawText(Text, (float)textX, (float)textY + textHeight * 2, textPaint);

                        canvas.Restore();
                    }
                    else
                    {
                        DrawRasterizedText(canvas, (float) textX, (float) textY, Text, textPaint, TextStrokeColor.ToSKColor());
                    }
        */
			}
		}


		public static void DrawText(SKCanvas canvas, float x, float y, string text, SKPaint textPaint, SKColor colorOutline, float scale)
		{
			float padding = 0;

			if (colorOutline.Alpha != 0)
			{
				padding = (float)(2 * scale);
				x -= padding;
				y -= padding;
			}

			using (var paintStroke = new SKPaint())
			{
				float paddingY = 0;
				float paddingX = 0;

				//SKRect bounds = new SKRect();
				//                textPaint.MeasureText(text, ref bounds);

				if (colorOutline.Alpha != 0)
				{
					paintStroke.TextSize = textPaint.TextSize * _scaleResampleText;
					paintStroke.Color = colorOutline;
					paintStroke.StrokeWidth = 2.0f * scale;
					paintStroke.IsStroke = true;
					paintStroke.IsAntialias = textPaint.IsAntialias;
					paintStroke.Typeface = textPaint.Typeface;

					//         paintStroke.MeasureText(text, ref bounds);
				}
				else
				{
					//       textPaint.MeasureText(text, ref bounds);
				}

				if (colorOutline.Alpha != 0)
				{
					canvas.DrawText(text, x, y, paintStroke);
				}

				canvas.DrawText(text, x, y, textPaint);
			}


		}


		public float LineHeight { get; protected set; }

		protected void UpdateFont()
		{
			InvalidateSurface();
		}

		private void DrawText(SKCanvas canvas, string text, SKRect area, SKPaint paint)
		{
			float lineHeight = paint.TextSize * LineSpacing;
			var lines = SplitLines(text, paint, area.Width);
			var height = lines.Count() * lineHeight;

			var y = area.MidY - height / 2.0f;

			foreach (var line in lines)
			{
				y += lineHeight;
				var x = area.MidX - line.Width / 2.0f;
				canvas.DrawText(line.Value, x, y, paint);
			}
		}

		private Line[] SplitLines(string text, SKPaint paint, float maxWidth)
		{
			var spaceWidth = paint.MeasureText(" ");
			var lines = text.Split('\n');

			return lines.SelectMany((line) =>
			{
				var result = new List<Line>();

				var words = line.Split(new[] { " " }, StringSplitOptions.None);

				var lineResult = new StringBuilder();
				float width = 0;
				foreach (var word in words)
				{
					var wordWidth = paint.MeasureText(word);
					var wordWithSpaceWidth = wordWidth + spaceWidth;
					var wordWithSpace = word + " ";

					if (width + wordWidth > maxWidth)
					{
						result.Add(new Line() { Value = lineResult.ToString(), Width = width });
						lineResult = new StringBuilder(wordWithSpace);
						width = wordWithSpaceWidth;
					}
					else
					{
						lineResult.Append(wordWithSpace);
						width += wordWithSpaceWidth;
					}
				}

				result.Add(new Line() { Value = lineResult.ToString(), Width = width });

				return result.ToArray();
			}).ToArray();
		}


		#region PROPERTIES

		//-------------------------------------------------------------
		// SensorRotation
		//-------------------------------------------------------------
		private const string nameSensorRotation = "SensorRotation";
		public static readonly BindableProperty SensorRotationProperty = BindableProperty.Create(nameSensorRotation, typeof(double), typeof(SkiaLabel),
			0.0, propertyChanged: RedrawCanvas);
		public double SensorRotation
		{
			get { return (double)GetValue(SensorRotationProperty); }
			set { SetValue(SensorRotationProperty, value); }
		}

		//-------------------------------------------------------------
		// Text
		//-------------------------------------------------------------
		private const string nameText = "Text";
		public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText,
			typeof(string), typeof(SkiaLabel), string.Empty,
			propertyChanged: RedrawCanvas);
		public string Text
		{
			get { return (string)GetValue(TextProperty); }
			set { SetValue(TextProperty, value); }
		}

		//-------------------------------------------------------------
		// TextColor
		//-------------------------------------------------------------
		private const string nameTextColor = "TextColor";
		public static readonly BindableProperty TextColorProperty = BindableProperty.Create(
			nameTextColor, typeof(Color), typeof(SkiaLabel),
			Colors.GreenYellow,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color TextColor
		{
			get { return (Color)GetValue(TextColorProperty); }
			set { SetValue(TextColorProperty, value); }
		}

		//-------------------------------------------------------------
		// TextStrokeColor
		//-------------------------------------------------------------
		private const string nameTextStrokeColor = "TextStrokeColor";
		public static readonly BindableProperty TextStrokeColorProperty = BindableProperty.Create(
			nameTextStrokeColor, typeof(Color), typeof(SkiaLabel),
			Colors.Transparent,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color TextStrokeColor
		{
			get { return (Color)GetValue(TextStrokeColorProperty); }
			set { SetValue(TextStrokeColorProperty, value); }
		}

		//-------------------------------------------------------------
		// FontSize
		//-------------------------------------------------------------
		private const string nameFontSize = "FontSize";
		public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize,
			typeof(double), typeof(SkiaLabel), 12.0);
		public double FontSize
		{
			get { return (double)GetValue(FontSizeProperty); }
			set { SetValue(FontSizeProperty, value); }
		}

		protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
		{
			base.OnPropertyChanged(propertyName);

			if (propertyName == nameFontSize)
			{
				UpdateFont();
			}
		}

		//-------------------------------------------------------------
		// RotateLayoutParameters
		//-------------------------------------------------------------
		private const string nameRotateLayoutParameters = "RotateLayoutParameters";
		public static readonly BindableProperty RotateLayoutParametersProperty = BindableProperty.Create(nameRotateLayoutParameters,
			typeof(bool), typeof(SkiaLabel), false,
			propertyChanged: RedrawCanvas);

		public SKTypeface TypeFace { get; set; }

		public bool RotateLayoutParameters
		{
			get { return (bool)GetValue(RotateLayoutParametersProperty); }
			set { SetValue(RotateLayoutParametersProperty, value); }
		}

		private static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)
		{
			var control = bindable as SkiaLabel;
			control?.InvalidateSurface();
		}

		#endregion


		public enum UseRotationDirection
		{
			None,
			UpsideDown,
			All
		}

		public class Line
		{
			public string Value { get; set; }

			public float Width { get; set; }
		}

	}


}
