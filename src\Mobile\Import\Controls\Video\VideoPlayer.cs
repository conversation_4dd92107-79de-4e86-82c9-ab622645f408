﻿using System;


namespace AppoMobi.VideoPlayer
{
    public class VideoView : View
    {

        
        public string Source
        {
            get { return (string)GetValue(SourceProperty); }
            set { SetValue(SourceProperty, value); }
        }
        public static readonly BindableProperty SourceProperty =
            BindableProperty.Create(
                nameof(Source),
                typeof(string),
                typeof(VideoView),
                string.Empty,
                BindingMode.TwoWay);
        
        public string Placeholder
        {
            get { return (string)GetValue(SourceProperty); }
            set { SetValue(SourceProperty, value); }
        }
        public static readonly BindableProperty PlaceholderProperty =
            BindableProperty.Create(
                nameof(Placeholder),
                typeof(string),
                typeof(VideoView),
                string.Empty,
                BindingMode.TwoWay);
        
        public bool Loop
        {
            get { return (bool)GetValue(LoopProperty); }
            set { SetValue(LoopProperty, value); }
        }
        public static readonly BindableProperty LoopProperty =
            BindableProperty.Create(
                nameof(Loop),
                typeof(bool),
                typeof(VideoView),
                true,
                BindingMode.TwoWay);
        
        public Action OnFinishedPlaying { get; set; }

    }


}

