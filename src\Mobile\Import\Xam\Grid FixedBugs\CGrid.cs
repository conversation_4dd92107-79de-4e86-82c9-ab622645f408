﻿

using Microsoft.Maui.Controls;

namespace AppoMobi.Xam
{
    public class CGrid : Grid
    {
        public CGrid()
        {
            Children.Add(new Microsoft.Maui.Controls.ContentView()); //avoiding the internal check: if (!InternalChildren.Any()) return;
            base.InvalidateMeasure();//force a call to EnsureRowsColumnsInitialized()
            Children.Clear(); //ready to roll
        }
    }

    public class GesturesGrid : AppoMobi.Touch.LegacyGesturesGrid
    {
        public GesturesGrid()
        {
            Children.Add(new Microsoft.Maui.Controls.ContentView()); //avoiding the internal check: if (!InternalChildren.Any()) return;
            base.InvalidateMeasure(); //force a call to EnsureRowsColumnsInitialized()
            Children.Clear(); //ready to roll
            //base.LayoutChildren(0, 0, 0, 0); //force a call to EnsureRowsColumnsInitialized()
        }
    }

}
