﻿using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Text;
using Android.Views;
using Android.Views.InputMethods;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using AppoMobi.Droid;
using AppoMobi.Forms.Controls.Input;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using Build = Android.OS.Build;
using Exception = System.Exception;
using TextChangedEventArgs = Android.Text.TextChangedEventArgs;
using View = Android.Views.View;


namespace AppoMobi.Handlers
{
    public partial class HandlerEntryCentered : EntryHandler
    {
        protected override void ConnectHandler(AppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

            this.FormsControl = (EntryCentered)this.VirtualView;

            UpdateSkin();

            FormsControl.RendererNeedUpdate += OnRendererNeedUpdate;

            InputFilter = new CustomFiler();
            var filters = new IInputFilter[] { InputFilter };

            NativeView.SetFilters(filters);

            Control.SetOnEditorActionListener(new ActionListener(this));
            Control.TextChanged += TextChanged;
            Control.FocusChange += FocusChanged;
        }

        protected override void DisconnectHandler(AppCompatEditText platformView)
        {
            InputFilter?.SetInputFilter(null);
            InputFilter = null;

            try
            {
                FormsControl.RendererNeedUpdate -= OnRendererNeedUpdate;

                Control.TextChanged -= TextChanged;
                Control.FocusChange -= FocusChanged;

                Control.SetOnEditorActionListener(null);

                FormsControl = null;
            }
            catch (Exception e)
            {
            }

            base.DisconnectHandler(platformView);
        }

        AppCompatEditText Control => PlatformView as AppCompatEditText;

        /// <summary>
        /// This is called when props change and when the text changes too
        /// </summary>
        private void ApplyDynamicLook()
        {

            if (!string.IsNullOrEmpty(Control.Text))
            {
                Control.TextSize = (float)FormsControl.TextSize;

                if (FormsControl.Centered)
                {
                    Control.Gravity = GravityFlags.CenterHorizontal;
                }
                else
                {
                    Control.Gravity = GravityFlags.Start;
                }
            }
            else
            {
                //placeholder visible
                Control.TextSize = (float)FormsControl.PlaceholderTextSize;

                if (FormsControl.PlaceHolderCentered)
                {
                    Control.Gravity = GravityFlags.CenterHorizontal;
                }
                else
                {
                    Control.Gravity = GravityFlags.Start;
                }
            }

        }



        private void UpdateSkin()
        {

            if (Control == null || FormsControl == null)
                return;

            try
            {

                if (!FormsControl.IsFocused)
                {
                    Control?.ClearFocus();
                    //NativeView?.ClearFocus();
                }

                if (FormsControl.MaxLines > 1)
                {
                    //todo this is not working at all !!!!

                    _backupType = NativeView.InputType;
                    NativeView.InputType = InputTypes.TextFlagMultiLine;
                    NativeView.SetSingleLine(false);
                    NativeView.SetMaxLines(FormsControl.MaxLines);
                }
                else
                {
                    NativeView.SetSingleLine(true);
                    if (_backupType != 0)
                        NativeView.InputType = _backupType;
                }

                //set background color to transparent
                //Control.SetTextSize();
                // this.Control.InputType = InputTypes.TextFlagNoSuggestions;
                Control.SetBackgroundColor(Android.Graphics.Color.Transparent);

                //set font
                //todo
                // var font = TextFont.Normal;
                //  Control.Typeface = font;

                //text size
                ApplyDynamicLook();

                if (FormsControl.TextColor != null)
                    Control.SetTextColor(FormsControl.TextColor.ToPlatform());

                //placeholder color
                if (FormsControl.PlaceholderColor != null)
                    Control.SetHintTextColor(FormsControl.PlaceholderColor.ToPlatform());

                if (Android.OS.Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop)
                {
                    //todo
                    //letter spacing
                    //   Control.LetterSpacing = TextFont.Info.LetterSpacing;//FormsControl.LetterSpacing;

                    //todo
                    //line spacing
                    //    Control.SetLineSpacing(1f, TextFont.Info.LineSpacing);//FormsControl.LineSpacing
                }
                else
                {
                    //todo
                    //not supported by lower android versions
                }



                //set select text on focus
                NativeView?.SetSelectAllOnFocus(FormsControl.SelectOnFocus);

                //set paddings
                Control.SetPadding(0, 0, 0, 0);

                //todo
                //var preset = TextFont.Info;
                //var adjust = new Rect();
                //if (preset != null)
                //{
                //    //PADDING to adjust Y position
                //    adjust = TextFont.Padding(); //todo for projects
                //}

                if (Build.VERSION.SdkInt > (BuildVersionCodes)23)
                {
                    //  Control.GetMargins().BottomMargin = (int)(-1 * Core.DisplayDensity);//needSpace;
                    // Control.SetPadding(0, 22, 0, 10);
                }
                else
                {
                    //Control.SetPadding(11, 5, 0, 5);
                }
                /*
                if (editText != null)
                {
                    editText.SetPadding(0,0,0,0);
                }
                */



                //set cursor
                IntPtr IntPtrtextViewClass = JNIEnv.FindClass(typeof(TextView));
                IntPtr mCursorDrawableResProperty = JNIEnv.GetFieldID(IntPtrtextViewClass, "mCursorDrawableRes", "I");
                JNIEnv.SetField(Control.Handle, mCursorDrawableResProperty, 0); // replace 0 with a Resource.Drawable.my_cursor 



                Control.ForceLayout();
            }
            catch (Exception ex)
            {
                //System.Diagnostics.Debug.WriteLine(@"ERROR:", ex.Message);
            }
        }


        protected EntryCentered FormsControl { get; private set; }


        protected CustomFiler InputFilter { get; set; }

        private EditText _nativeView;

        private InputTypes _backupType;

        protected EditText NativeView
        {
            get
            {
                return Control;
            }
        }

        class ActionListener : Java.Lang.Object, TextView.IOnEditorActionListener
        {
            public ActionListener(HandlerEntryCentered renderer)
            {
                _weakParent = new WeakReference<HandlerEntryCentered>(renderer);
            }

            public bool OnEditorAction(TextView v, ImeAction actionId, KeyEvent e)
            {
                if (!_weakParent.TryGetTarget(out HandlerEntryCentered renderer))
                {
                    return false;
                }

                renderer.FormsControl.SendCompleted();

                if (renderer.FormsControl.UnfocusLocked)
                {
                    return true;
                }

                return false;
            }

            private WeakReference<HandlerEntryCentered> _weakParent;

        }

        private void TextChanged(object? sender, TextChangedEventArgs changedEventArgs)
        {
            var check = NativeView.InputType;
            ApplyDynamicLook();
            FormsControl.OnEditing();
        }

        private void FocusChanged(object? sender, View.FocusChangeEventArgs eh)
        {
            if (eh.HasFocus)
            {
                var check = NativeView.InputType;

                //````````````````````````````````````````````````
                // Focused
                //````````````````````````````````````````````````
                if (Android.OS.Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop)
                {

                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        await Task.Delay(200);
                        var imm = (InputMethodManager)Control.Context.GetSystemService(Context.InputMethodService);
                        //    imm.ShowSoftInput(editText, ShowFlags.Implicit);
                    });
                }

                //FormsControl.IsFocused = true;

                FormsControl.OnFocused();
            }
            else
            //````````````````````````````````````````````````
            // Unfocused
            //````````````````````````````````````````````````
            {
                //if (FormsControl.UnfocusLocked)
                //    return;

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Task.Delay(500);
                    var imm = (InputMethodManager)Android.App.Application.Context.GetSystemService(Context.InputMethodService);
                    //  imm.HideSoftInputFromWindow(editText.WindowToken, 0);
                });

                //FormsControl.IsFocused = false;

                FormsControl.OnUnfocused();
            }
        }


        private void OnRendererNeedUpdate(object sender, EventArgs e)
        {
            InputFilter.SetInputFilter(FormsControl.InputFilter);
            UpdateSkin();
        }





    }




}
