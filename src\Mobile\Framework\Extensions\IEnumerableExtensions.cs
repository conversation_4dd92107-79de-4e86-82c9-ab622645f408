﻿using AppoMobi.Framework.Abstractions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using DrawnUi.Draw;

namespace AppoMobi.Framework.Extensions
{

    public static partial class IEnumerableExtensions
    {

        public static void AddIfNotExists<T>(this ICollection<T> list, T add) where T : IHasKey
        {
            if (list.All(x => x.Key != add.Key))
                list.Add(add);
        }

        public static void AddIfNotExists<T>(this List<T> list, T add) where T : IHasKey
        {
            if (list.All(x => x.Key != add.Key))
                list.Add(add);
        }



        /// <summary>
        /// FirstOrDefault(x => x.Id == id)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static T FindById<T>(this IEnumerable<T> source, string id) where T : class, IHasStringId
        {
            return (T)source.FirstOrDefault(x => x.Id == id);
        }

        public static T FindById<T>(this IEnumerable<T> source, int id) where T : class, IHasIntId
        {
            return (T)source.FirstOrDefault(x => x.Id == id);
        }



        /// <summary>
        /// FirstOrDefault(x => x.Key == key)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static T FindByKey<T>(this IEnumerable<T> source, string key) where T : class, IHasKey
        {
            var found = source.FirstOrDefault(x => x.Key == key);
            return (T)found;
        }


        public static void ResetSelection(this IEnumerable<ICanBeSelected> source)
        {
            foreach (var item in source)
            {
                item.Selected = false;
            }
        }

        public static void ResetSelection(this IEnumerable<ISelectableOption> source)
        {
            foreach (var item in source)
            {
                item.Selected = false;
            }
        }

        public static bool SelectById(this IEnumerable<ISelectableOption> source, string id)
        {
            var found = source.FirstOrDefault(x => x.Id == id);
            if (found == null) return false;
            found.Selected = true;
            return true;
        }

        public static T Selected<T>(this IEnumerable<T> source) where T : class, ISelectableOption
        {
            return (T)source.FirstOrDefault(x => x.Selected);
        }

        public static IEnumerable<T> SelectedMany<T>(this IEnumerable<T> source, int id) where T : class, ISelectableOption
        {
            return source.Where(x => x.Selected);
        }

        public static void Select<T>(this IEnumerable<T> source, string id) where T : class, ISelectableOption
        {
            foreach (var member in source)
            {
                member.Selected = member.Id == id;
            }
        }

        public static void Select<T>(this IEnumerable<T> source, IEnumerable<string> ids) where T : class, ISelectableOption
        {
            if (ids == null)
                return;

            foreach (var member in source)
            {
                member.Selected = ids.Contains(member.Id);
            }
        }

        public static int TotalSelected(this IEnumerable<ICanBeSelected> This)
        {
            return This.Count(x => x.Selected);
        }





    }


    //public class ObservableOptionsWithId<T> : NiftyObservableCollection<T>, INotifyPropertyChanged where T : ICanBeSelected, IHasStringId
    //{
    //    
    //    public T GetById(string id)
    //    
    //    {
    //        if (this.Any())
    //        {
    //            return this.FirstOrDefault(x => x.Id == id);
    //        }
    //        return default(T);
    //    }
    //    
    //    public bool SelectOne(string id)
    //    
    //    {
    //        var found = this.FirstOrDefault(x => x.Id == id);
    //        if (found == null) return false;
    //        found.Selected = true;
    //        return true;
    //    }
    //    
    //    public new T Selected
    //    
    //    {
    //        get
    //        {
    //            return this.FirstOrDefault(x => x.Selected);
    //        }
    //        set
    //        {
    //            SelectOne(value);
    //        }
    //    }
    //    
    //    public IEnumerable<T> SelectedMany
    //    
    //    {
    //        get
    //        {
    //            return this.Where(x => x.Selected);
    //        }
    //    }
    //    
    //    public void SelectOne(T item)
    //    
    //    {
    //        if (item != null)
    //        {
    //            foreach (var member in this)
    //            {
    //                if (member.Id == item.Id)
    //                {
    //                    member.Selected = true;
    //                }
    //                else
    //                {
    //                    member.Selected = false;
    //                }
    //            }
    //        }
    //        else
    //        {
    //            //null

    //        }
    //        OnPropertyChanged(new PropertyChangedEventArgs("Selected"));
    //    }


    //}

}
