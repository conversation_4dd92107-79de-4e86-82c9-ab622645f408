﻿using System;
using AppoMobi.Tenant;


namespace AppoMobi.UI
{
    //*******************************************************************************
    public class FeedFrameSwitcher : ContentView
    //*******************************************************************************
    {
        public string UID { get; set; }

        private View _frame { get; set; }

        
        public FeedFrameSwitcher()
        
        {
            UID = Guid.NewGuid().ToString();
            if (Core.IsAndroid)
            {
                if (Core.AndroidAPI < 24)
                {
                    _frame = new FeedFrameOldAndroid();
                    return;
                }
            }
            _frame = new FeedFrame();
        }

        
        public void Init()
        
        {
            var frameContent = Content;

            if (Core.IsAndroid)
            {
                if (Core.AndroidAPI < 24)
                {
                    _frame = new FeedFrameOldAndroid();
                }
            }
            if (_frame == null)
                _frame = new FeedFrame();

      

            ((FeedFrame)_frame).Content = frameContent;

            Content = _frame;
        }



    }

    

    public class FrameShadow : Frame
    {
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                if (propertyName == "Scale")
                {
                    var stop = true;
                }
            });


            base.OnPropertyChanged(propertyName);
        }

        public FrameShadow()
        {
            IsClippedToBounds = false;

            if (TenantOptions.DarkSkin)
                BackgroundColor = AppColors.Controls;
            else
                BackgroundColor = Colors.White;

            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                CornerRadius = 6;
                HasShadow = true;
                if (TenantOptions.DarkSkin)
                    BorderColor = Colors.DimGray;
                else
                    BorderColor = Colors.White; //this is fix for android 6.0 when shadow is not showing
                //    BorderColor=Colors.White;
                //ImageWait.BitmapOptimizations = true;

            }
            else
            {
                HasShadow = true;
                CornerRadius = 6;
                Opacity = 0.10;

            }

        }


    }

    //*******************************************************************************
    public class FeedFrame  : Frame
    //*******************************************************************************
    {
        //this is renderered by xamarin

        public string UID { get; set; }

        public FeedFrame()
        {
            UID = Guid.NewGuid().ToString();

            HasShadow = true;
                BackgroundColor = AppColors.Cards;
            IsClippedToBounds = true;

            //BackgroundColor = Colors.Red;//AppColors.Cards;
            //HorizontalOptions = LayoutOptions.FillAndExpand;
            //VerticalOptions = LayoutOptions.FillAndExpand;
            ;
            Padding = 0;
            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                CornerRadius = 6;
            }
            else
            {
                CornerRadius = 6;
            }

        }
    }

    //*******************************************************************************
    public class FeedFrameOldAndroid : FeedFrame
    //*******************************************************************************
    {        
        //this is renderered by a custom renderer
        public FeedFrameOldAndroid()
        {
            BackgroundColor = AppColors.Cards;

        }
    }
}
