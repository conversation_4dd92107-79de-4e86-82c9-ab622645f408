﻿using AppoMobi.Xam;
using AppoMobi.Xam.Extensions;

namespace AppoMobi.Models
{
    public class NiftyBaseModel : BaseViewModelBak
    {
        public bool OnSelectedItem<T>(T item, NiftyObservableCollection<T> list, params object[] raiseProperties)
        {
            if (item != null)
            {
                string myId = Reflection.GetPropertyValueFor<string>(item, "Id", string.Empty);
                var selected = Reflection.GetPropertyValueFor(list, "Selected");
                string selectedId = Reflection.GetPropertyValueFor<string>(selected, "Id", string.Empty);


                if (selectedId == myId)
                {
                    return false; //reselected
                }

                //Reflection.TrySetPropertyValue(selected, "Selected", false);

                foreach (var member in list)
                {

                    string thisId = Reflection.GetPropertyValueFor(member, "Id", string.Empty);
                    if (thisId == myId)
                    {
                        Reflection.TrySetPropertyValue(member, "Selected", true);
                        Reflection.TrySetPropertyValue(list, "Selected", member);
                        Reflection.TrySetPropertyValue(list, "SelectionLocked", true);
                        continue;
                    }
                    Reflection.TrySetPropertyValue(member, "Selected", false);
                }
                OnPropertyChanged("Selected");
            }

            if (raiseProperties != null)
            {
                foreach (var prop in raiseProperties)
                {
                    OnPropertyChanged(prop.ToString());
                }
            }
            return true;
        }




        public void SetReady()
        {
            IsLoading = false;
            //            HasContent = true;
        }




        public void RedrawItems<T>(IEnumerable<T> buffer, ref NiftyObservableCollection<T> front, bool force = false, string page = null)

        {
            if (string.IsNullOrEmpty(page)) page = CurrentView;
            if (page == null) return;
            if (page.Contains(page))
            {
                if ((!front.Refreshed && buffer?.Count() > 0) || force)
                {
                    front.RefreshRange(0, buffer.Clone());
                    front.Refreshed = true;
                }
            }
        }


        public NiftyBaseModel()

        {



        }

        private string _CurrentView;
        public string CurrentView
        {
            get { return _CurrentView; }
            set
            {
                if (_CurrentView != value)
                {
                    _CurrentView = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Loads clones to front and sets flags.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="buffer"></param>
        /// <param name="front"></param>
        /// <param name="forceRefresh"></param>
        /// <returns></returns>

        public bool LoadItems<T>(IEnumerable<T> buffer, ref NiftyObservableCollection<T> front, bool forceRefresh = false)

        {
            if (forceRefresh)
                front.Loaded = false;

            if (front.Loaded) //just refresh
            {
                front.Refreshed = false; //to be refreshed visually
            }
            else
            {
                //todo check if need refreshed flag to be false
                //primary set without refresh
                try
                {
                    var newList = buffer.Clone();


                    // Update the UI
                    front.Clear();
                    front.AddRange(newList);
                    front.Refreshed = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    front.Refreshed = false;
                    return false;
                }
            }
            front.Loaded = true;
            return true;
        }
        /// <summary>
        /// Call this when page is displayed to visually refresh data
        /// </summary>

        public void RefreshItems<T>(IEnumerable<T> buffer, ref NiftyObservableCollection<T> front)

        {
            if (!front.Refreshed && buffer?.Count() > 0)
            {
                front.RefreshRange(0, buffer.Clone());
                front.Refreshed = true;
            }
        }

        private bool _isLoading = true;
        public bool IsLoading

        {
            get { return _isLoading; }

            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }


        private bool _isOffline = false;
        public bool IsOffline

        {
            get { return _isOffline; }

            set
            {
                _isOffline = value;
                OnPropertyChanged();
            }

        }
    }
}
