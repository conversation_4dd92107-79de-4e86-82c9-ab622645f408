﻿using System;
using System.Globalization;

namespace AppoMobi.Xam.Converters
{
    public class DoubleToHalfFloatConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var @double = (double)value;
            var result = (float)@double;

            if (result < 0)
            {
                return 0;
            }

            var half = result / 2.0;

            return half;
        }
 
    }
}
