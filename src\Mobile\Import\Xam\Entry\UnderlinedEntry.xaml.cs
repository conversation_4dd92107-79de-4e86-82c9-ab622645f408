﻿using System;
using AppoMobi.Forms.Common.ResX;



namespace AppoMobi.Xam
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class UnderlinedEntry
	{



        //public EntryCentered Entry { get; set; }

        //public string Text => cEntry.Text;
        //public string Placeholder => cEntry.Placeholder;

	    //-------------------------------------------------------------
	    // PlaceholderSmallTextSize
	    //-------------------------------------------------------------
	    private const string namePlaceholderSmallTextSize = "PlaceholderSmallTextSize";
	    public static readonly BindableProperty PlaceholderSmallTextSizeProperty = BindableProperty.Create(namePlaceholderSmallTextSize, typeof(double), typeof(UnderlinedEntry), 10.0); //, BindingMode.TwoWay
	    public double PlaceholderSmallTextSize
	    {
	        get { return (double)GetValue(PlaceholderSmallTextSizeProperty); }
	        set { SetValue(PlaceholderSmallTextSizeProperty, value); }
	    }

        //-------------------------------------------------------------
        // Required
        //-------------------------------------------------------------
        private const string nameRequired = "Required";
        public static readonly BindableProperty RequiredProperty = BindableProperty.Create(nameRequired, typeof(bool), typeof(UnderlinedEntry), false); //, BindingMode.TwoWay
        public bool Required
        {
            get { return (bool)GetValue(RequiredProperty); }
            set { SetValue(RequiredProperty, value); }
        }


        //-------------------------------------------------------------
        // EntryHeightRequest
        //-------------------------------------------------------------
        private const string nameEntryHeightRequest = "EntryHeightRequest";
        public static readonly BindableProperty EntryHeightRequestProperty = BindableProperty.Create(nameEntryHeightRequest, typeof(double), typeof(UnderlinedEntry), -1.0); //, BindingMode.TwoWay
        public double EntryHeightRequest
        {
            get { return (double)GetValue(EntryHeightRequestProperty); }
            set { SetValue(EntryHeightRequestProperty, value); }
        }	




        
        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {
                case nameEntryHeightRequest:
                    cEntry.HeightRequest = EntryHeightRequest;
                    cDisplay.HeightRequest = EntryHeightRequest;
                    break;

                case nameNativePlaceholderText:
                    if (HideNativePlaceholder)
                    {
                        cEntry.Placeholder = NativePlaceholderText;
                    }
                    break;

	            case namePlaceholderSmallTextSize:
	                cPlaceholder.FontSize = PlaceholderSmallTextSize;
                    break;

                case nameHideNativePlaceholder:
	                if (HideNativePlaceholder)
	                {
	                    cEntry.Placeholder = NativePlaceholderText;
                    }
	                else
	                {
	                    cEntry.Placeholder = PlaceholderText;
                    }
                    UpdateBottomPlaceholder();
	                break;

                case namePlaceholderText:
                    cPlaceholder.Text = PlaceholderText;
                    if (HideNativePlaceholder)
                    {
                        cEntry.Placeholder = NativePlaceholderText;
                    }
                    else
                    {
                        cEntry.Placeholder = PlaceholderText;
                    }
	                break;

	            case nameText:
                    UpdateFilteredDisplay();
                    UpdateBottomPlaceholder();
	                break;

	            case nameKeyboard:
	                cEntry.Keyboard = Keyboard;
	                break;

	            case nameReadOnly:
	                cEntry.InputTransparent = ReadOnly;
	                if (FadeUnfocused)
	                {
	                    if (ReadOnly) 
	                        cEntry.Opacity = 0.5;
	                    else
	                        cEntry.Opacity = 1.0;
	                }
                    else
	                {
	                    
	                }
	                break;

	            case nameTextSize:
	                cEntry.TextSize = TextSize;
                    cDisplay.FontSize = TextSize;
                    break;

	            case namePlaceholderActiveColor:
	                UpdateBottomPlaceholder();
                    break;

	            case nameprop1:
	                cEntry.PlaceholderTextSize = prop1;
	                break;

            }

        }
        
	    public void AjustEntryHeight(double height)
        
	    {
	        cEntry.HeightRequest = height;
        }


        //-------------------------------------------------------------
        // HideNativePlaceholder
        //-------------------------------------------------------------
        private const string nameHideNativePlaceholder = "HideNativePlaceholder";
        public static readonly BindableProperty HideNativePlaceholderProperty = BindableProperty.Create(nameHideNativePlaceholder, typeof(bool), typeof(UnderlinedEntry), false); //, BindingMode.TwoWay
        public bool HideNativePlaceholder
        {
            get { return (bool)GetValue(HideNativePlaceholderProperty); }
            set { SetValue(HideNativePlaceholderProperty, value); }
        }

	    //-------------------------------------------------------------
	    // NativePlaceholderText
	    //-------------------------------------------------------------
	    private const string nameNativePlaceholderText = "NativePlaceholderText";
	    public static readonly BindableProperty NativePlaceholderTextProperty = BindableProperty.Create(nameNativePlaceholderText, typeof(string), typeof(UnderlinedEntry), ""); //, BindingMode.TwoWay
	    public string NativePlaceholderText
	    {
	        get { return (string)GetValue(NativePlaceholderTextProperty); }
	        set { SetValue(NativePlaceholderTextProperty, value); }
	    }




        //-------------------------------------------------------------
        // prop1
        //-------------------------------------------------------------
        private const string nameprop1 = "prop1";
        public static readonly BindableProperty prop1Property = BindableProperty.Create(nameprop1, typeof(double), typeof(UnderlinedEntry), 1.0); //, BindingMode.TwoWay
        public double prop1
        {
            get { return (double)GetValue(prop1Property); }
            set { SetValue(prop1Property, value); }
        }	




        //-------------------------------------------------------------
        // TextSize
        //-------------------------------------------------------------
        private const string nameTextSize = "TextSize";
        public static readonly BindableProperty TextSizeProperty = BindableProperty.Create(nameTextSize, typeof(double), typeof(UnderlinedEntry), FontSizes.Medium); //, BindingMode.TwoWay
        public double TextSize
        {
            get { return (double)GetValue(TextSizeProperty); }
            set { SetValue(TextSizeProperty, value); }
        }	


        //-------------------------------------------------------------
        // Keyboard
        //-------------------------------------------------------------
        private const string nameKeyboard = "Keyboard";
        public static readonly BindableProperty KeyboardProperty = BindableProperty.Create(nameKeyboard, typeof(Keyboard), typeof(UnderlinedEntry), 
            Keyboard.Default); //, BindingMode.TwoWay
        public Keyboard Keyboard
        {
            get { return (Keyboard)GetValue(KeyboardProperty); }
            set { SetValue(KeyboardProperty, value); }
        }	



        //-------------------------------------------------------------
        // Text
        //-------------------------------------------------------------
        private const string nameText = "Text";
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(UnderlinedEntry), string.Empty); //, BindingMode.TwoWay
        public string Text
        {
            get
            {
                return cEntry.Text;
                //return (string)GetValue(TextProperty);
            }
            set
            {
                cEntry.Set(value);
                SetValue(TextProperty, value);
            }
        }	

    

        //-------------------------------------------------------------
        // PlaceholderText
        //-------------------------------------------------------------
        private const string namePlaceholderText = "PlaceholderText";
        public static readonly BindableProperty PlaceholderTextProperty = BindableProperty.Create(namePlaceholderText, typeof(string), typeof(UnderlinedEntry), string.Empty); //, BindingMode.TwoWay
        public string PlaceholderText
        {
            get { return (string)GetValue(PlaceholderTextProperty); }
            set { SetValue(PlaceholderTextProperty, value); }
        }


        //-------------------------------------------------------------
        // ReadOnly
        //-------------------------------------------------------------
        private const string nameReadOnly = "ReadOnly";
        public static readonly BindableProperty ReadOnlyProperty = BindableProperty.Create(nameReadOnly, typeof(bool), typeof(UnderlinedEntry), false); //, BindingMode.TwoWay
        public bool ReadOnly
        {
            get { return (bool)GetValue(ReadOnlyProperty); }
            set { SetValue(ReadOnlyProperty, value); }
        }

	    //-------------------------------------------------------------
	    // FadeUnfocused
	    //-------------------------------------------------------------
	    private const string nameFadeUnfocused = "FadeUnfocused";
	    public static readonly BindableProperty FadeUnfocusedProperty = BindableProperty.Create(nameFadeUnfocused, typeof(bool), typeof(UnderlinedEntry), false); //, BindingMode.TwoWay
	    public bool FadeUnfocused
	    {
	        get { return (bool)GetValue(FadeUnfocusedProperty); }
	        set { SetValue(FadeUnfocusedProperty, value); }
	    }

        //-------------------------------------------------------------
        // PlaceholderActiveColor
        //-------------------------------------------------------------
        private const string namePlaceholderActiveColor = "PlaceholderActiveColor";
        public static readonly BindableProperty PlaceholderActiveColorProperty = BindableProperty.Create(namePlaceholderActiveColor, typeof(Color), typeof(UnderlinedEntry), TextColors.PlaceholderActive); //, BindingMode.TwoWay
        public Color PlaceholderActiveColor
        {
            get { return (Color)GetValue(PlaceholderActiveColorProperty); }
            set { SetValue(PlaceholderActiveColorProperty, value); }
        }	

        public new bool IsFocused { get; private set; }

        
        public UnderlinedEntry()
        
		{
			InitializeComponent ();
          
            try
            {
		        cPlaceholder.Text = cEntry.Placeholder;
                cPlaceholder.TextColor = cEntry.PlaceholderColor;
            }
		    catch
		    {
		    }


            if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
		    {
		        cGradientLine.HeightRequest = 0.5;
		        cPlaceholder.TranslationY = 0;
		    }
		    else
		    {
		        cGradientLine.HeightRequest = 0.8;
		        cPlaceholder.TranslationY = 0.5;
            }

		    UpdateBottomPlaceholder();

            //todo dispose unfocused
            cEntry.UnfocusedEvent += CEntry_OnUnfocused;
		    cEntry.FocusedEvent += CEntry_OnFocused;
		    cEntry.EditingEvent += CEntry_OnEditing;

        }
        
	    private void CEntry_OnEditing(object sender, EventArgs e)
        
	    {
	        UpdateBottomPlaceholder();
	        EditingEvent?.Invoke(sender, e);
            UpdateFilteredDisplay();
        }
        
	    private void CEntry_OnFocused(object sender, EventArgs e)
        
	    {
	        IsFocused = true;
            UpdateBottomPlaceholder();
	        FocusedEvent?.Invoke(sender, e);
        }


        protected void UpdateFilteredDisplay()
        {
            if (DisplayFilter != null)
            {
                if (Text?.Length > 0)
                {
                    cEntry.Opacity = 0.0001;
                }
                else
                {
                    cEntry.Opacity = 1;
                }
                cDisplay.Text = DisplayFilter.Invoke(Text);
            }
        }



        
        private void CEntry_OnUnfocused(object sender, EventArgs e)
        
	    {
	        IsFocused = false;
	        UpdateBottomPlaceholder();
            UnfocusedEvent?.Invoke(sender, e);
        }

                
        public Func<string, string> DisplayFilter { get; set; }

        
        public void SetInputFilter(Func<string, int, int, string> filter)
            
        {
            cEntry.SetInputFilter(filter);

        }

        
        public void SetDisplayFilter(Func<string, string> filter)
        
        {
            DisplayFilter = filter;
            if (filter == null)
            {
                cDisplay.IsVisible = false;
            }
            else
            {
                cDisplay.IsVisible = true;
            }
        UpdateFilteredDisplay();
        }


        
        protected void UpdateBottomPlaceholder()
        
	    {
	        if (IsFocused)
	        {
	            cPlaceholder.TextColor = PlaceholderActiveColor;
	            if (FadeUnfocused)
	            {
	                cEntry.TextColor = TextColors.Entry;
	            }
            }
	        else
	        {
	            cPlaceholder.TextColor = cEntry.PlaceholderColor;
	            if (FadeUnfocused)
	            {
	                cEntry.TextColor = TextColors.Placeholder;
                }
            }

            if (string.IsNullOrEmpty(cEntry.Text) && Required)
                cPlaceholder.Text = ResStrings.Required;
            else
                cPlaceholder.Text = PlaceholderText;

            if (string.IsNullOrEmpty(cEntry.Text) && !HideNativePlaceholder)
	        {
                if (Required)
                {
                    //required
                    cPlaceholder.IsVisible = true;
                }
                else
                {
                    cPlaceholder.IsVisible = false;
                }
            }
	        else
	        {
	            cPlaceholder.IsVisible = true;
	        }

        }

        /// <summary>
        /// Focus left the field
        /// </summary>
        public event EventHandler UnfocusedEvent;

	    //focus left the entry
	    /// <summary>
	    /// Focus entered field
	    /// </summary>
	    public event EventHandler FocusedEvent;

	    /// <summary>
	    /// Fires when text is typed so you can react to changes in entry
	    /// </summary>
	    public event EventHandler EditingEvent;


    }
}