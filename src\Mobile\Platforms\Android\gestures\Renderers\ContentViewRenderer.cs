using Android.Content;
using Android.Views;
using AppoMobi.Touch;
using AppoMobi.Touch.Droid;
using System;
using System.ComponentModel;
using AppoMobi.Touch.Droid.Renderers;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;

//[assembly: ExportRenderer(typeof(AppoMobi.Touch.ContentView), typeof(AppoMobi.Touch.Droid.Renderers.ContentViewRenderer))]
namespace AppoMobi.Touch.Droid.Renderers
{
	public class ContentViewRenderer : VisualElementRenderer<LegacyGesturesContentView>
	{
		public ContentViewRenderer(Context context) : base(context)
		{
		}

		public override bool DispatchTouchEvent(MotionEvent e)
		{
			AndroidGestureHandler.HandleMotionEvent(base.Element, this, e);
			return base.DispatchTouchEvent(e);
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				AndroidGestureHandler.RemoveInstance(base.Element, this);
			}
			base.Dispose(disposing);
		}

		protected override void OnElementChanged(ElementChangedEventArgs<LegacyGesturesContentView> e)
		{
			base.OnElementChanged(e);
			AndroidGestureHandler.OnElementChanged(e.OldElement, e.NewElement, this);
		}

		protected override void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e)
		{
			base.OnElementPropertyChanged(sender, e);
			AndroidGestureHandler.OnElementPropertyChanged(sender, e, this);
		}
	}
}