﻿namespace AppoMobi.Droid.Camera.Services
{

    /// <summary>
    /// Camera operation class, Abstract class, ready for Camera1 that may need to be used
    /// </summary>
    public abstract class CameraLoader: Java.Lang.Object
    {
        //public byte[] Data { get; set; }

        public OnPreviewFrameListener mOnPreviewFrameListener;

        public abstract void OnResume(int width, int height);

        public abstract void onPause();

        public abstract void switchCamera();

        public abstract int getCameraOrientation();

        public abstract bool hasMultipleCamera();

        public abstract bool isFrontCamera();

        public void setOnPreviewFrameListener(OnPreviewFrameListener onPreviewFrameListener)
        {
            mOnPreviewFrameListener = onPreviewFrameListener;
        }

        public interface OnPreviewFrameListener
        {
            void onPreviewFrame(byte[] data, int width, int height);
        }

    }
}

