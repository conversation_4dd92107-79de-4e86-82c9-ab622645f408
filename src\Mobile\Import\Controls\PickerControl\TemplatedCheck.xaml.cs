﻿using System.Windows.Input;
using AppoMobi.Touch;


namespace AppoMobi.Forms.Controls.Input
{
 
    public partial class TemplatedCheck 
    {
        public TemplatedCheck()
        {
            InitializeComponent();
        }


        #region Command


        public static readonly BindableProperty ChangedCommandProperty = BindableProperty.Create(
            nameof(ChangedCommand),
            typeof(ICommand),
            typeof(TemplatedCheck),
            null);

        public ICommand ChangedCommand
        {
            get { return (ICommand)GetValue(ChangedCommandProperty); }
            set { SetValue(ChangedCommandProperty, value); }
        }


        public static readonly BindableProperty CommandParameterProperty = BindableProperty.Create(
            nameof(CommandParameter),
            typeof(object),
            typeof(TemplatedCheck),
            null);

        public object CommandParameter
        {
            get { return (object)GetValue(CommandParameterProperty); }
            set { SetValue(CommandParameterProperty, value); }
        }



        #endregion

        public ICommand CommandInternalTapped
        {
            get
            {
                return new Command(async (object context) =>
                {
                    Checked = !Checked;
                });
            }
        }

        
        //-------------------------------------------------------------
        // SvgTintColor
        //-------------------------------------------------------------
        private const string nameSvgTintColor = "SvgTintColor";
        public static readonly BindableProperty SvgTintColorProperty = BindableProperty.Create(nameSvgTintColor, typeof(Color), typeof(TemplatedCheck), Colors.Transparent); //, BindingMode.TwoWay
        public Color SvgTintColor
        {
            get { return (Color)GetValue(SvgTintColorProperty); }
            set { SetValue(SvgTintColorProperty, value); }
        }	


        //-------------------------------------------------------------
        // SvgUnselected
        //-------------------------------------------------------------
        private const string nameSvgUnselected = "SvgUnselected";
        public static readonly BindableProperty SvgUnselectedProperty = BindableProperty.Create(nameSvgUnselected, typeof(string), typeof(TemplatedCheck),
            "couch.checkb.svg");
        public string SvgUnselected
        {
            get { return (string)GetValue(SvgUnselectedProperty); }
            set { SetValue(SvgUnselectedProperty, value); }
        }

        //-------------------------------------------------------------
        // SvgSelected
        //-------------------------------------------------------------
        private const string nameSvgSelected = "SvgSelected";
        public static readonly BindableProperty SvgSelectedProperty = BindableProperty.Create(nameSvgSelected, typeof(string), typeof(TemplatedCheck),
            "couch.checkbfill.svg");
        public string SvgSelected
        {
            get { return (string)GetValue(SvgSelectedProperty); }
            set { SetValue(SvgSelectedProperty, value); }
        }

        //-------------------------------------------------------------
        // Tag
        //-------------------------------------------------------------
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(TemplatedCheck), string.Empty); //, BindingMode.TwoWay
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }



        //-------------------------------------------------------------
        // Checked
        //-------------------------------------------------------------
        private const string nameChecked = "Checked";

        public static readonly BindableProperty CheckedProperty =
            BindableProperty.Create(nameChecked, typeof(bool), typeof(TemplatedCheck), false, BindingMode.TwoWay);


 

        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == nameChecked)
            {
                if (ChangedCommand != null)
                {
                    if (ChangedCommand.CanExecute(null))
                    {
                        ChangedCommand?.Execute(CommandParameter);
                    }
                }
            }

        }


        public bool Checked
        {
            get { return (bool)GetValue(CheckedProperty); }
            set { SetValue(CheckedProperty, value); }
        }


        private void OnTapped(object sender, TapEventArgs e)
        {
            Checked = !Checked;
        }

    }
}