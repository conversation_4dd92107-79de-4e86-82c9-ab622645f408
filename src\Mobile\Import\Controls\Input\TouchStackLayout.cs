﻿using AppoMobi.Touch;


namespace AppoMobi.Forms.Controls
{
    public class TouchStackLayout : AppoMobi.Touch.LegacyGesturesStackLayout
    {
        public TouchStackLayout()
        {
            Down += OnDown;
            Up += OnUp;
        }

        //-------------------------------------------------------------
        // DownOpacity
        //-------------------------------------------------------------
        private const string nameDownOpacity = "DownOpacity";
        public static readonly BindableProperty DownOpacityProperty = BindableProperty.Create(nameDownOpacity, typeof(double), typeof(TouchStackLayout), 0.75); //, BindingMode.TwoWay
        private double _savedOpacity;

        public double DownOpacity
        {
            get { return (double)GetValue(DownOpacityProperty); }
            set { SetValue(DownOpacityProperty, value); }
        }


        private bool _TouchDown;
        public bool TouchDown
        {
            get { return _TouchDown; }
            set
            {
                if (_TouchDown != value)
                {
                    _TouchDown = value;
                    OnPropertyChanged();
                }
            }
        }

        private void OnUp(object sender, DownUpEventArgs e)
        {

            Scale = 1.0;
            Opacity = _savedOpacity;
            TouchDown = false;
        }

        private void OnDown(object sender, DownUpEventArgs e)
        {

            if (_savedOpacity != DownOpacity)
                _savedOpacity = Opacity;

            Opacity = DownOpacity;
            Scale = 0.985;

            TouchDown = true;
        }
    }
}