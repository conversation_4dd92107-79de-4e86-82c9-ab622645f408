using Android.Content;
using Android.Views;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace AppoMobi.Touch.Droid
{
	internal class TapGestureDetector
	{
		protected readonly ITapGestureListener Listener;

		protected readonly int touchSlopSquare;

		protected readonly int longPressTimeout;

		private Dictionary<int, Point> touches = new Dictionary<int, Point>();

		private long lastdown;

		private bool ignoreGesture;

		private bool isLongPressing;

		private CancellationTokenSource cancelLongPress;

		internal TapGestureDetector(Context context, ITapGestureListener listener)
		{
			this.Listener = listener;
			int scaledTouchSlop = ViewConfiguration.Get(context).ScaledTouchSlop;
			this.touchSlopSquare = scaledTouchSlop * scaledTouchSlop;
			this.longPressTimeout = ViewConfiguration.LongPressTimeout;
		}

		private bool AnyPointerMoved(MotionEvent e)
		{
			for (int i = 0; i < e.PointerCount; i++)
			{
				int pointerId = e.GetPointerId(i);
				if (this.touches.ContainsKey(pointerId))
				{
					Point item = this.touches[pointerId];
					double x = item.X - (double)e.GetX(i);
					double y = item.Y - (double)e.GetY(i);
					if (x * x + y * y > (double)this.touchSlopSquare)
					{
						return true;
					}
				}
				else
				{
					this.lastdown = e.EventTime;
					this.touches.Add(pointerId, new Point((double)e.GetX(i), (double)e.GetY(i)));
				}
			}
			return false;
		}

		private bool EndGesture(MotionEvent e, bool cancel = false)
		{
			if (this.cancelLongPress != null)
			{
				try
				{
					this.cancelLongPress.Cancel();
				}
				catch
				{
				}
			}
			bool flag = false;
			if (this.isLongPressing)
			{
				this.Listener.OnLongPressed(e, cancel);
			}
			else if (this.lastdown > (long)0 && (e.ActionMasked == (MotionEventActions)1 || e.ActionMasked == (MotionEventActions)6))
			{
				flag = this.Listener.OnTapping(e);
			}
			this.ignoreGesture = true;
			this.isLongPressing = false;
			this.touches.Clear();
			this.lastdown = (long)0;
			return flag;
		}

		public bool OnTouchEvent(MotionEvent e)
		{
			bool flag = false;
			switch (e.ActionMasked)
			{
				case (MotionEventActions)0:
				{
					if (this.cancelLongPress != null)
					{
						try
						{
							this.cancelLongPress.Cancel();
						}
						catch
						{
						}
					}
					if (e.PointerCount == 1)
					{
						this.ignoreGesture = false;
						this.isLongPressing = false;
						this.touches.Clear();
					}
					for (int i = 0; i < e.PointerCount; i++)
					{
						int pointerId = e.GetPointerId(i);
						if (!this.touches.ContainsKey(pointerId))
						{
							this.lastdown = e.EventTime;
							this.touches.Add(pointerId, new Point((double)e.GetX(i), (double)e.GetY(i)));
						}
					}
					this.cancelLongPress = new CancellationTokenSource();

				    Task.Run(async () =>
				    {
				        await Task.Delay(this.longPressTimeout, this.cancelLongPress.Token);
				        this.cancelLongPress = null;
				        if (this.lastdown > (long)0)
				        {
				            this.isLongPressing = true;
				            this.lastdown = (long)0;
				            this.Listener.OnLongPressing(Enumerable.ToArray<Point>(this.touches.Values), (long)this.longPressTimeout);
				        }


                    }, cancelLongPress.Token);

					return flag;
				}
				case (MotionEventActions)1:
				case (MotionEventActions)3:
				case (MotionEventActions)6:
				{
					flag = this.EndGesture(e, false);
					return flag;
				}
				case (MotionEventActions)2:
				{
					if (this.ignoreGesture)
					{
						return flag;
					}
					if (!this.AnyPointerMoved(e))
					{
						if (this.lastdown <= (long)0 || e.EventTime - this.lastdown <= (long)this.longPressTimeout)
						{
							return flag;
						}
						if (this.cancelLongPress != null)
						{
							try
							{
								this.cancelLongPress.Cancel();
							}
							catch
							{
							}
						}
						this.isLongPressing = true;
						this.lastdown = (long)0;
						this.Listener.OnLongPressing(e);
						return flag;
					}
					else
					{
						flag = this.EndGesture(e, true);
						return flag;
					}
				}
				case (MotionEventActions)4:
				case (MotionEventActions)5:
				{
					return flag;
				}
				default:
				{
					return flag;
				}
			}
		}
	}
}