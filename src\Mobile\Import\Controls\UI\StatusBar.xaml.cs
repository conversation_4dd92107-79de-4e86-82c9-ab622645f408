﻿


using AppoMobi.Maui.Navigation;

namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class StatusBar
    {
        private DeviceRotation _orientation;

        public StatusBar()
        {
            InitializeComponent();
            Initialize();
        }

        protected override void OnHandlerChanged()
        {
            base.OnHandlerChanged();

            if (this.Handler == null)
            {
                App.Instance.Messager.Unsubscribe(this, "AppLayoutInvalidated");
            }
            else
            {
                App.Instance.Messager.Subscribe<string>(this, "AppLayoutInvalidated", async (sender, arg) =>
                {
                    Update(_orientation);
                });
            }
        }

        protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == nameUseGradient)
            {
                ControlGradient.IsVisible = UseGradient;
            }
        }

        //-------------------------------------------------------------
        // UseGradient
        //-------------------------------------------------------------
        private const string nameUseGradient = "UseGradient";
        public static readonly BindableProperty UseGradientProperty = BindableProperty.Create(nameUseGradient, typeof(bool), typeof(StatusBar), false); //, BindingMode.TwoWay
        public bool UseGradient
        {
            get { return (bool)GetValue(UseGradientProperty); }
            set { SetValue(UseGradientProperty, value); }
        }	


        /// <summary>
        /// You have to call it manually when device orientation changes
        /// </summary>
        public double Update(DeviceRotation Orientation)
        {
            _orientation = Orientation;
            var statusHeight = Super.StatusBarHeight;

            Core.StatusBarHeightRequest = statusHeight; //todo remove
            HeightRequest = statusHeight;
            return statusHeight;
        }

        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            Core.StatusBarHeightRequest = Super.StatusBarHeight;

            if (Settings.Current.iModel == "iPhone X")
            {
                
            }
            else
            {
                HeightRequest = Core.StatusBarHeightRequest;
            }
            
        }


        #region Properties



        #endregion

    }



}