﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="AppoMobi.NiftyShare"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:appoMobi="clr-namespace:AppoMobi">

    <ContentView.Content>
        <gestures:LegacyGesturesGrid
            x:Name="ControlContainer"
            Padding="{StaticResource PaddingNiftyShare}"
            ColumnSpacing="16"
            Down="ControlContainer_OnDown"
            HorizontalOptions="FillAndExpand"
            LongPressed="ControlContainer_OnLongPressed"
            Up="ControlContainer_OnUp">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="OnTapped" />
            </Grid.GestureRecognizers>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <!--  column 0 - TEXT  -->
            <ContentView Grid.Column="0">
                <Label
                    x:Name="ControlLabelText"
                    InputTransparent="True"
                    FontSize="14.5"              TextColor="{x:Static appoMobi:AppColors.BwGrey}"              
                    VerticalOptions="Center" />
            </ContentView>
            <!--  column 1 - SHARE ICON  -->
            <appoMobi:CImage
                Grid.Column="1"
                DownsampleToViewSize="False"
                HeightRequest="25"
                Source="sharew"
                VerticalOptions="Center"
                WidthRequest="25">
                <appoMobi:CImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{StaticResource HexColor_LinkIcon}" />
                </appoMobi:CImage.Transformations>
                <appoMobi:CImage.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnShared" />
                </appoMobi:CImage.GestureRecognizers>
            </appoMobi:CImage>
        </gestures:LegacyGesturesGrid>
    </ContentView.Content>
    <!--  {StaticResource HexColor_LinkIcon}  -->
</ContentView>