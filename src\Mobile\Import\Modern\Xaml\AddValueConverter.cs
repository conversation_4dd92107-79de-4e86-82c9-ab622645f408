﻿using System;
using System.Globalization;


namespace AppoMobi.Forms.Framework.Xaml
{
    public class AddValueConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var divider = ((string)parameter).ToDouble();
                var ret = (double)value + divider;
                return ret;
            }
            catch (Exception e)
            {
            }

            return value;
        }


    }
}