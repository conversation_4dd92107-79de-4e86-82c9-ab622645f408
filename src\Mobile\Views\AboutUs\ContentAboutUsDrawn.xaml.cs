using System;
using System.Collections.Generic;
using AppoMobi.Common.Constants;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Common.Dto;
using AppoMobi.Nifty;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;
using DrawnUi.Draw;

namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ContentAboutUsDrawn : IncludedContent
    {
        public List<CMyListItem> ContactList = new List<CMyListItem>();
        public List<CMyListItem> ContactListMap = new List<CMyListItem>();
        public List<CMyListItem> ContactListRes = new List<CMyListItem>();
        private string appVersion;

        public string AppVersion        
        {
            get => appVersion;
            set
            {
                if (value == appVersion)
                {
                    return;
                }
                appVersion = value;
                OnPropertyChanged();
            }
        }

        private CompanyInfoDTO Model => Core.Current.MyCompany;

        public ContentAboutUsDrawn(IPageEnhancedNav daddy)
        {
            InitializeComponent();
            Init(daddy);

            BindingContext = Core.Current;

            string prms = null;

            // Build contact list
            prms = Core.Current.MyCompany.ContactUsTel;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "tel",
                    FontIcon = FontIcons.fa_phone_square,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsWebsite;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "url",
                    FontIcon = FontIcons.fa_globe,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsEmail;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "mail",
                    FontIcon = FontIcons.fa_envelope,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsFacebook;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "facebook",
                    FontIcon = FontIcons.fa_facebook, FontOverride = "FaBrands",
                    Desc = ResStrings.Facebook,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsVk;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "vk",
                    FontIcon = FontIcons.fa_vk, FontOverride = "FaBrands",
                    Desc = ResStrings.VK,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsInstagram;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "instagram",
                    FontIcon = FontIcons.fa_instagram, FontOverride = "FaBrands",
                    Desc = ResStrings.Instagram,
                    Parameters = prms,
                    Selected = false
                });
            }

            // Setup toolbar icons
            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsTel))
            {
                Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_phone);
                Daddy.RightIcon1Symbol.RotationY = 180;
                Daddy.ToggleButtonVisibility(ButtonType.Right1, true);
            }
            else
            {
                Daddy.ToggleButtonVisibility(ButtonType.Right1, false);
            }

            BuildIconsList();
            BuildDevFooter();
        }

        private void BuildIconsList()
        {
            IconsList.Children.Clear();
            
            foreach (var item in ContactList)
            {
                var iconButton = new SkiaLayer
                {
                    UseCache = SkiaCacheType.Image,
                    Margin = new Thickness(4),
                    HeightRequest = 40,
                };

                var icon = new DrawnFontIcon()
                {
                    Margin = new Thickness(4),
                    LockRatio = 1,
                    FontSize = 24,
                    WidthRequest = 26,
                    TextColor = AppColors.CardsHeaderText,
                    VerticalOptions = LayoutOptions.Center,
                    Preset = item.FontIcon
                };
                iconButton.Children.Add(icon);

                iconButton.Children.Add(new SkiaMarkdownLabel(item.Desc)
                {
                    TextColor = AppColors.CardsHeaderText,
                    Margin = new Thickness(40,0,0,0),
                    MaxLines = 1,
                    VerticalTextAlignment = TextAlignment.Center,
                    VerticalOptions = LayoutOptions.Center
                });

                iconButton.Tapped += (s, e) => OnIconTapped(item);
                
                IconsList.Children.Add(iconButton);
            }
        }

        private void BuildDevFooter()
        {
            AppVersion = ResStrings.PageSettings_PageSettings_Version + " " + Core.Native.GetAppVersion();

            LabelBuild.Text = AppVersion;
        }

        public override void OnRightIcon1Clicked()
        {
            CWorld.Dial(Core.Current.MyCompany.ContactUsTel);
            base.OnRightIcon1Clicked();
        }

        private void OnIconTapped(CMyListItem item)
        {
            HandleContactAction(item);
        }

        private void OnContactItemTapped(CMyListItem item)
        {
            HandleContactAction(item);
        }

        // Legacy method for compatibility with original interface
        private async void NiftyList_OnTapped(object sender, EventArgs e)
        {
            // This method is kept for compatibility but the actual handling
            // is done through the individual item tap handlers
        }

        private async void HandleContactAction(CMyListItem item)
        {
            switch (item.Tag)
            {
                case "tel":
                    CWorld.Dial(item.Parameters);
                    break;

                case "www":
                    await Core.PushInstance(Navigation, typeof(WebpageCustomized), item.Parameters, Core.Current.MyCompany.Name, Core.Current.MyCompany.ContactUsTel);
                    break;

                case "url":
                     Core.Native.OpenUrl(item.Parameters);
                    break;

                case "facebook":
                    if (! Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebookNumeric))
                    {
                         Core.Native.OpenUrl(@"https://www.facebook.com/" + Core.Current.MyCompany.ContactUsFacebook);
                    }
                    break;

                case "vk": //todo
                    //if (! Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebook))
                    //{
                         Core.Native.OpenUrl(@"https://www.vk.com/" + Core.Current.MyCompany.ContactUsVk);
                    //}
                    break;

                case "instagram":
                    if (! Core.Native.OpenInAppInstagram(Core.Current.MyCompany.ContactUsInstagram))
                    {
                        //else open in our browser:
                        Core.Native.OpenUrl(@"https://www.instagram.com/" + Core.Current.MyCompany.ContactUsInstagram);
                       // await Core.PushInstance(Navigation, typeof(Webpage), @"https://www.instagram.com/" + Core.Current.MyCompany.ContactUsInstagram, Core.Current.MyCompany.Name + " " + ResStrings.Instagram, "");
                    }
                    break;

                case "mail":

                     Core.Native.OpenUrl($"mailto:{item.Parameters}");
//                    Core.Mail(Params);
                    break;

                case "map":
                    //await Core.PushInstance(Daddy.Navigation, typeof(PageWeOnMap));
                    //                    await Core.PushInstance(Navigation, typeof(PageAboutUsMap));
                    break;


                case "navi":
                    //await NiftyNative.Navigate(Core.Current.MyCompany.Name, Core.Current.MyCompany.MapX, Core.Current.MyCompany.MapY);
                    break;

            }
        }

         private async void ShowMapTapped(object sender, EventArgs e)
        {

           // await Core.PushInstance(Daddy.Navigation, typeof(PageWeOnMap));
            //    await Core.PushInstance(Navigation, typeof(PageAboutUsMap));
        }
    }
}
