﻿namespace AppoMobi.Xam
{
    //public class KeyboardView: Grid
    //{

    //    public void KeyboardResized(int keyboardSize)
    //    {
    //        OnKeyboardResized(keyboardSize);
    //        //OnKeyboardToggled(state);
    //    }




    //    public virtual int OnKeyboardResized(int size)
    //    {

    //        Margin = new Thickness(0, 0, 0, size);

    //        return size;
    //    }

 

    //}
}
