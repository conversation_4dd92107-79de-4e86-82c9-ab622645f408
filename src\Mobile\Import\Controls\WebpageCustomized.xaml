﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PageEnhancedNav
    x:Class="AppoMobi.WebpageCustomized"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    BackgroundColor="{x:Static appoMobi:AppColors.PrimaryLightest}"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}">

    <pages:PageEnhancedNav.InsertContent>
        <pages:IncludedContent Margin="0" HorizontalOptions="StartAndExpand">


            <Grid VerticalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>



                <WebView
                    x:Name="MyBrowser"
                    Grid.Row="0"
                    HorizontalOptions="FillAndExpand"
                    Navigated="MyBrowser_OnNavigated"
                    Navigating="MyBrowser_OnNavigating"
                    VerticalOptions="FillAndExpand" />



            </Grid>



        </pages:IncludedContent>
    </pages:PageEnhancedNav.InsertContent>

</pages:PageEnhancedNav>











