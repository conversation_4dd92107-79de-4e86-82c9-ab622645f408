﻿using System;
using System.Globalization;


namespace AppoMobi.Forms.Framework.Xaml
{
    public class CompareIntegersInvertConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var ret = false;

            try
            {
                if (value is Enum)
                {
                    int intVar = (int)value.GetType().GetField("value__").GetValue(value);

                    if (intVar == parameter.ToString().ToInteger())
                        return false;
                }

            }
            catch (Exception e)
            {

            }

            try
            {
                var iVisualStep = int.Parse((string)parameter);
                var iStep = (int)value;
                if (iStep != iVisualStep)
                    ret = true;
                return ret;
            }
            catch (Exception e)
            {
            }

            try
            {
                ret = true;
                var ints = ((string)parameter).Split(',');
                foreach (var number in ints)
                {
                    var iStep = int.Parse(number);
                    if (iStep == (int)value)
                    {
                        ret = false;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
            }

            return ret;
        }


    }
}