﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.Content.Res;
using Android.Views;
using Android.Views.InputMethods;
using AppoMobi.Droid.Native;
using AppoMobi.Xam;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using System.Threading;
using Android.Runtime;
using Android.Hardware.Display;
using Android.Media;
using Android.Media.Projection;
using TwinTechs.Droid.Extensions;
using Xamarin.Forms;
using Activity = Android.App.Activity;
using Android.Graphics;
using Android.OS;
using Android.Util;

namespace AppoMobi.Helpers
{
    public class FullScreenCaptureHelper
    {
        private MediaProjection _mediaProjection;
        private VirtualDisplay _virtualDisplay;
        private ImageReader _imageReader;
        private int _width, _height, _density;
        private TaskCompletionSource<byte[]> _completionSource;

        public FullScreenCaptureHelper(Activity activity, Intent data)
        {
            var mediaProjectionManager = (MediaProjectionManager)activity.GetSystemService(Context.MediaProjectionService);
            _mediaProjection = mediaProjectionManager.GetMediaProjection((int)Result.Ok, data);

            var displayMetrics = new DisplayMetrics();
            activity.WindowManager.DefaultDisplay.GetMetrics(displayMetrics);
            _density = (int)displayMetrics.DensityDpi;
            _width = displayMetrics.WidthPixels;
            _height = displayMetrics.HeightPixels;

            _imageReader = ImageReader.NewInstance(_width, _height, ImageFormatType.FlexRgba8888, 2);
        }

        public Task<byte[]> CaptureScreenshotAsync()
        {
            _completionSource = new TaskCompletionSource<byte[]>();

            _virtualDisplay = _mediaProjection.CreateVirtualDisplay(
                "ScreenCapture",
                _width,
                _height,
                _density,
                DisplayFlags.None,
                _imageReader.Surface,
                null,
                null);

            _imageReader.SetOnImageAvailableListener(new ImageAvailableListener(_completionSource, _imageReader), null);

            return _completionSource.Task;
        }

        private class ImageAvailableListener : Java.Lang.Object, ImageReader.IOnImageAvailableListener
        {
            private TaskCompletionSource<byte[]> _completionSource;
            private ImageReader _imageReader;

            public ImageAvailableListener(TaskCompletionSource<byte[]> completionSource, ImageReader imageReader)
            {
                _completionSource = completionSource;
                _imageReader = imageReader;
            }

            public void OnImageAvailable(ImageReader reader)
            {
                using var image = reader.AcquireLatestImage();
                if (image != null)
                {
                    var buffer = image.GetPlanes()[0].Buffer;
                    var bytes = new byte[buffer.Remaining()];
                    buffer.Get(bytes);
                    _completionSource.TrySetResult(bytes);
                }
                else
                {
                    _completionSource.TrySetResult(null);
                }
            }
        }
    }


    [Preserve(AllMembers = true)]
    public partial class NativeTasks : INativeTasks
    {


        public Task<byte[]> CaptureScreenshotAsync()
        {
            //return CaptureScreenshotAsync(Platform.CurrentActivity.Window.DecorView, Platform.CurrentActivity);
            var rootView = Platform.CurrentActivity.Window.DecorView.RootView;
            return CaptureScreenshotAsync(rootView, Platform.CurrentActivity);
        }

        public async Task<byte[]> CaptureScreenshotAsync(Android.Views.View view, Activity activity)
        {
            if (view.Height < 1 || view.Width < 1)
                return null;

            byte[] buffer = null;

            if ((int)Build.VERSION.SdkInt < 24) 
            {
                view.DrawingCacheEnabled = true;

                view.BuildDrawingCache(true);

                using (var screenshot = Bitmap.CreateBitmap(
                    view.Width,
                    view.Height,
                    Bitmap.Config.Argb8888))
                {
                    var canvas = new Canvas(screenshot);

                    view.Draw(canvas);

                    using (var stream = new MemoryStream())
                    {
                        screenshot.Compress(Bitmap.CompressFormat.Png, 100, stream);
                        buffer = stream.ToArray();
                    }
                }

                view.DrawingCacheEnabled = false;

                return buffer;
            }

            bool wait = true;

            using var helper = new ScreenshotHelper(view, activity);

            helper.Capture((Bitmap bitmap) =>
            {
                try
                {

                    if (!helper.Error)
                    {
                        using (var stream = new MemoryStream())
                        {
                            bitmap.Compress(Bitmap.CompressFormat.Png, 100, stream);
                            buffer = stream.ToArray();
                        }
                    }

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    wait = false;
                }
            });

            while (wait)
            {
               await Task.Delay(1);
            }

            return buffer;
        }


        public class ScreenshotHelper : Java.Lang.Object, PixelCopy.IOnPixelCopyFinishedListener
        {


            public void OnPixelCopyFinished(int copyResult)
            {
                var stop = true;
                if (copyResult == (int)PixelCopyResult.Success)
                {
                    Error = false;
                    //todo CallbackGotScreenshot();
                    _callback(_bitmap);
                }
                else
                {
                    Error = true;
                }

                _callback(_bitmap);

            }

            public bool Error { get; protected set; }

            public ScreenshotHelper(Android.Views.View view, Activity activity)
            {
                _view = view;
                _activity = activity;

                _bitmap = Bitmap.CreateBitmap(
                    _view.Width,
                    _view.Height,
                    Bitmap.Config.Argb8888);
            }

            // Starts a background thread and its {@link Handler}.
            private void StartBackgroundThread()
            {
                _BackgroundThread = new HandlerThread("ScreeshotMakerBackground");
                _BackgroundThread.Start();
                _BackgroundHandler = new Handler(_BackgroundThread.Looper);
            }

            // Stops the background thread and its {@link Handler}.
            private void StopBackgroundThread()
            {
                try
                {
                    _BackgroundThread.QuitSafely();
                    _BackgroundThread.Join();
                    _BackgroundThread = null;
                    _BackgroundHandler = null;
                }
                catch (Exception)
                {
                    //e.PrintStackTrace();
                }
            }

            public void Capture(Action<Bitmap> callback)
            {
                //var locationOfViewInWindow = new int[2];
                //_view.GetLocationInWindow(locationOfViewInWindow);
                _callback = callback;

                try
                {
                    StartBackgroundThread();
                    //todo could create-use background handler
                    PixelCopy.Request(_activity.Window, _bitmap, this,
                        _BackgroundHandler);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    Task.Run(StopBackgroundThread);
                }
            }

            private Android.Views.View _view;
            private Activity _activity;
            private Bitmap _bitmap;
            private HandlerThread _BackgroundThread;
            private Handler _BackgroundHandler;
            private Action<Bitmap> _callback;


            public new void Dispose()
            {
                _bitmap?.Dispose();
                _bitmap = null;
                _activity = null;
                _view = null;
                _callback = null;

                base.Dispose();
            }

        }

        // Method to check if the device has GPS hardware available
        public bool CheckGpsSupported()
        {
            bool value = false;
            Android.Content.PM.PackageManager pm = Android.App.Application.Context.PackageManager;
            if (pm.HasSystemFeature(Android.Content.PM.PackageManager.FeatureLocationGps))
            {
                // GPS hardware is available
                value = true;
            }
            else
            {
                // GPS hardware is not available
                value = false;
            }
            return value;
        }

        // Method to check if GPS is currently enabled
        public bool CheckGpsEnabled()
        {
            bool value = false;
            Android.Locations.LocationManager manager = (Android.Locations.LocationManager)Android.App.Application.Context.GetSystemService(Android.Content.Context.LocationService);
            if (!manager.IsProviderEnabled(Android.Locations.LocationManager.GpsProvider))
            {
                // GPS is not enabled
                value = false;
            }
            else
            {
                // GPS is enabled
                value = true;
            }
            return value;
        }

        public void ExecuteTask(string task, params object[] parameters)
        {

            switch (task)
            {

                case "cannotSleep":
                    DroidCore.MainWindow.AddFlags(WindowManagerFlags.KeepScreenOn);
                    break;

                case "canSleep":
                    DroidCore.MainWindow.ClearFlags(WindowManagerFlags.KeepScreenOn);
                    break;

                case "playClickSound":
                    DroidCore.Current.MainView.PlaySoundEffect(SoundEffects.Click);
                    break;


                case "playSound":
                    DroidCore.Current.PlayBeep();
                    /*
                    Android.Net.Uri notification = RingtoneManager.GetDefaultUri(RingtoneType.Notification);
                    Ringtone r = RingtoneManager.GetRingtone(Android.App.Application.Context, notification);
                    r.Play();
                    */
                    break;

                case "showStatusBar":
                    DroidCore.MainWindow.ClearFlags(WindowManagerFlags.Fullscreen);
                    var newUiOptions = 0;// (int)uiOptions;
                                         // newUiOptions |= (int)SystemUiFlags.LowProfile;
                                         // newUiOptions |= (int)SystemUiFlags.HideNavigation;
                                         //newUiOptions |= (int)SystemUiFlags.Immersive;
                                         // newUiOptions |= (int)SystemUiFlags.LayoutStable;
                    DroidCore.Current.MainView.SystemUiVisibility = (StatusBarVisibility)newUiOptions;
                    break;

                case "showStatusBarImmersive":
                    DroidCore.MainWindow.ClearFlags(WindowManagerFlags.Fullscreen);
                    newUiOptions = 0;// (int)uiOptions;
                    newUiOptions |= (int)SystemUiFlags.LowProfile;
                    newUiOptions |= (int)SystemUiFlags.HideNavigation;
                    newUiOptions |= (int)SystemUiFlags.Immersive;
                    newUiOptions |= (int)SystemUiFlags.LayoutStable;
                    DroidCore.Current.MainView.SystemUiVisibility = (StatusBarVisibility)newUiOptions;
                    break;


                case "registerFont":
                    var preset = AppoMobi.Xam.Fonts.GetPresetById((string)parameters[0]);
                    DroidCore.RegisterFont(preset, (string)parameters[1]);
                    break;

                case "setInterfaceFont":
                    var fontUi = AppoMobi.Xam.Fonts.GetPresetById((string)parameters[0]);
                    if (DroidCore.InterfaceFont != null)
                    {
                        if (DroidCore.InterfaceFont.Info.Id.ToLowerInvariant() == ((string)parameters[0]).ToLowerInvariant())
                            break;
                        DroidCore.InterfaceFont.Dispose();
                    }
                    DroidCore.InterfaceFont = new FontAndroid(fontUi, "ui");
                    break;

                case "setMainFont":
                    var font = AppoMobi.Xam.Fonts.GetPresetById((string)parameters[0]);
                    if (DroidCore.MainFont != null)
                    {
                        if (DroidCore.MainFont.Info.Id.ToLowerInvariant() == ((string)parameters[0]).ToLowerInvariant())
                            break;
                        DroidCore.MainFont.Dispose();
                    }
                    DroidCore.MainFont = new FontAndroid(font, "main");
                    break;

                case "showSelectInput":
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        Thread.Sleep(100); // For some reason, a short delay is required here.
                        var imm = (InputMethodManager)Context.GetSystemService(Context.InputMethodService);
                        imm.ShowInputMethodPicker();
                    });
                    break;
            }
        }

        //---------------------------------------------------------
        public void OpenGPSSettings()
        //---------------------------------------------------------
        {

            var intent = new Intent(Android.Provider.Settings.ActionLocationSourceSettings);
            Context.StartActivity(intent);
        }

        
        public List<string> ListAvailableFonts()
        
        {
            var ret = new List<string>();

            //foreach (var familyName in UIFont.FamilyNames)
            //{
            //    foreach (var font in UIFont.FontNamesForFamilyName(familyName))
            //    {
            //        ret.Add(font);
            //    }
            //}
            return ret;
        }


        
        public Context Context
        
        {
            get
            {
                return Platform.CurrentActivity ?? Android.App.Application.Context;
            }
        }

        public void InitPush()
        {

        }

        
        public bool OpenAppInStore()
        
        {
            Context context = Context;
            PackageManager manager = context.PackageManager;
            PackageInfo info = manager.GetPackageInfo(context.PackageName, 0);

            var appPackageName = context.PackageName;
            var intent = new Intent(Intent.ActionView);
            var url = @"market://details?id=" + appPackageName;

            try
            {
                intent.SetData(Android.Net.Uri.Parse(url));
                Context.StartActivity(intent);
            }
            catch (Exception ex)
            {
                url = "http://play.google.com/store/apps/details?id=" + appPackageName;
                intent.SetData(Android.Net.Uri.Parse(url));
                Context.StartActivity(intent);
            }

            return true;
        }

        public object CurrentViewDesc()
        {
            return null;
        }

        
        public bool OpenInAppInstagram(string user)
        
        {
            try
            {
                var url = @"http://instagram.com/_u/" + user;
                var intent = Context.PackageManager.GetLaunchIntentForPackage("com.instagram.android");
                intent.SetData(Android.Net.Uri.Parse(url));
                Context.StartActivity(intent);
                return true;
            }
            catch (Exception ex)
            {

                return false;
            }
        }

        //---------------------------------------------------------
        public Microsoft.Maui.Graphics.Point GetViewPositionOnScreen(Microsoft.Maui.Controls.View control)
        //---------------------------------------------------------
        {
            var loc = new int[2];
            var native = control.GetNativeView();
            if (native != null)
            {
                native.GetLocationOnScreen(loc);
                var point = new Microsoft.Maui.Graphics.Point((double)loc[0], (double)loc[1]);
                return point;
            }
            return new Microsoft.Maui.Graphics.Point(-1, -1);
        }

        
        public bool NavigateYandexMaps(string name, double latitude, double longitude)
        
        {
            try
            {

                var url = string.Format(@"yandexmaps://maps.yandex.ru/?pt={1},{0}", latitude.ToString().Replace(",", "."), longitude.ToString().Replace(",", "."));

                return OpenUrl(url);

                //var lang = Settings.Current.SelectedLang.ToLower();
                //var intent = Context.PackageManager.GetLaunchIntentForPackage(lang + ".yandex.yandexmaps");
                //intent.SetData(Uri.Parse(url));
                //Context.StartActivity(intent);
                //return true;
            }
            catch
            {
                return false;
            }
        }

        
        public bool NavigateYandexNavigator(string name, double latitude, double longitude)
        
        {


            try
            {

                var url = string.Format(@"yandexnavi://build_route_on_map?lat_to={0}&lon_to={1}", latitude.ToString().Replace(",", "."), longitude.ToString().Replace(",", "."));
                return OpenUrl(url);

                var lang = Settings.Current.SelectedLang.ToLower();
                var intent = Context.PackageManager.GetLaunchIntentForPackage(lang + ".yandex.yandexnavi");
                intent.SetData(Android.Net.Uri.Parse(url));
                Context.StartActivity(intent);
                return true;
            }
            catch
            {
                return false;
            }


        }






        
        public bool OpenInAppFacebook(string user)
        
        {
            try
            {
                //if exception fb app is not installed
                var versionCode = Context.PackageManager.GetPackageInfo("com.facebook.katana", 0);
                var url = "";

                url = @"fb://facewebmodal/f?href=" + @"https://www.facebook.com/n/?" + user;


                //if (versionCode.VersionCode >= 3002850) ???????????????
                //{
                //    //newer versions of fb app
                //    url = @"fb://facewebmodal/f?href=" + @"https://www.facebook.com/n/?" + user;
                //}
                //else
                //{
                //    //older versions of fb app
                //    url= @"fb://page/" + user;
                //}
                var intent = new Intent(Intent.ActionView);
                intent.SetData(Android.Net.Uri.Parse(url));
                Context.StartActivity(intent);
                return true;
            }
            catch
            {
                return false;
            }

        }



        
        public string GetAppVersion()
        
        {

            Context context = Context;
            PackageManager manager = context.PackageManager;
            PackageInfo info = manager.GetPackageInfo(context.PackageName, 0);
            return info.VersionName;
        }

        
        public string GetAppBuild()
        
        {
            Context context = Context;
            PackageManager manager = context.PackageManager;
            PackageInfo info = manager.GetPackageInfo(context.PackageName, 0);
            return info.VersionCode.ToString();
        }

        
        public bool AskForRating()
        
        {
            var ok = OpenAppInMarket(Context);
            if (ok) return true;

            var intent = new Intent(Intent.ActionView,
                Android.Net.Uri.Parse("http://play.google.com/store/apps/details?id=" + Context.PackageName));
            Context.StartActivity(intent);
            return false;
        }

        
        public bool OpenAppInMarket(Context context)
        
        {
            // you can also use BuildConfig.APPLICATION_ID
            String appId = context.PackageName;
            Intent rateIntent = new Intent(Intent.ActionView,
                Android.Net.Uri.Parse("market://details?id=" + appId));
            bool marketFound = false;

            // find all applications able to handle our rateIntent
            var otherApps = context.PackageManager.QueryIntentActivities(rateIntent, 0);
            foreach (var otherApp in otherApps)
            {
                // look for Google Play application
                if (otherApp.ActivityInfo.ApplicationInfo.PackageName.Equals("com.android.vending"))
                {

                    ActivityInfo otherAppActivity = otherApp.ActivityInfo;
                    ComponentName componentName = new ComponentName(
                        otherAppActivity.ApplicationInfo.PackageName,
                        otherAppActivity.Name
                    );
                    // make sure it does NOT open in the stack of your activity
                    rateIntent.AddFlags(ActivityFlags.NewTask);
                    // task reparenting if needed
                    rateIntent.AddFlags(ActivityFlags.ResetTaskIfNeeded);
                    // if the Google Play was already open in a search result
                    //  this make sure it still go to the app page you requested
                    rateIntent.AddFlags(ActivityFlags.ClearTop);
                    // this make sure only the Google Play app is allowed to
                    // intercept the intent
                    rateIntent.SetComponent(componentName);
                    context.StartActivity(rateIntent);
                    marketFound = true;
                }
            }

            return marketFound;
        }

        //---------------------------------------------------------
        public void CloseApp()
        //---------------------------------------------------------
        {
            var activity = (Activity)Context;
            activity.FinishAffinity();

            Android.OS.Process.KillProcess(Android.OS.Process.MyPid());
        }

        /*
        //---------------------------------------------------------
        public void SetTabbedBarBackgroundColor(string HexColor)
        //---------------------------------------------------------
        {

            var c = new Color();
            c = Color.ParseColor(HexColor);

            var mainactivity = Android.App.Application.Context;
            var activity = Xamarin.Forms.Context as Activity;

            var component = new ComponentName(mainactivity, Class.FromType(typeof(MainActivity)));
            
            if (activity == null) return;

            var e = activity.FindViewById(Resource.Layout.Tabbar);
            e.SetBackgroundColor(c);


        }
        */
        public double GetButtonWidth(string text)
        {
            Android.Graphics.Rect bounds = new Android.Graphics.Rect();
            //TextView textView = new TextView(Context);
            var btnButton = new Android.Widget.Button(Context);
            btnButton.Paint.GetTextBounds(text, 0, text.Length, bounds);
            var length = bounds.Width();
            return length / Resources.System.DisplayMetrics.ScaledDensity;
        }

        //---------------------------------------------------------
        public void PushEnableSound(bool value)
        //---------------------------------------------------------
        {
            //todo move this shit
            //OneSignal.EnableSound(value);

        }


        ////---------------------------------------------------------
        //public void UpdateTabbedMenu()
        ////---------------------------------------------------------
        //{


        //}

        //---------------------------------------------------------
        public void HideNavigationOverlay()
        //---------------------------------------------------------
        {

            if (!(Context is Activity activity)) return;
            activity.Window.DecorView.SystemUiVisibility = (StatusBarVisibility)SystemUiFlags.HideNavigation;

        }

        //---------------------------------------------------------
        public void OpenSettings()
        //---------------------------------------------------------
        {
            var intent = new Intent(Android.Provider.Settings.ActionApplicationDetailsSettings,
                Android.Net.Uri.Parse("package:" + Context.PackageName));
            Context.StartActivity(intent);
        }

        //---------------------------------------------------------
        public void OpenPermissions()
        //---------------------------------------------------------
        {
            OpenSettings();
        }

        //---------------------------------------------------------
        public void OpenGPSPermissions()
        //---------------------------------------------------------
        {

            var intent = new Intent(Android.Provider.Settings.ActionApplicationDetailsSettings,
                Android.Net.Uri.Parse("package:" + Context.PackageName));
            Context.StartActivity(intent);
        }



        //---------------------------------------------------------
        public bool OpenUrl(string url)
        //---------------------------------------------------------
        {
            try
            {

                var uri = Android.Net.Uri.Parse(url);
                var intent = new Intent(Intent.ActionView, uri);
                Context.StartActivity(intent);
                return true;
            }
            catch
            {
                return false;
            }


        }

        
        public void Test()
        
        {
            //not working booh
            //UIApplication.SharedApplication.SetStatusBarStyle(UIStatusBarStyle.Default, false);

            //UINavigationBar.Appearance.SetTitleTextAttributes(new UITextAttributes
            //{
            //    TextColor = Color.FromHex(AppColors.bw_highlight).ToUIColor()
            //});
        }
    }
    //***************************************************************************
    public static class ViewExtensions
    //***************************************************************************
    {
        private static readonly Type _platformType = Type.GetType("Xamarin.Forms.Platform.Android.Platform, Xamarin.Forms.Platform.Android", true);
        private static BindableProperty _rendererProperty;

        public static BindableProperty RendererProperty
        {
            get
            {
                _rendererProperty = (BindableProperty)_platformType.GetField("RendererProperty", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static)
                    .GetValue(null);

                return _rendererProperty;
            }
        }

        //public static IVisualElementRenderer GetRenderer(this BindableObject bindableObject)
        //{
        //    var value = bindableObject.GetValue(RendererProperty);
        //    return (IVisualElementRenderer)bindableObject.GetValue(RendererProperty);
        //}

        //public static Android.Views.View GetNativeView(this BindableObject bindableObject)
        //{
        //    var renderer = bindableObject.GetRenderer();
        //    if (renderer != null)
        //    {
        //        return renderer.View;
        //    }
        //    return null;
        //}

        //public static void SetRenderer(this BindableObject bindableObject, IVisualElementRenderer renderer)
        //{
        //    //			var value = bindableObject.GetValue (RendererProperty);
        //    bindableObject.SetValue(RendererProperty, renderer);
        //}

    
    }

}