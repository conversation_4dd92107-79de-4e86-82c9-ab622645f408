﻿using System;
using System.Globalization;

namespace AppoMobi.Xam.Converters
{
    public class IntOverZeroConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            const bool success = true;
 
            if (value is int)
            {               
                if ((int)value > 0) return success;
            }
            return !success;
        }

 
    }

    public class DoubleOverZeroConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            const bool success = true;

            if (value is double)
            {
                if ((double)value > 0) return success;
            }
            return !success;
        }


    }

}
