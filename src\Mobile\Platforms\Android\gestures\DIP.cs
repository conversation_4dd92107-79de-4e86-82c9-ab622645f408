using Android.App;
using Android.Content;
using Android.Content.Res;
using Android.Util;
using System;
using Android.Runtime;
using Xamarin.Forms;
using Application = Android.App.Application;

namespace AppoMobi.Touch.Droid
{
	/// <summary>
	/// Calculator to help calculate Device Independant Pixels to real pixels.
	/// </summary>
	public static class DIP
	{
		public readonly static float Density;

		static DIP()
		{
			DIP.Density = Application.Context.Resources.DisplayMetrics.Density;
		}

        public static Point ToPoint(double dipX, double dipY)
		{
			return new Point(dipX / (double)DIP.Density, dipY / (double)DIP.Density);
		}

		public static Rect ToRectangle(double dipX, double dipY, double dipWidth, double dipHeight)
		{
			return new Rect(dipX / (double)DIP.Density, dipY / (double)DIP.Density, dipWidth / (double)DIP.Density, dipHeight / (double)DIP.Density);
		}
	}
}