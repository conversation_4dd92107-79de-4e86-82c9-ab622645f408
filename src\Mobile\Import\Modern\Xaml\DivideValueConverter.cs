﻿using System;
using System.Globalization;


namespace AppoMobi.Forms.Framework.Xaml
{
    public class DivideValueConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {


            try
            {
                if ((double)value < 0.5)
                    return value;

                var divider = ((string)parameter).ToDouble();
                var ret = (double)value / divider;
                return ret;
            }
            catch (Exception e)
            {
            }

            return value;
        }


    }
}