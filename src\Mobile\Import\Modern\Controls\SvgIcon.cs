﻿using AppoMobi.Forms.Controls.Skia;
using SkiaSharp;
using SkiaSharp.Views.Maui.Controls;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;
using Svg.Skia;


namespace AppoMobi.Forms.Controls.Svg
{

	public class SvgIcon : ContentView, IDisposable
	{
		private readonly SKCanvasView _canvasView = new SKCanvasView();

		public void Dispose()
		{
			_canvasView.PaintSurface -= CanvasViewOnPaintSurface;
		}


		//-------------------------------------------------------------
		// UseAssembly
		//-------------------------------------------------------------
		private const string nameUseAssembly = "UseAssembly";
		public static readonly BindableProperty UseAssemblyProperty = BindableProperty.Create(nameUseAssembly, typeof(object), typeof(SvgIcon),
			null);
		public object UseAssembly
		{
			get { return (object)GetValue(UseAssemblyProperty); }
			set { SetValue(UseAssemblyProperty, value); }
		}

		public SvgIcon()
		{
			//CornerRadius = 0;
			//HasShadow = false;
			//            BackgroundColor = Colors.Transparent;

			HorizontalOptions = LayoutOptions.Start;
			VerticalOptions = LayoutOptions.Start;
			Padding = new Thickness(0);


			Content = _canvasView;
			_canvasView.PaintSurface += CanvasViewOnPaintSurface;
		}


		//-------------------------------------------------------------
		// Aspect
		//-------------------------------------------------------------
		private const string nameAspect = "Aspect";
		public static readonly BindableProperty AspectProperty = BindableProperty.Create(nameAspect, typeof(BitmapStretch), typeof(SvgIcon), BitmapStretch.AspectFit,
			propertyChanged: RedrawCanvas);
		public BitmapStretch Aspect
		{
			get { return (BitmapStretch)GetValue(AspectProperty); }
			set { SetValue(AspectProperty, value); }
		}

		//-------------------------------------------------------------
		// UseGradient
		//-------------------------------------------------------------
		private const string nameUseGradient = "UseGradient";
		public static readonly BindableProperty UseGradientProperty = BindableProperty.Create(nameUseGradient, typeof(bool), typeof(SvgIcon), false,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public bool UseGradient
		{
			get { return (bool)GetValue(UseGradientProperty); }
			set { SetValue(UseGradientProperty, value); }
		}

		//-------------------------------------------------------------
		// IsGhost
		//-------------------------------------------------------------
		private const string nameIsGhost = "IsGhost";
		public static readonly BindableProperty IsGhostProperty = BindableProperty.Create(nameIsGhost, typeof(bool), typeof(SvgIcon), false,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public bool IsGhost
		{
			get { return (bool)GetValue(IsGhostProperty); }
			set { SetValue(IsGhostProperty, value); }
		}

		//-------------------------------------------------------------
		// FontAwesomePrimaryColor
		//-------------------------------------------------------------
		private const string nameFontAwesomePrimaryColor = "FontAwesomePrimaryColor";
		public static readonly BindableProperty FontAwesomePrimaryColorProperty = BindableProperty.Create(nameFontAwesomePrimaryColor, typeof(Color), typeof(SvgIcon), Colors.Transparent);
		public Color FontAwesomePrimaryColor
		{
			get { return (Color)GetValue(FontAwesomePrimaryColorProperty); }
			set { SetValue(FontAwesomePrimaryColorProperty, value); }
		}

		//-------------------------------------------------------------
		// FontAwesomeSecondaryColor
		//-------------------------------------------------------------
		private const string nameFontAwesomeSecondaryColor = "FontAwesomeSecondaryColor";
		public static readonly BindableProperty FontAwesomeSecondaryColorProperty = BindableProperty.Create(nameFontAwesomeSecondaryColor, typeof(Color), typeof(SvgIcon), Colors.Transparent);
		public Color FontAwesomeSecondaryColor
		{
			get { return (Color)GetValue(FontAwesomeSecondaryColorProperty); }
			set { SetValue(FontAwesomeSecondaryColorProperty, value); }
		}

		//-------------------------------------------------------------
		// StartColor
		//-------------------------------------------------------------
		private const string nameStartColor = "StartColor";
		public static readonly BindableProperty StartColorProperty = BindableProperty.Create(nameStartColor, typeof(Color), typeof(SvgIcon),
			Colors.DarkGray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color StartColor
		{
			get { return (Color)GetValue(StartColorProperty); }
			set { SetValue(StartColorProperty, value); }
		}

		//-------------------------------------------------------------
		// EndColor
		//-------------------------------------------------------------
		private const string nameEndColor = "EndColor";
		public static readonly BindableProperty EndColorProperty = BindableProperty.Create(nameEndColor, typeof(Color), typeof(SvgIcon),
			Colors.Gray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color EndColor
		{
			get { return (Color)GetValue(EndColorProperty); }
			set { SetValue(EndColorProperty, value); }
		}

		//-------------------------------------------------------------
		// TintColor
		//-------------------------------------------------------------
		private const string nameTintColor = "TintColor";
		public static readonly BindableProperty TintColorProperty = BindableProperty.Create(nameTintColor, typeof(Color), typeof(SvgIcon),
				Colors.Transparent,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color TintColor
		{
			get { return (Color)GetValue(TintColorProperty); }
			set { SetValue(TintColorProperty, value); }
		}

		//-------------------------------------------------------------
		// GradientBlendMode
		//-------------------------------------------------------------
		private const string nameGradientBlendMode = "GradientBlendMode";
		public static readonly BindableProperty GradientBlendModeProperty = BindableProperty.Create(nameGradientBlendMode, typeof(SKBlendMode), typeof(SvgIcon),
			SKBlendMode.SrcIn,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay

		public SKBlendMode GradientBlendMode
		{
			get { return (SKBlendMode)GetValue(GradientBlendModeProperty); }
			set { SetValue(GradientBlendModeProperty, value); }
		}


		//-------------------------------------------------------------
		// StartXRatio
		//-------------------------------------------------------------
		private const string nameStartXRatio = "StartXRatio";
		public static readonly BindableProperty StartXRatioProperty = BindableProperty.Create(nameStartXRatio, typeof(float), typeof(SvgIcon), 0.1f,
			propertyChanged: RedrawCanvas);
		public float StartXRatio
		{
			get { return (float)GetValue(StartXRatioProperty); }
			set { SetValue(StartXRatioProperty, value); }
		}

		//-------------------------------------------------------------
		// StartYRatio
		//-------------------------------------------------------------
		private const string nameStartYRatio = "StartYRatio";
		public static readonly BindableProperty StartYRatioProperty = BindableProperty.Create(nameStartYRatio, typeof(float), typeof(SvgIcon), 1.0f,
			propertyChanged: RedrawCanvas);
		public float StartYRatio
		{
			get { return (float)GetValue(StartYRatioProperty); }
			set { SetValue(StartYRatioProperty, value); }
		}

		//-------------------------------------------------------------
		// EndXRatio
		//-------------------------------------------------------------
		private const string nameEndXRatio = "EndXRatio";
		public static readonly BindableProperty EndXRatioProperty = BindableProperty.Create(nameEndXRatio, typeof(float), typeof(SvgIcon), 0.9f,
			propertyChanged: RedrawCanvas);
		public float EndXRatio
		{
			get { return (float)GetValue(EndXRatioProperty); }
			set { SetValue(EndXRatioProperty, value); }
		}

		//-------------------------------------------------------------
		// EndYRatio
		//-------------------------------------------------------------
		private const string nameEndYRatio = "EndYRatio";
		public static readonly BindableProperty EndYRatioProperty = BindableProperty.Create(nameEndYRatio, typeof(float), typeof(SvgIcon), 0.1f,
			propertyChanged: RedrawCanvas);
		public float EndYRatio
		{
			get { return (float)GetValue(EndYRatioProperty); }
			set { SetValue(EndYRatioProperty, value); }
		}



		//-------------------------------------------------------------
		// Text
		//-------------------------------------------------------------
		private const string nameText = "Text";
		public static readonly BindableProperty TextProperty = BindableProperty.Create(nameText, typeof(string), typeof(SvgIcon), string.Empty,
			propertyChanged: RedrawCanvas);
		public string Text
		{
			get { return (string)GetValue(TextProperty); }
			set { SetValue(TextProperty, value); }
		}


		//-------------------------------------------------------------
		// FontSize
		//-------------------------------------------------------------
		private const string nameFontSize = "FontSize";
		public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize, typeof(double), typeof(SvgIcon), 12.0,
			propertyChanged: RedrawCanvas);
		public double FontSize
		{
			get { return (double)GetValue(FontSizeProperty); }
			set { SetValue(FontSizeProperty, value); }
		}


		//-------------------------------------------------------------
		// FontFamily
		//-------------------------------------------------------------
		private const string nameFontFamily = "FontFamily";
		public static readonly BindableProperty FontFamilyProperty = BindableProperty.Create(nameFontFamily, typeof(string), typeof(SvgIcon), null,
			propertyChanged: RedrawCanvas);
		public string FontFamily
		{
			get { return (string)GetValue(FontFamilyProperty); }
			set
			{
				SetValue(FontFamilyProperty, value);
			}
		}

		public static SKTypeface GetTypeface(string fullFontName)
		{
			SKTypeface result;

			var assembly = Assembly.GetExecutingAssembly();
			var stream = assembly.GetManifestResourceStream("ClassLibrary1.Font." + fullFontName);
			if (stream == null)
				return null;

			result = SKTypeface.FromStream(stream);
			return result;
		}




		//-------------------------------------------------------------
		// SvgHorizontalOptions
		//-------------------------------------------------------------
		private const string nameSvgHorizontalOptions = "SvgHorizontalOptions";
		public static readonly BindableProperty SvgHorizontalOptionsProperty = BindableProperty.Create(nameSvgHorizontalOptions, typeof(LayoutAlignment), typeof(SvgIcon), LayoutAlignment.Center);
		public LayoutAlignment SvgHorizontalOptions
		{
			get { return (LayoutAlignment)GetValue(SvgHorizontalOptionsProperty); }
			set { SetValue(SvgHorizontalOptionsProperty, value); }
		}

		//-------------------------------------------------------------
		// SvgVerticalOptions
		//-------------------------------------------------------------
		private const string nameSvgVerticalOptions = "SvgVerticalOptions";
		public static readonly BindableProperty SvgVerticalOptionsProperty = BindableProperty.Create(nameSvgVerticalOptions, typeof(LayoutAlignment), typeof(SvgIcon), LayoutAlignment.Center);
		public LayoutAlignment SvgVerticalOptions
		{
			get { return (LayoutAlignment)GetValue(SvgVerticalOptionsProperty); }
			set { SetValue(SvgVerticalOptionsProperty, value); }
		}

		private void CanvasViewOnPaintSurface(object sender, SKPaintSurfaceEventArgs args)
		{


			SKCanvas canvas = args.Surface.Canvas;
			canvas.Clear();

			if (IsGhost)
				return;

			//TEXT
			if (string.IsNullOrEmpty(image) && !string.IsNullOrEmpty(Text))
			{
				SKImageInfo info = args.Info;
				SKSurface surface = args.Surface;


				SKPaint textPaint = new SKPaint
				{
					Color = TintColor.ToSKColor(),
					TextSize = (float)FontSize * Core.DisplayDensity,
					IsAntialias = true,
					//Typeface = SKTypeface.FromFile(FontFamily)
					Typeface = CustomFontManager.Instance.GetFont(FontFamily)
				};

				if (UseGradient)
				{
					textPaint.Shader = SKShader.CreateLinearGradient(
						new SKPoint(info.Width * StartXRatio, info.Height * StartYRatio),
						new SKPoint(info.Width * EndXRatio, info.Height * EndYRatio),
						new SKColor[]
                        //new SKPoint(info.Width * 0.1f, info.Height),
                        //new SKPoint(info.Width * 0.9f, info.Width * 0.1f),
                        //new SKColor[]
                        {
							StartColor.ToSKColor(),
							EndColor.ToSKColor()
						},
						null,
						SKShaderTileMode.Clamp);
					//  textPaint.BlendMode = GradientBlendMode;

				}


				// Adjust TextSize property so text is 90% of screen width
				//                float textWidth = textPaint.MeasureText(Text);
				//              textPaint.TextSize = 0.9f * info.Width * textPaint.TextSize / textWidth;

				// Find the text bounds
				SKRect textBounds = new SKRect();
				textPaint.MeasureText(Text, ref textBounds);

				// Calculate offsets to center the text on the screen
				float xText = info.Width / 2 - textBounds.MidX;
				float yText = info.Height / 2 - textBounds.MidY;

				// And draw the text
				canvas.DrawText(Text, xText, yText, textPaint);


				return;
			}

			//NOT TEXT
			if (!string.IsNullOrEmpty(image))
			{
				byte[] byteArray = Encoding.ASCII.GetBytes(image);
				using (Stream stream = new MemoryStream(byteArray))
				{
                    var svg = new SKSvg();
                    try
                    {
                        svg.Load(stream);
                        if (svg == null)
                        {
                            throw new Exception("[SkiaSvg] Failed to load string");
                        }

                        	//scale png
					/*
                     var scaled = bitmap.Resize(new SKImageInfo(newWidth, newHeight), SKBitmapResizeMethod.Lanczos3);
                     */

					//scale
					SKImageInfo info = args.Info;
					canvas.Translate(info.Width / 2f, info.Height / 2f);
					SKRect bounds = svg.Picture.CullRect;

					float xRatio = info.Width / bounds.Width;
					float yRatio = info.Height / bounds.Height;

					//ASPECT FIT
					float ratio = Math.Min(xRatio, yRatio);

					//ASPECT FILL
					if (Aspect == BitmapStretch.AspectFill)
					{
						//todo
						ratio = Math.Max(xRatio, yRatio);
					}
					else
					if (Aspect == BitmapStretch.AspectFitFill)
					{
						if (info.Width < info.Height)
						{
							ratio = bounds.Height / info.Height;
						}
						else
						{
							ratio = bounds.Width / info.Width;
						}
					}

					var ratioX = ratio;
					var ratioY = ratio;

					var translateX = -bounds.MidX;
					var translateY = -bounds.MidY;

					//canvas.Scale(ratioX, ratioY);

					if (SvgVerticalOptions == LayoutAlignment.Fill)
					{
						ratioY = yRatio;
					}
					else if (SvgVerticalOptions == LayoutAlignment.Start)
					{
						var takesHeight = svg.Picture.CullRect.Height * ratio;
						var freeSpace = (info.Height - takesHeight) / ratio;

						translateY = -(bounds.MidX + freeSpace / 2.0f);
					}
					else if (SvgVerticalOptions == LayoutAlignment.End)
					{
						var takesHeight = svg.Picture.CullRect.Height * ratio;
						var freeSpace = (info.Height - takesHeight) / ratio;

						translateY = -bounds.MidX + freeSpace / 2.0f;
					}

					if (SvgHorizontalOptions == LayoutAlignment.Fill)
					{
						ratioX = xRatio;
					}
					else if (SvgHorizontalOptions == LayoutAlignment.Start)
					{
						var takesWidth = svg.Picture.CullRect.Width * ratio;
						var freeSpace = (info.Width - takesWidth) / ratio;

						translateX = -(bounds.MidX + freeSpace / 2.0f);
					}
					else if (SvgHorizontalOptions == LayoutAlignment.End)
					{
						var takesWidth = svg.Picture.CullRect.Width * ratio;
						var freeSpace = (info.Width - takesWidth) / ratio;

						translateX = -bounds.MidX + freeSpace / 2.0f;
					}

					canvas.Scale(ratioX, ratioY);

					canvas.Translate(translateX, translateY);


					if (TintColor != null && TintColor != Colors.Transparent && !UseGradient)
					{

						using (var paint = new SKPaint())
						{
							paint.IsAntialias = true;
							paint.ColorFilter = SKColorFilter.CreateBlendMode(TintColor.ToSKColor(), SKBlendMode.SrcIn);
							canvas.DrawPicture(svg.Picture, paint);
						}

					}
					else if (UseGradient)
					{
						canvas.DrawPicture(svg.Picture);
						canvas.ResetMatrix();

						using (var paint = new SKPaint())
						{
							paint.IsAntialias = true;
							paint.Shader = SKShader.CreateLinearGradient(
								new SKPoint(info.Width * StartXRatio, info.Height * StartYRatio),
								new SKPoint(info.Width * EndXRatio, info.Height * EndYRatio),
								new SKColor[]
								{
									StartColor.ToSKColor(),
									EndColor.ToSKColor()
								},
								null,
								SKShaderTileMode.Clamp);

							paint.BlendMode = GradientBlendMode;


							canvas.DrawRect(info.Rect, paint);
						}
					}
					else
					{
						canvas.DrawPicture(svg.Picture);
					}
                    }
                    catch (Exception e)
                    {
                        Debug.WriteLine(e);
                        return;
                    }

				

				}
			}
		}



		private string _image;
		protected string image
		{
			get { return _image; }
			set
			{
				if (_image != value)
				{
					_image = value;

					HasContent = !string.IsNullOrEmpty(value);
				}
			}
		}


		protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
		{
			base.OnPropertyChanged(propertyName);

			if (propertyName == nameUseAssembly)
			{
				if (!string.IsNullOrEmpty(IconFilePath))
					UpdateImageFromFile();
			}
			else
			if (propertyName == "IconFilePath")
			{
				if (string.IsNullOrEmpty(IconFilePath) && string.IsNullOrEmpty(SvgString))
				{
					image = "";
				}
				else
				if (!string.IsNullOrEmpty(IconFilePath))
					UpdateImageFromFile();
				else
					UpdateImageFromString(SvgString);
			}
			else
			if (propertyName == "SvgString")
			{
				if (string.IsNullOrEmpty(IconFilePath) && string.IsNullOrEmpty(SvgString))
				{
					image = "";
				}
				else
				if (!string.IsNullOrEmpty(SvgString))
					UpdateImageFromString(SvgString);
				else
					UpdateImageFromFile();
			}
			else
			if (propertyName == nameFontAwesomePrimaryColor ||
				propertyName == nameFontAwesomeSecondaryColor)
			{
				if (!string.IsNullOrEmpty(SvgString))
				{
					UpdateImageFromString(SvgString);
				}
				else
				{
					UpdateImageFromFile();
				}

			}

		}

		protected void UpdateImageFromString(string source)
		{
			if (string.IsNullOrEmpty(source))
			{
				image = "";
				return;
			}

			//replace anything
			if (FontAwesomePrimaryColor != Colors.Transparent)
			{
				source = source.Replace("class=\"fa-primary\"", $"fill=\"{FontAwesomePrimaryColor.ToHex()}\"");
			}
			if (FontAwesomeSecondaryColor != Colors.Transparent)
			{
				source = source.Replace("class=\"fa-secondary\"", $"fill=\"{FontAwesomeSecondaryColor.ToHex()}\"");
			}

			image = source;

			_canvasView?.InvalidateSurface();
		}

		protected void UpdateImageFromFile()
		{
			SvgString = "";

			if (string.IsNullOrEmpty(IconFilePath))
			{
				image = "";
				return;
			}


			{

				Assembly assembly;
				if (this.UseAssembly != null)
				{
					assembly = UseAssembly.GetType().Assembly;
				}
				else
				{
					assembly = this.GetType().Assembly;
				}

				_part1 = assembly.GetName().Name + $".Images.";
				var fullname = _part1 + IconFilePath;

				using (Stream file = assembly.GetManifestResourceStream(fullname))
				{
					if (file == null)
					{
						image = "";
						return;
					}

					var content = "";
					using (var reader = new System.IO.StreamReader(file))
					{
						content = reader.ReadToEnd();

						UpdateImageFromString(content);
					}
				}

				_canvasView?.InvalidateSurface();
			}

		}









		//-------------------------------------------------------------
		// SvgString
		//-------------------------------------------------------------
		private const string nameSvgString = "SvgString";
		public static readonly BindableProperty SvgStringProperty = BindableProperty.Create(nameSvgString, typeof(string), typeof(SvgIcon),
			string.Empty); //, BindingMode.TwoWay
		public string SvgString
		{
			get { return (string)GetValue(SvgStringProperty); }
			set { SetValue(SvgStringProperty, value); }
		}


		//-------------------------------------------------------------
		// HasContent
		//-------------------------------------------------------------
		private const string nameHasContent = "HasContent";
		public static readonly BindableProperty HasContentProperty = BindableProperty.Create(nameHasContent, typeof(bool), typeof(SvgIcon), false, BindingMode.OneWayToSource); //, BindingMode.TwoWay
		/// <summary>
		/// Readonly, has an icon to show
		/// </summary>
		public bool HasContent
		{
			get { return (bool)GetValue(HasContentProperty); }
			set { SetValue(HasContentProperty, value); }
		}




		public static readonly BindableProperty IconFilePathProperty = BindableProperty.Create(
			nameof(IconFilePath),
			typeof(string),
			typeof(SvgIcon),
			default(string));

		private string _part1;

		public string IconFilePath
		{
			get => (string)GetValue(IconFilePathProperty);
			set => SetValue(IconFilePathProperty, value);
		}

		private static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)
		{
			SvgIcon svgIcon = bindable as SvgIcon;
			svgIcon?._canvasView.InvalidateSurface();
		}


	}

}
