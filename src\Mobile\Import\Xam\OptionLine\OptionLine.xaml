﻿<?xml version="1.0" encoding="UTF-8" ?>

<gestures:LegacyGesturesStackLayout
    x:Class="AppoMobi.Xam.OptionLine"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Margin="20,0,20,0"
    Down="CGrid_OnDown"
    HorizontalOptions="Fill"
    Spacing="0"
    Tapped="OnTapped"
    Up="CGrid_OnUp">


    <Grid
        Margin="0,16,0,16"
        ColumnSpacing="16"
        HorizontalOptions="FillAndExpand">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <Label
            x:Name="cAction"
            Grid.Column="0"
            Margin="1,0,0,0"
            FontSize="14"
            LineBreakMode="WordWrap"
            TextColor="{x:Static xam:TextColors.GreyDark}"
            VerticalOptions="Center" />

        <StackLayout
            x:Name="cSelected"
            Grid.Column="1"
            HorizontalOptions="End"
            Orientation="Horizontal"
            Spacing="16"
            VerticalOptions="Center">

            <Label
                x:Name="cSelectedDesc"
                FontSize="13"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                LineBreakMode="WordWrap"
                TextColor="{x:Static xam:TextColors.Selected}"
                VerticalOptions="Center" />

            <!--  arrow Text="⇨"  -->
            <xam:FontIconLabel
                x:Name="cIndicator"
                Margin="0,0,1,0"
                FontSize="15"
                HorizontalOptions="End"
                IconName="fa_chevron_right"
                TextColor="{x:Static xam:TextColors.BtnArrow}"
                VerticalOptions="Center" />


        </StackLayout>

        <Switch
            x:Name="cBoolSelector"
            Grid.Column="1"
            HorizontalOptions="End"
            InputTransparent="True"
            IsVisible="False"
            VerticalOptions="Center"
            WidthRequest="-1" />

    </Grid>


    <BoxView
        x:Name="BottomLine"
        BackgroundColor="LightGray"
        HeightRequest="0.75"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="End" />


</gestures:LegacyGesturesStackLayout>