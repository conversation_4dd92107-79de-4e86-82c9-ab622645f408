﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

using AppoMobi.Touch;

namespace AppoMobi.Xam
{


    
    public class NiftySlider : AppoMobi.Touch.LegacyGesturesContentView
    
    {


        public void Dispose()
        {
            MainScroll.Scrolled -= OnScrolled;
            this.Down -= OnDown;
            this.Up -= OnUp;
            this.Panned -= OnPanned;
            this.Swiped -= OnSwiped;
            this.Tapped -= OnTapped;
            this.LongPressed -= OnLongPressed;
            this.Unfocused -= OnUnfocused;
            this.Tapping -= OnTapping;

            ControlDataStack?.Dispose();
        }

		public bool IsPressed { get; set; } = false;
		public bool IsPanned { get; set; } = false;

        private SliderScroll MainScroll { get; set; } = new SliderScroll();
        private Microsoft.Maui.Controls.StackLayout Stack { get; set; } = new Microsoft.Maui.Controls.StackLayout();
        public NiftyDataStack ControlDataStack { get; set; } = new NiftyDataStack();

	    public IList<IView> Views { get; private set; } = null;

        public bool Clone { get; set; } = false;
        public NiftySlider MyClone { get; set; }

        public EventHandler OnSliderReady { get; set; }

        private bool was_touched { get; set; }

        
        public void ListChanged()
        
        {
            
        }

        
        public NiftySlider()
	    
        {


            MainScroll.Scrolled += OnScrolled;
            
            if (DeviceInfo.Current.Platform != DevicePlatform.Android)
            { 
                //the UP event is firing on Android instantly, so not working
              //  Stack.Down += OnDown;
                //Stack.Up += OnUp;
            }


            this.Down += OnDown;
            this.Up += OnUp;
            this.Panned += OnPanned;
            this.Swiped += OnSwiped;
            this.Tapped += OnTapped;
            this.LongPressed += OnLongPressed;
            this.Unfocused += OnUnfocused;
            this.Tapping += OnTapping;

            ControlDataStack.Spacing = 0;
            Views = ControlDataStack.Views;

            ControlDataStack.Orientation = StackOrientation.Horizontal; //!!

	        ControlDataStack.HorizontalOptions = LayoutOptions.FillAndExpand;
	        ControlDataStack.VerticalOptions = LayoutOptions.FillAndExpand;


            Stack.Orientation = StackOrientation.Horizontal;
            Stack.Spacing = 0;
	        Stack.HorizontalOptions = LayoutOptions.FillAndExpand;
	        Stack.VerticalOptions = LayoutOptions.FillAndExpand;
	        Stack.Children.Add(ControlDataStack);

            MainScroll.Orientation = ScrollOrientation.Horizontal; //!!!

            MainScroll.HorizontalOptions = LayoutOptions.FillAndExpand;
            MainScroll.VerticalOptions = LayoutOptions.FillAndExpand;
            MainScroll.Content = Stack;

            Content = MainScroll;
	    }

        //---------------------------------------------------------
        private async void OnTapping(object sender, Object e)
        //---------------------------------------------------------
        {
            key_down = false;
            was_touched = true;
            //Debug.WriteLine("**Slider TAPPING");
        }
        //---------------------------------------------------------
        private async void OnUnfocused(object sender, Object e)
            //---------------------------------------------------------
        {
            key_down = false;
            //Debug.WriteLine("**Slider UNFOCUSED");
        }
        //---------------------------------------------------------
        private async void OnLongPressed(object sender, LongPressEventArgs e)
        //---------------------------------------------------------
        {
            //was_panned = true;
            key_down = false;
            //Debug.WriteLine("**Slider LONGPRESSED");
        }
        //---------------------------------------------------------
        private async void OnSwiped(object sender, SwipeEventArgs e)
            //---------------------------------------------------------
        {
            was_touched = true;
            key_down = false;
            var args = new ScrolledEventArgs(MainScroll.ScrollX, MainScroll.ScrollY);
            //Debug.WriteLine("**Slider SWIPED");
            OnScrolled(MainScroll, args);
        }
        //---------------------------------------------------------
        private async void OnTapped(object sender, TapEventArgs e)
        //---------------------------------------------------------
        {
            was_touched = true;
            key_down = false;
            //Debug.WriteLine("**Slider TAPPED");
        }
        //private bool was_panned;

        //---------------------------------------------------------
        private async void OnPanned(object sender, PanEventArgs e)
            //---------------------------------------------------------
        {
            was_touched = true;

            key_down = false;
            //Debug.WriteLine("**Slider PANNED");
            //if (!IsScrolling)
            //    AlignCurrentSlide(true);
        }
        private bool key_up_adjust { get; set; }
        
        private bool _OnUp { get; set; } = false;
        private void OnUp(object sender, DownUpEventArgs e)
        
        {
            key_down = false;
            was_touched = true;

            if (_OnUp) return;
            _OnUp = true;            
            //Debug.WriteLine("**Slider Key_UP had pos " + Position.ToString());

            //if (!IsScrolling)
            //    AlignCurrentSlide(true);

            key_up_adjust = false;

            //let the autoslider work again
            Device.StartTimer(TimeSpan.FromSeconds(0.5), () =>
            {
                //slide_pause = false;
                _OnUp = false;
                return false;
            });


        }

        private bool key_down { get; set; }
        
        private void OnDown(object sender, DownUpEventArgs e)
        
        {
            ////Debug.WriteLine("**Slider Key_DOWN had pos " + Position.ToString());
            PauseSlider("OnDown");
            key_down = true;
            ////Debug.WriteLine("**Slider KEY_DOWN");
            IsAdjusting = false;
            was_touched = true;


        }
        
        protected override async void OnPropertyChanged([CallerMemberName]string propertyName = null)
	    
	    {
	        base.OnPropertyChanged(propertyName);

	        switch (propertyName)
	        {


	            case nameSlideShowTime:

	                var restart = slide_running | slide_tasker;

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        // Update the UI

                    });

                    await StopAutoSlider();
	                await Task.Delay(1500);
	                if (restart) await LaunchAutoSlider();
	                break;

                case nameItemsSource:

                    last_width = -1;

                    ControlDataStack.ItemTemplate = ItemTemplate;
                    ControlDataStack.ItemsSource = ItemsSource;
	                if (ItemsSource?.Count > 0)
	                {
	                    Position = 0;
	                    OnSizeAllocated(Width, Height);
                        OnSliderReady?.Invoke(this, null);
                    }
                    else
	                {
	                    Position = -1;
	                }


	                break;


	            //property changed
	            case namePosition:
                    //try scroll to item
	                try
	                {
	                    if (Position>=0)//Pos != Position)//
                        {

	                        if (InternalCallPosition)
	                        {
	                            //Debug.WriteLine("** SLIDER IGNORING scrolling on InternalCallPosition to " + Position.ToString());
	                            InternalCallPosition = false;
	                            return;
	                        }

	                        //Debug.WriteLine("** SLIDER executed scrolling to " + Position.ToString());

                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                // Update the UI
                                try
                                {
                                    var view = (View)Views[Position];
                                    MainScroll.ScrollToAsync(view.X, 0, AnimateTransition);
                                }
                                catch (Exception e)
                                {
                                }
                            });
                        }
	                    //else
	                    //{
	                    //    //Debug.WriteLine("** SLIDER IGNORING2 scrolling to " + Position.ToString());
                     //   }
                    }
                    catch
	                {
	                }

	                

                    break;


                    //property changed
                    //case nameShowIndicators:
                    //    ControlName.ShowIndicators = ShowIndicators;
                    //    break;

                    ////property changed
                    //case nameAnimateTransition:
                    //    ControlName.AnimateTransition = AnimateTransition;
                    //    break;


            }

        }

        private int Pos { get; set; } = -1;

        
        // Position
        
        private const string namePosition = "Position";
        public static readonly BindableProperty PositionProperty = BindableProperty.Create(namePosition, typeof(int), typeof(NiftySlider), 0); //, BindingMode.TwoWay
        public int Position
        {
            get { return (int)GetValue(PositionProperty); }
            set { SetValue(PositionProperty, value); }
        }



        
        // Orientation
        
        private const string nameOrientation = "Orientation";
        public static readonly BindableProperty OrientationProperty = BindableProperty.Create(nameOrientation, typeof(ScrollOrientation), typeof(NiftySlider), ScrollOrientation.Horizontal); //, BindingMode.TwoWay
        public ScrollOrientation Orientation
        {
            get { return (ScrollOrientation)GetValue(OrientationProperty); }
            set { SetValue(OrientationProperty, value); }
        }	


        
        // ShowIndicators
        
        private const string nameShowIndicators = "ShowIndicators";
        public static readonly BindableProperty ShowIndicatorsProperty = BindableProperty.Create(nameShowIndicators, typeof(bool), typeof(NiftySlider), false); //, BindingMode.TwoWay
        public bool ShowIndicators
        {
            get { return (bool)GetValue(ShowIndicatorsProperty); }
            set { SetValue(ShowIndicatorsProperty, value); }
        }



        
        // AnimateTransition
        
        private const string nameAnimateTransition = "AnimateTransition";
        public static readonly BindableProperty AnimateTransitionProperty = BindableProperty.Create(nameAnimateTransition, typeof(bool), typeof(NiftySlider), true); //, BindingMode.TwoWay
        public bool AnimateTransition
        {
            get { return (bool)GetValue(AnimateTransitionProperty); }
            set { SetValue(AnimateTransitionProperty, value); }
        }	
    
	  

		
		// Tag
		
		private const string nameTag = "Tag";
		public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(NiftySlider), string.Empty);
		public string Tag
		{
			get { return (string)GetValue(TagProperty); }
			set { SetValue(TagProperty, value); }
		}


		
		// SelectedParams
		
		private const string nameSelectedParams = "SelectedParams";
		public static readonly BindableProperty SelectedParamsProperty = BindableProperty.Create(nameSelectedParams, typeof(string), typeof(NiftySlider), string.Empty);
		public string SelectedParams
		{
			get { return (string)GetValue(SelectedParamsProperty); }
			set { SetValue(SelectedParamsProperty, value); }
		}


        
        // SlideShowTime
        
        private const string nameSlideShowTime = "SlideShowTime";
        public static readonly BindableProperty SlideShowTimeProperty = BindableProperty.Create(nameSlideShowTime, typeof(int), typeof(NiftySlider), 5000); //, BindingMode.TwoWay
        public int SlideShowTime
        {
            get { return (int)GetValue(SlideShowTimeProperty); }
            set { SetValue(SlideShowTimeProperty, value); }
        }	

    
        
        // Sticky
        
        private const string nameSticky = "Sticky";
        public static readonly BindableProperty StickyProperty = BindableProperty.Create(nameSticky, typeof(bool), typeof(NiftySlider), true); //, BindingMode.TwoWay
        public bool Sticky
        {
            get { return (bool)GetValue(StickyProperty); }
            set { SetValue(StickyProperty, value); }
        }


        
        // IsScrolling
        
        private const string nameIsScrolling = "IsScrolling";
        public static readonly BindableProperty IsScrollingProperty = BindableProperty.Create(nameIsScrolling, typeof(bool), typeof(NiftySlider), false); //, BindingMode.TwoWay
        public bool IsScrolling
        {
            get { return (bool)GetValue(IsScrollingProperty); }
            private set { SetValue(IsScrollingProperty, value); }
        }	

       ////property changed
       //case nameIsScrolling:
       //             ControlName.IsScrolling = IsScrolling;
       //             break;		


		
		public DataTemplate ItemTemplate
		
		{
			get;
			set;
		}

        public bool IsAdjusting { get; private set; }

        
		// ItemsSource
		
		private const string nameItemsSource = "ItemsSource";
		public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameItemsSource, typeof(IList), typeof(NiftySlider), null, BindingMode.TwoWay);
		public IList ItemsSource
		{
			get { return (IList)GetValue(ItemsSourceProperty); }
			set { SetValue(ItemsSourceProperty, value); }
		}

        private double last_scroll { get; set; } = -1;

        private bool lock_onscrolled { get; set; }
        private bool scrolling_fired { get; set; }

        private bool scrolling_tracked { get; set; }
        
        private void OnScrolled(object sender, ScrolledEventArgs e)
        
        {
            if (lock_onscrolled) return;
            lock_onscrolled = true;

            //PauseSlider("OnScrolled");
            scrolling_fired = true;
            last_scroll = e.ScrollX;
            IsScrolling = true;

            ControlDataStack.ProcessSlidesVisibility(e.ScrollX, MainScroll.Width, false);

            if (!scrolling_tracked)
            {
                scrolling_tracked = true;
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                   // var now = e.ScrollX;
                   // if (now == last_scroll) IsScrolling = false;


                    //wait for scrolling to stop
                    if (scrolling_fired || IsAdjusting)
                    {
                        scrolling_fired = false;

                        if (!IsScrolling)
                        {
                            scrolling_tracked = false;
                            return false; //do not repeat
                        }

                        return true; //repeat
                    }

                    //Debug.WriteLine("**Slider scrolling STOPPED");

                    int pos = 0;
                    double step = 0;

                    //update my position
                    if (SliderCellSizing == CellSizing.FillParent)
                    {
                        step = slider_width * 0.5;
                        pos = (int) ((last_scroll + step) / slider_width);
                        if (pos > Views.Count - 1) pos = Views.Count - 1;
                        else if (pos < 0) pos = 0;
                    }
                    else
                    {
                        //todo
                        var myIndex = 0;
                        double myIndexValue=0.0d;
                        var slideNum = 0;
                        foreach (SliderCell slide in Views)
                        {
                            var compare = slide.AmountAppeared;
                            if (compare > 0.5) compare = 1;
                            if (compare > myIndexValue)
                            {
                                myIndex = slideNum;
                                myIndexValue = compare;
                            }
                            slideNum++;
                        }
                       pos = myIndex;
                    }

                    Pos = pos;

                    InternalCallPosition = true;
                    Position = Pos;
                    
                   // //Debug.WriteLine("**Slider scrolling stopped at Pos " + Position.ToString() + " " + last_scroll.ToString());                    
                    IsScrolling = false;


                    if (was_touched)
                    {
                        ////Debug.WriteLine("**Slider scrolling STOPPED at " + Position.ToString());
                        if (Sticky && !IsAdjusting)
                        {
                            if (!key_down)
                            {
                                was_touched = false;
                             //   //Debug.WriteLine("**Manual sliding stopped, adjusting..");
                                AlignCurrentSlide(true);
                            }
                            else
                            {
                                //========================================
                                Device.StartTimer(TimeSpan.FromSeconds(1.5), () =>
                                {
                                    if (IsScrolling) return false;
                                    if (!key_down)
                                    {
                                        //Debug.WriteLine("**KEY UP, adjusting..");
                                        was_touched = false;
                                        AlignCurrentSlide(true);
                                        return false; //do not repeat
                                    }
                                    return true; //watch
                                });
                                //========================================
                            }

                        }
                        else
                        {
                            ResumeSlider();
                        }
                    }
                    scrolling_tracked = false;
                    return false;
                });
            }

            //was_panned = false;

            lock_onscrolled = false;
        }

        
        private void PauseSlider(string cause="")
        
        {
            //Debug.WriteLine("** SLIDER PAUSED "+cause);
            slide_pause = true;
        }
        
        private void ResumeSlider(string cause = "")
        
        {
            if (slide_stop)
            {
                //Debug.WriteLine("** CANNOT RESUME SLIDER PROHIBITED " + cause);
                return;
            }
            //Debug.WriteLine("** SLIDER UNPAUSED " + cause);
            slide_pause = false;

            if (!slide_running) LaunchAutoSlider();
        }

        public bool InternalCallPosition { get; set; }

        private bool slide_stop { get; set; } = false;
        private bool slide_forward { get; set; } = true;
        private bool slide_pause { get; set; } = true;
        private bool slide_dont { get; set; } = false;
        private int slide_last { get; set; } = -1;
        private bool slide_running { get; set; } = false;
        private bool slide_tasker { get; set; } = false;
        private bool timer_working { get; set; } = false;

        private string id { get; set; } = Guid.NewGuid().ToString();

        
        public void AlignCurrentSlide(bool animate)
        
        {
            if (IsAdjusting || Position < 0 || Position > Views?.Count-1) return;
            if (slide_stop && animate) return;

            IsAdjusting = true;

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                // Update the UI
                    try
                    {   
                        var view = (View)Views[Position];
                        await MainScroll.ScrollToAsync(view.X, 0, animate);
                        IsAdjusting = false;
                        //Debug.WriteLine("**Slider ALIGNED to " + Position.ToString()+" to "+ Views[Position].X.ToString());
                        ResumeSlider();
                    }
                    catch
                    {
                        //Debug.WriteLine("**Slider adjusting FAILED " + Position.ToString() + " to " + Views[Position].X.ToString());
                    }
                });


        }

        public async Task Relaunch()
        {
            if (slide_running || !CanAutoSlide || slide_tasker)
            {
                return;
            }
            await LaunchAutoSlider();
        }

        
        public async Task LaunchAutoSlider()
        
        {
            slide_stop = false;
            slide_dont = false;
            slide_pause = false;

            force_update_slides_sizes = true;
            OnSizeAllocated(Width, Height);

            if (slide_running || !CanAutoSlide || slide_tasker)
            {
                return;
            }

            //Debug.WriteLine("** Slider LAUNCH Ordered.");

            //  ResumeSlider();

            //отложенный запуск, ждем когда зарузятся

            if ((ItemsSource==null || ItemsSource.Count < 1) && !slide_tasker)
            {
      

                //Debug.WriteLine("** Slider Launch Delayed.");

                slide_tasker = true;
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                  //  if (slide_stop) return false; //stop this shit
                        if (ItemsSource?.Count > 0)
                        {
                            slide_tasker= false;
                            LaunchAutoSlider();
                            return false;
                        }
                    //repeat later
                    return true;
                });

                return;
            }

            //Debug.WriteLine("** Slider LAUNCHED !!!");

            slide_running = true;
            slide_last = -1;
            slide_forward = true;
            //=================================================================
            // AUTO SLIDER ROTATION is here
            //=================================================================
            Device.StartTimer(TimeSpan.FromMilliseconds(SlideShowTime), () => //
            //=================================================================
            {
                if (timer_working) return true;
                timer_working = true;

                slide_running = true;
                int pos = 0;
                int oldpos = Position;
                try
                {
                    if (ItemsSource?.Count < 1 || slide_stop)
                    {
                        slide_running = false;

                        timer_working = false;
                        return false;
                    }
                    if (slide_pause)
                        {
                        timer_working = false;
                        return true;                            
                        }
                    if (slide_forward)
                    {
                        if (oldpos < ItemsSource?.Count - 1)
                        {
                            pos = oldpos + 1;
                        }
                        else
                        {
                            slide_forward = false;
                            pos = oldpos - 1;
                        }
                    }
                    else
                    {
                        if (oldpos > 0)
                        {
                            pos = oldpos - 1;
                        }
                        else
                        {
                            slide_forward = true;
                            pos = oldpos + 1;
                        }

                    }
                    //Debug.WriteLine("**Slider "+id+" ORDERED SCROLL to " + pos.ToString());
                    InternalCallPosition = false;
                    Position = pos;
                }
                catch
                {
                    slide_running = false;
                    timer_working = false;
                    return false;
                }
                timer_working = false;
                return true;
            });



        }
        
        public async Task StopAutoSlider()
        
        {
            //Debug.WriteLine("** Slider Stopped.");
            slide_stop = true;
            //while (slide_running)
            //{
            //    await Task.Delay(100);
            //    slide_stop = true;
            //}
        }



        
        // IsRunning
        
        private const string nameIsRunning = "IsRunning";
        public static readonly BindableProperty IsRunningProperty = BindableProperty.Create(nameIsRunning, typeof(bool), typeof(NiftySlider), false); //, BindingMode.TwoWay
        public bool IsRunning
        {
            get
            {
                return (bool)slide_running;
            }
        }	

        
        // CanAutoSlide
        
        private const string nameCanAutoSlide = "CanAutoSlide";
        public static readonly BindableProperty CanAutoSlideProperty = BindableProperty.Create(nameCanAutoSlide, typeof(bool), typeof(NiftySlider), true); //, BindingMode.TwoWay
        public bool CanAutoSlide
        {
            get { return (bool)GetValue(CanAutoSlideProperty); }
            set { SetValue(CanAutoSlideProperty, value); }
        }	


        


        
        // SliderCellWidth
        
        private const string nameSliderCellWidth = "SliderCellWidth";
        public static readonly BindableProperty SliderCellWidthProperty = BindableProperty.Create(nameSliderCellWidth, typeof(double), typeof(NiftySlider), -1.0d); //, BindingMode.TwoWay
        public double SliderCellWidth
        {
            get { return (double)GetValue(SliderCellWidthProperty); }
            set { SetValue(SliderCellWidthProperty, value); }
        }

        ////property changed
        //case nameSliderCellWidth:
        //             ControlName.SliderCellWidth = SliderCellWidth;
        //             break;		


        
        // SliderCellHeight
        
        private const string nameSliderCellHeight = "SliderCellHeight";
        public static readonly BindableProperty SliderCellHeightProperty = BindableProperty.Create(nameSliderCellHeight, typeof(double), typeof(NiftySlider), -1.0d); //, BindingMode.TwoWay
        public double SliderCellHeight
        {
            get { return (double)GetValue(SliderCellHeightProperty); }
            set { SetValue(SliderCellHeightProperty, value); }
        }	

       ////property changed
       //case nameSliderCellHeight:
       //             ControlName.SliderCellHeight = SliderCellHeight;
       //             break;		

       
       public enum CellSizing
        
       {
           FillParent,
           FitParent,
           Explicit
       }


        
        // SliderCellSizing
        
        private const string nameSliderCellSizing = "SliderCellSizing";
        public static readonly BindableProperty SliderCellSizingProperty = BindableProperty.Create(nameSliderCellSizing, typeof(CellSizing), typeof(NiftySlider), CellSizing.FillParent); //, BindingMode.TwoWay
        public CellSizing SliderCellSizing
        {
            get { return (CellSizing)GetValue(SliderCellSizingProperty); }
            set { SetValue(SliderCellSizingProperty, value); }
        }	

       ////property changed
       //case nameSliderCellSizing:
       //             ControlName.SliderCellSizing = SliderCellSizing;
       //             break;		




        private double last_width { get; set; } = -1;
        private double slider_width { get; set; } = -1;
        private Object thisLock = new Object();
        private bool cells_resized { get; set; }
        protected bool force_update_slides_sizes { get; set; }

        
        protected override void OnSizeAllocated(double width, double height) 
        
        {
            if (slide_stop)
            {
                var stop = true;
                //base.OnSizeAllocated(width, height);
                //return;
            }

            //Debug.WriteLine("**Slider OnSizeAllocated");

            if (width > 0 && Children.Count > 0 && ControlDataStack.Views.Count > 0 && SliderCellSizing==CellSizing.FillParent)
                {
                    

                    if (width != last_width || force_update_slides_sizes)
                    {
                        force_update_slides_sizes = false;
                    //resize slides to fill the slider width
                        try
                        {
                            foreach (SliderCell child in ControlDataStack.Views)
                            {
                                child.WidthRequest = width;
                                //Debug.WriteLine("**Slider adapted cell width to " + width);
                        }
                    }
                    catch{};

                        last_width = slider_width = width;
                        //Debug.WriteLine("**Slider changed cells to " + slider_width.ToString());
                        cells_resized = true;
                        // now wait for this event again to proceed to resizing titles on next one
                    }
                    else
                    {
                        if (cells_resized)
                        {
                                //slides resized
                                foreach (SliderCell child in ControlDataStack.Views)
                                {
                                    //ajust SliderTitle too
                                    foreach (var subchild in child.Children)
                                    {
                                        if (subchild.GetType() == typeof(SliderTitle))
                                        {
                                            var slide = (SliderTitle) subchild;
                                            if (slide.Width > 0)
                                            {
                                                if (slide.NativeWidth < 0)
                                                    slide.NativeWidth = slide.Width;

                                                if (slide.NativeWidth < slider_width / 100 * 40)
                                                {
                                                    MainThread.BeginInvokeOnMainThread(() =>
                                                    {
                                                        // Update the UI
                                                        slide.WidthRequest = slider_width / 100 * 40;
                                                    });
                                                }
                                                else
                                                {
                                                    MainThread.BeginInvokeOnMainThread(() =>
                                                    {
                                                        // Update the UI
                                                        slide.WidthRequest = -1;
                                                    });
                                                }
                                            }


                                        }                                       
                                    }
                                }

                                //Debug.WriteLine("**Slider adapted titles for " + slider_width.ToString());
                                AlignCurrentSlide(false);                            
                           
                        }                       
                    }

                }

                base.OnSizeAllocated(width, height);
            
        }

        /*

        protected override bool ShouldInvalidateOnChildAdded(View child)
        {
            return false; // stop pestering me
        }

        protected override bool ShouldInvalidateOnChildRemoved(View child)
        {
            return false; // go away and leave me alone
        }

        protected override void OnChildMeasureInvalidated()
        {
            // I'm ignoring you.  You'll take whatever size I want to give
            // you.  And you'll like it.
        }
        */

        //***********************************************************************
        public class SliderScroll : SolidScrollView
        //*******************************************************************
        {



        }

    }

    //***********************************************************************
    public class SliderTitle : Microsoft.Maui.Controls.ContentView
    //***********************************************************************
    {


        
        // Tag
        
        private const string nameTag = "Tag";
        public static readonly BindableProperty TagProperty = BindableProperty.Create(nameTag, typeof(string), typeof(SliderTitle), string.Empty); //, BindingMode.TwoWay
        public string Tag
        {
            get { return (string)GetValue(TagProperty); }
            set { SetValue(TagProperty, value); }
        }


        
        // NativeWidth
        
        private const string nameNativeWidth = "NativeWidth";
        public static readonly BindableProperty NativeWidthProperty = BindableProperty.Create(nameNativeWidth, typeof(double), typeof(SliderTitle), -1.0d); //, BindingMode.TwoWay
        public double NativeWidth
        {
            get { return (double)GetValue(NativeWidthProperty); }
            set { SetValue(NativeWidthProperty, value); }
        }	

       ////property changed
       //case nameNativeWidth:
       //             ControlName.NativeWidth = NativeWidth;
       //             break;		  

    }

    //***********************************************************************
    public class SliderShadow : Microsoft.Maui.Controls.ContentView
    //***********************************************************************
    {



    }

    //***********************************************************************
    public class SliderCell : AppoMobi.Touch.LegacyGesturesGrid
    //***********************************************************************
    {

        
        public virtual void Dispose()
        
        {



        }

        public SliderCell()
        {
            Children.Add(new Microsoft.Maui.Controls.ContentView());
            base.InvalidateMeasure();
            Children.Clear();

            ColumnSpacing = 0;
            RowSpacing = 0;
            VerticalOptions = LayoutOptions.FillAndExpand;
            HorizontalOptions = LayoutOptions.Start;
        }

        public string Tag { get; set; }
        public int Index { get; set; }

        public double AmountAppeared { get; set; }

        //-------------------------------------------------------------------
        protected override void OnSizeAllocated(double width, double height)
        //-------------------------------------------------------------------
        {
            base.OnSizeAllocated(width, height);
        //    Debug.WriteLine("[SliderCell] width changed to " + width);
        }

        
        public virtual void OnAppearing()
        
        {
            var container = RealParent as NiftySlider;
            if (container != null)
            container.ControlDataStack.SendCellAppearing(this);
        }
        
        public virtual void OnDisapearing()
        
        {
            var container = RealParent as NiftySlider;
            if (container != null)
            container.ControlDataStack.SendCellDisappearing(this);
        }
        private bool _Appeared;
        public bool Appeared
        {
            get { return _Appeared; }
            set
            {
                if (_Appeared != value)
                {
                    _Appeared = value;
                    OnPropertyChanging();
                    if (value)
                        OnAppearing();
                    else
                        OnDisapearing();
                    OnPropertyChanged();
                }
            }
        }
    }

      

    }



